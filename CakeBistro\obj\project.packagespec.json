﻿"restore":{"projectUniqueName":"C:\\Users\\<USER>\\Desktop\\CakeBistro\\CakeBistro\\CakeBistro.csproj","projectName":"CakeBistro","projectPath":"C:\\Users\\<USER>\\Desktop\\CakeBistro\\CakeBistro\\CakeBistro.csproj","outputPath":"C:\\Users\\<USER>\\Desktop\\CakeBistro\\CakeBistro\\obj\\","projectStyle":"PackageReference","UsingMicrosoftNETSdk":false,"fallbackFolders":["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"],"originalTargetFrameworks":["net8.0-windows10.0.19041.0"],"sources":{"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\":{},"C:\\Program Files\\dotnet\\library-packs":{},"https://api.nuget.org/v3/index.json":{}},"frameworks":{"net8.0-windows10.0.19041":{"targetAlias":"net8.0-windows10.0.19041.0","projectReferences":{"C:\\Users\\<USER>\\Desktop\\CakeBistro\\CakeBistro.Core\\CakeBistro.Core.csproj":{"projectPath":"C:\\Users\\<USER>\\Desktop\\CakeBistro\\CakeBistro.Core\\CakeBistro.Core.csproj"}}}},"warningProperties":{"warnAsError":["NU1605"]},"restoreAuditProperties":{"enableAudit":"true","auditLevel":"low","auditMode":"direct"}}"frameworks":{"net8.0-windows10.0.19041":{"targetAlias":"net8.0-windows10.0.19041.0","dependencies":{"BCrypt.Net-Next":{"target":"Package","version":"[4.0.3, )"},"CommunityToolkit.Maui":{"target":"Package","version":"[7.0.1, )"},"CommunityToolkit.Mvvm":{"target":"Package","version":"[8.2.2, )"},"EPPlus":{"target":"Package","version":"[8.0.7, )"},"Microcharts.Maui":{"target":"Package","version":"[1.0.1, )"},"Microsoft.EntityFrameworkCore.Sqlite":{"target":"Package","version":"[8.0.1, )"},"Microsoft.EntityFrameworkCore.Tools":{"target":"Package","version":"[8.0.1, )"},"Microsoft.Extensions.Configuration.Json":{"target":"Package","version":"[8.0.1, )"},"Microsoft.Extensions.DependencyInjection":{"target":"Package","version":"[8.0.1, )"},"Microsoft.Extensions.Logging.Debug":{"target":"Package","version":"[8.0.1, )"},"Microsoft.Maui.Controls":{"target":"Package","version":"[8.0.100, )"},"Microsoft.Maui.Controls.Compatibility":{"target":"Package","version":"[8.0.100, )"},"System.ComponentModel.Annotations":{"target":"Package","version":"[5.0.0, )"},"iTextSharp.LGPLv2.Core":{"target":"Package","version":"[3.7.4, )"}},"imports":["net461","net462","net47","net471","net472","net48","net481"],"assetTargetFallback":true,"warn":true,"downloadDependencies":[{"name":"Microsoft.Windows.SDK.NET.Ref","version":"[10.0.19041.57, 10.0.19041.57]"}],"frameworkReferences":{"Microsoft.NETCore.App":{"privateAssets":"all"},"Microsoft.Windows.SDK.NET.Ref.Windows":{"privateAssets":"all"}},"runtimeIdentifierGraphPath":"C:\\Program Files\\dotnet\\sdk\\9.0.302\\RuntimeIdentifierGraph.json"}}"runtimes":{"win10-x64":{"#import":[]}}