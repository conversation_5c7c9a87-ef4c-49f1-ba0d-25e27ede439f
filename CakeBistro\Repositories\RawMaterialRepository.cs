using Microsoft.EntityFrameworkCore;
using System.Linq.Expressions;
using System.Threading.Tasks;
using CakeBistro.Core.Models;
using CakeBistro.Context;

namespace CakeBistro.Repositories
{
    public class RawMaterialRepository : BaseRepository<CakeBistro.Core.Models.RawMaterial>, IRawMaterialRepository
    {
        public RawMaterialRepository(CakeBistroContext context) : base(context)
        {
        }

        public async Task<IEnumerable<CakeBistro.Core.Models.RawMaterial>> GetByCategoryAsync(string category)
        {
            return await _context.RawMaterials
                .Where(r => r.Category == category)
                .ToListAsync();
        }

        public async Task<CakeBistro.Core.Models.RawMaterial> GetByNameAsync(string name)
        {
            return await _context.RawMaterials
                .FirstOrDefaultAsync(r => r.Name == name);
        }
    }
}
