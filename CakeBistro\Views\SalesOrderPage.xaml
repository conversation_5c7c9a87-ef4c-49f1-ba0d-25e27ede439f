<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="CakeBistro.Views.SalesOrderPage"
             Title="Sales Order Management">
    <ContentPage.Content>
        <ScrollView>
            <VerticalStackLayout Spacing="25" Padding="30">
                <!-- Customer Selection -->
                <Label Text="Customer" FontSize="24" HorizontalOptions="Start"/>
                
                <HorizontalStackLayout>
                    <Picker ItemsSource="{Binding Customers}"
                             ItemDisplayBinding="{Binding Name}"
                             SelectedItem="{Binding SelectedCustomer, Mode=TwoWay}"
                             WidthRequest="200"/>
                    <Button Text="New Customer" Clicked="OnAddCustomerClicked"/>
                </HorizontalStackLayout>
                
                <Grid ColumnDefinitions="*,*" RowDefinitions="Auto,Auto,Auto,Auto,Auto">
                    <Label Grid.Row="0" Grid.Column="0" Text="Customer Code:"/>
                    <Label Grid.Row="0" Grid.Column="1" Text="{Binding SelectedCustomer.AccountCode}"/>
                    
                    <Label Grid.Row="1" Grid.Column="0" Text="Contact Person:"/>
                    <Label Grid.Row="1" Grid.Column="1" Text="{Binding SelectedCustomer.ContactPerson}"/>
                    
                    <Label Grid.Row="2" Grid.Column="0" Text="Phone Number:"/>
                    <Label Grid.Row="2" Grid.Column="1" Text="{Binding SelectedCustomer.PhoneNumber}"/>
                    
                    <Label Grid.Row="3" Grid.Column="0" Text="Email:"/>
                    <Label Grid.Row="3" Grid.Column="1" Text="{Binding SelectedCustomer.Email}"/>
                    
                    <Label Grid.Row="4" Grid.Column="0" Text="Credit Limit:"/>
                    <Label Grid.Row="4" Grid.Column="1" Text="{Binding SelectedCustomer.CreditLimit, StringFormat='{0:C2}'}"/>
                </Grid>
                
                <!-- Order Details -->
                <Label Text="Order Details" FontSize="24" HorizontalOptions="Start"/>
                
                <Grid ColumnDefinitions="*,*" RowDefinitions="Auto,Auto,Auto,Auto,Auto,Auto,Auto">
                    <Label Grid.Row="0" Grid.Column="0" Text="Order Number:"/>
                    <Label Grid.Row="0" Grid.Column="1" Text="{Binding OrderNumber}"/>
                    
                    <Label Grid.Row="1" Grid.Column="0" Text="Order Date:"/>
                    <DatePicker Grid.Row="1" Grid.Column="1" Date="{Binding OrderDate, Mode=TwoWay}"/>
                    
                    <Label Grid.Row="2" Grid.Column="0" Text="Location:"/>
                    <Entry Grid.Row="2" Grid.Column="1" Text="{Binding Location, Mode=TwoWay}"/>
                    
                    <Label Grid.Row="3" Grid.Column="0" Text="Delivery Required:"/>
                    <Switch Grid.Row="3" Grid.Column="1" IsToggled="{Binding IsDelivery, Mode=TwoWay}"/>
                    
                    <Label Grid.Row="4" Grid.Column="0" Text="Status:"/>
                    <Label Grid.Row="4" Grid.Column="1" Text="{Binding SelectedSalesOrder.Status}" TextColor="{Binding SelectedSalesOrder.StatusColor}"/>
                    
                    <Label Grid.Row="5" Grid.Column="0" Text="Notes:"/>
                    <Editor Grid.Row="5" Grid.Column="1" Text="{Binding Notes, Mode=TwoWay}"/>
                    
                    <Button Grid.Row="6" Grid.Column="0" Text="Update Order" Clicked="OnUpdateSalesOrder" IsEnabled="{Binding SelectedSalesOrder, Converter={StaticResource NotNullToBooleanConverter}}"/>
                    <!-- Grid layout with two columns for organizing UI elements -->
                    <!-- Column 0: Contains left-side content -->
                    <!-- Column 1: Contains right-side content -->
                    <!-- Ensure proper Grid.Column attribute syntax: Grid.Column="0" or Grid.Column="1" -->
                    <!-- Verify all XML tags are properly closed with matching opening and closing brackets -->
                    <Button Grid.Row="6" Grid.Column="1" Text="Process Transaction" Clicked="OnProcessTransaction" IsEnabled="{Binding SelectedSalesOrder, Converter={StaticResource NotNullToBooleanConverter}}"/>
                </Grid>
                
                <!-- Order Items -->
                <Label Text="Order Items" FontSize="24" HorizontalOptions="Start"/>
                
                <HorizontalStackLayout>
                    <Picker ItemsSource="{Binding AvailableProducts}"
                             ItemDisplayBinding="{Binding Name}"
                             SelectedItem="{Binding SelectedProduct, Mode=TwoWay}"
                             WidthRequest="150"/>
                    <Entry Text="{Binding Quantity, Mode=TwoWay, StringFormat={}{0:F2}}" WidthRequest="70"/>
                    <Button Text="Add Item" Clicked="OnAddOrderItem"/>
                </HorizontalStackLayout>
                
                <CollectionView ItemsSource="{Binding OrderItems}"
                                  SelectedItem="{Binding SelectedOrderItem}">
                    <CollectionView.ItemsLayout>
                        <LinearItemsLayout Orientation="Vertical"/>
                    </CollectionView.ItemsLayout>
                    <CollectionView.ItemTemplate>
                        <DataTemplate>
                            <Grid ColumnDefinitions="*,*,*,*" RowDefinitions="Auto,Auto">
                                <Label Grid.Row="0" Grid.Column="0" Text="{Binding Product.Name}"/>
                                <Label Grid.Row="0" Grid.Column="1" Text="{Binding Quantity, StringFormat='{0:F2} units'}"/>
                                <Label Grid.Row="0" Grid.Column="2" Text="{Binding UnitPrice, StringFormat='{0:C2}'}"/>
                                <Label Grid.Row="0" Grid.Column="3" Text="{Binding TotalPrice, StringFormat='{0:C2}'}"/>
                                
                                <Label Grid.Row="1" Grid.Column="0" Text="{Binding Product.Category}"/>
                                <Label Grid.Row="1" Grid.Column="1" Text="{Binding Discount, StringFormat='Discount: {0:P2}'}"/>
                                <Label Grid.Row="1" Grid.Column="2" Text="{Binding Tax, StringFormat='Tax: {0:P2}'}"/>
                                <Label Grid.Row="1" Grid.Column="3" Text="{Binding ProfitMargin, StringFormat='Margin: {0:P2}'}"/>
                            </Grid>
                        </DataTemplate>
                    </CollectionView.ItemTemplate>
                </CollectionView>
                
                <Button Text="Remove Selected Item" Clicked="OnRemoveOrderItem" IsEnabled="{Binding SelectedOrderItem, Converter={StaticResource NotNullToBooleanConverter}}"/>
                
                <!-- Order Summary -->
                <Grid ColumnDefinitions="*,*" RowDefinitions="Auto,Auto,Auto,Auto,Auto">
                    <Label Grid.Row="0" Grid.Column="0" Text="Subtotal:" HorizontalTextAlignment="End"/>
                    <Label Grid.Row="0" Grid.Column="1" Text="{Binding TotalAmount, StringFormat='{0:C2}'}"/>
                    
                    <Label Grid.Row="1" Grid.Column="0" Text="Discount:" HorizontalTextAlignment="End"/>
                    <Entry Grid.Row="1" Grid.Column="1" Text="{Binding Discount, Mode=TwoWay, StringFormat={}{0:C2}}"/>
                    
                    <Label Grid.Row="2" Grid.Column="0" Text="Fuel Cost:" HorizontalTextAlignment="End"/>
                    <Entry Grid.Row="2" Grid.Column="1" Text="{Binding FuelCost, Mode=TwoWay, StringFormat={}{0:C2}}"/>
                    
                    <Label Grid.Row="3" Grid.Column="0" Text="Net Amount:" HorizontalTextAlignment="End"/>
                    <Label Grid.Row="3" Grid.Column="1" Text="{Binding NetAmount, StringFormat='{0:C2}'}"/>
                    
                    <Label Grid.Row="4" Grid.Column="0" Text="Payment Method:" HorizontalTextAlignment="End"/>
                    <Picker Grid.Row="4" Grid.Column="1" ItemsSource="{Binding PaymentMethods}" SelectedItem="{Binding SelectedPaymentMethod, Mode=TwoWay}"/>
                </Grid>
                
                <!-- Vehicle & Driver Assignment -->
                <Label Text="Vehicle &amp; Driver Assignment" FontSize="24" HorizontalOptions="Start"/>
                
                <Grid ColumnDefinitions="*,*" RowDefinitions="Auto,Auto,Auto">
                    <Label Grid.Row="0" Grid.Column="0" Text="Vehicle:"/>
                    <Picker Grid.Row="0" Grid.Column="1" ItemsSource="{Binding Vehicles}" ItemDisplayBinding="{Binding RegistrationNumber}" SelectedItem="{Binding SelectedVehicle, Mode=TwoWay}"/>
                    
                    <Label Grid.Row="1" Grid.Column="0" Text="Driver:"/>
                    <Picker Grid.Row="1" Grid.Column="1" ItemsSource="{Binding Drivers}" ItemDisplayBinding="{Binding Name}" SelectedItem="{Binding SelectedDriver, Mode=TwoWay}"/>
                    
                    <Label Grid.Row="2" Grid.Column="0" Text="Delivery Address:"/>
                    <Editor Grid.Row="2" Grid.Column="1" Text="{Binding DeliveryAddress, Mode=TwoWay}"/>
                </Grid>
                
                <!-- Sales History -->
                <Label Text="Customer Sales History" FontSize="24" HorizontalOptions="Start"/>
                
                <CollectionView ItemsSource="{Binding CustomerSalesHistory}"
                                  SelectionMode="None">
                    <CollectionView.ItemsLayout>
                        <LinearItemsLayout Orientation="Vertical"/>
                    </CollectionView.ItemsLayout>
                    <CollectionView.ItemTemplate>
                        <DataTemplate>
                            <Frame Padding="10" Margin="5">
                                <Grid ColumnDefinitions="*,*,*" RowDefinitions="Auto,Auto">
                                    <Label Grid.Row="0" Grid.Column="0" Text="{Binding OrderNumber}"/>
                                    <Label Grid.Row="0" Grid.Column="1" Text="{Binding OrderDate, StringFormat='Date: {0:yyyy-MM-dd}'}"/>
                                    <Label Grid.Row="0" Grid.Column="2" Text="{Binding TotalAmount, StringFormat='{0:C2}'}"/>
                                    
                                    <Label Grid.Row="1" Grid.Column="0" Text="{Binding Status}"/>
                                    <Label Grid.Row="1" Grid.Column="1" Text="{Binding PaymentStatus}"/>
                                    <Label Grid.Row="1" Grid.Column="2" Text="{Binding DeliveryStatus}"/>
                                </Grid>
                            </Frame>
                        </DataTemplate>
                    </CollectionView.ItemTemplate>
                </CollectionView>
                
                <!-- Sales Orders List -->
                <Label Text="Sales Orders" FontSize="24" HorizontalOptions="Start"/>
                
                <HorizontalStackLayout>
                    <DatePicker Date="{Binding StartDate, Mode=TwoWay}"/>
                    <DatePicker Date="{Binding EndDate, Mode=TwoWay}"/>
                    <Picker ItemsSource="{Binding OrderStatuses}" SelectedItem="{Binding SelectedStatus, Mode=TwoWay}" WidthRequest="120"/>
                    <Button Text="Filter" Clicked="OnFilterOrders"/>
                </HorizontalStackLayout>
                
                <CollectionView ItemsSource="{Binding SalesOrders}"
                                  SelectedItem="{Binding SelectedSalesOrder, Mode=TwoWay}"
                                  SelectionMode="Single">
                    <CollectionView.ItemsLayout>
                        <LinearItemsLayout Orientation="Vertical"/>
                    </CollectionView.ItemsLayout>
                    <CollectionView.ItemTemplate>
                        <DataTemplate>
                            <Frame Padding="10" Margin="5">
                                <Grid ColumnDefinitions="*,*,*,*" RowDefinitions="Auto,Auto">
                                    <Label Grid.Row="0" Grid.Column="0" Text="{Binding Id, StringFormat='Order #{0}'}"/>
                                    <Label Grid.Row="0" Grid.Column="1" Text="{Binding Customer.Name}"/>
                                    <Label Grid.Row="0" Grid.Column="2" Text="{Binding OrderDate, StringFormat='Date: {0:yyyy-MM-dd}'}"/>
                                    <Label Grid.Row="0" Grid.Column="3" Text="{Binding TotalAmount, StringFormat='{0:C2}'}"/>
                                    
                                    <Label Grid.Row="1" Grid.Column="0" Text="{Binding Status}"/>
                                    <Label Grid.Row="1" Grid.Column="1" Text="{Binding PaymentStatus}"/>
                                    <Label Grid.Row="1" Grid.Column="2" Text="{Binding DeliveryStatus}"/>
                                    <Label Grid.Row="1" Grid.Column="3" Text="{Binding DeliveryDate, StringFormat='Deliver: {0:yyyy-MM-dd}'}"/>
                                </Grid>
                            </Frame>
                        </DataTemplate>
                    </CollectionView.ItemTemplate>
                </CollectionView>
                
                <!-- Action Buttons -->
                <HorizontalStackLayout Spacing="10" HorizontalOptions="Center">
                    <Button Text="New Order" Command="{Binding AddSalesOrderCommand}" BackgroundColor="Green" TextColor="White" />
                    <Button Text="Edit Order" Command="{Binding EditSalesOrderCommand}" CommandParameter="{Binding SelectedSalesOrder}" IsEnabled="{Binding SelectedSalesOrder, Converter={StaticResource NotNullToBooleanConverter}}" BackgroundColor="Blue" TextColor="White" />
                    <Button Text="Delete Order" IsEnabled="{Binding SelectedSalesOrder, Converter={StaticResource NotNullToBooleanConverter}}"/>
                    <Button Text="Generate Report" Clicked="OnGenerateReport"/>
                    <Button Text="Refresh" Clicked="OnRefreshData"/>
                </HorizontalStackLayout>
                
                <!-- Statistics -->
                <Grid ColumnDefinitions="*,*,*,*" RowDefinitions="Auto,Auto">
                    <Label Grid.Row="0" Grid.Column="0" Text="Total Sales (Period):" HorizontalTextAlignment="End"/>
                    <Label Grid.Row="0" Grid.Column="1" Text="{Binding PeriodTotalSales, StringFormat='{0:C2}'}"/>
                    
                    <Label Grid.Row="0" Grid.Column="2" Text="Average Order Value:" HorizontalTextAlignment="End"/>
                    <Label Grid.Row="0" Grid.Column="3" Text="{Binding AverageOrderValue, StringFormat='{0:C2}'}"/>
                    
                    <Label Grid.Row="1" Grid.Column="0" Text="Top Selling Product:" HorizontalTextAlignment="End"/>
                    <Label Grid.Row="1" Grid.Column="1" Text="{Binding TopSellingProduct}"/>
                    
                    <Label Grid.Row="1" Grid.Column="2" Text="Daily Average Sales:" HorizontalTextAlignment="End"/>
                    <Label Grid.Row="1" Grid.Column="3" Text="{Binding DailyAverageSales, StringFormat='{0:C2}'}"/>
                </Grid>
                
                <!-- Navigation -->
                <HorizontalStackLayout Spacing="10" HorizontalOptions="Center">
                    <Button Text="Back to Dashboard" Clicked="OnBackToDashboard"/>
                    <Button Text="Print Invoice" Clicked="OnPrintInvoice"/>
                    <Button Text="Send Email" Clicked="OnSendEmail"/>
                </HorizontalStackLayout>
            </VerticalStackLayout>
        </ScrollView>
    </ContentPage.Content>
</ContentPage>