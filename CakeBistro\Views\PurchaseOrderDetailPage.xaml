<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="MCakeBistro.Views.PurchaseOrderDetailPage"
             Title="Purchase Order Details">
    <VerticalStackLayout>
        <!-- Order header -->
        <Grid Padding="10" BackgroundColor="#f5f5f5">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>
            
            <VerticalStackLayout>
                <Label Text="Order Number:" TextColor="Gray"/>
                <Label Text="{Binding Order.OrderNumber}"
                       FontAttributes="Bold"/>
                
                <Label Text="Supplier:" TextColor="Gray" Margin="0,10,0,0"/>
                <Label Text="{Binding Order.Supplier.Name}"
                       FontAttributes="Bold"/>
            </VerticalStackLayout>
            
            <VerticalStackLayout Grid.Column="1" HorizontalOptions="End">
                <Label Text="Date:" TextColor="Gray"/>
                <Label Text="{Binding Order.OrderDate, StringFormat='{0:MMM d, yyyy}'}"
                       FontAttributes="Bold"/>
                
                <Label Text="Status:" TextColor="Gray" Margin="0,10,0,0"/>
                <Label Text="{Binding Order.Status}"
                       FontAttributes="Bold"/>
            </VerticalStackLayout>
        </Grid>
        
        <!-- Items list -->
        <ScrollView>
            <VerticalStackLayout Padding="10">
                <Label Text="Items" FontSize="Large" FontAttributes="Bold"/>
                
                <CollectionView ItemsSource="{Binding Order.Items}"
                                SelectionMode="Single"
                                SelectedItem="{Binding SelectedItem}">
                    <CollectionView.ItemTemplate>
                        <DataTemplate>
                            <Grid Padding="5">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                
                                <VerticalStackLayout>
                                    <Label Text="{Binding Description}"
                                           FontAttributes="Bold"/>
                                    <Label Text="{Binding RawMaterial.Name}"
                                           TextColor="Gray"/>
                                </VerticalStackLayout>
                                
                                <Label Grid.Column="1" Text="{Binding Quantity}" HorizontalOptions="Center" VerticalOptions="Center"/>
                                <Label Grid.Column="2" Text="{Binding TotalPrice, StringFormat='{}{0:C}'}" HorizontalOptions="End" VerticalOptions="Center"/>
                            </Grid>
                        </DataTemplate>
                    </CollectionView.ItemTemplate>
                </CollectionView>
                
                <!-- Item actions -->
                <HorizontalStackLayout Spacing="10" Margin="0,10">
                    <Button Text="Add Item"
                            Command="{Binding AddItemCommand}"
                            HorizontalOptions="FillAndExpand"/>
                    <Button Text="Remove Item"
                            Command="{Binding RemoveItemCommand}"
                            HorizontalOptions="FillAndExpand"/>
                </HorizontalStackLayout>
                
                <!-- Order totals -->
                <Frame Margin="0,20" Padding="10" BackgroundColor="#f5f5f5">
                    <VerticalStackLayout>
                        <Label Text="Order Summary" FontSize="Medium" FontAttributes="Bold"/>
                        
                        <Grid ColumnDefinitions="*,Auto">
                            <Label Grid.Row="0" Grid.Column="0" Text="Subtotal:" TextColor="Gray"/>
                            <Label Grid.Row="0" Grid.Column="1" Text="{Binding Order.TotalAmount, StringFormat='{}{0:C}'}" HorizontalTextAlignment="End"/>
                        </Grid>
                    </VerticalStackLayout>
                </Frame>
                
                <!-- Action buttons -->
                <HorizontalStackLayout Spacing="10" Margin="0,20">
                    <Button Text="Save"
                            Command="{Binding SaveCommand}"
                            HorizontalOptions="FillAndExpand"/>
                    <Button Text="Cancel"
                            Command="{Binding CancelCommand}"
                            HorizontalOptions="FillAndExpand"/>
                    
                    <Button Text="Submit"
                            Command="{Binding SubmitCommand}"
                            HorizontalOptions="FillAndExpand"/>
                    <Button Text="Receive"
                            Command="{Binding ReceiveCommand}"
                            HorizontalOptions="FillAndExpand"/>
                </HorizontalStackLayout>
            </VerticalStackLayout>
        </ScrollView>
    </VerticalStackLayout>
</ContentPage>