<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="CakeBistro.Views.StockAdjustmentFormPage"
             Title="Stock Adjustment Form">
    <StackLayout Padding="20" Spacing="16">
        <Label Text="Stock Adjustment Entry" FontAttributes="Bold" FontSize="Large"/>
        <Entry Placeholder="Reason"
               Text="{Binding Reason}"
               x:Name="ReasonEntry"
               BackgroundColor="{Binding ReasonError, Converter={StaticResource NullToErrorColorConverter}}"/>
        <Label Text="{Binding ReasonError}" TextColor="Red" IsVisible="{Binding ReasonError, Converter={StaticResource NullToFalseConverter}}" FontSize="Small"/>
        <Entry Placeholder="Quantity" Keyboard="Numeric"
               Text="{Binding Quantity}"
               x:Name="QuantityEntry"
               BackgroundColor="{Binding QuantityError, Converter={StaticResource NullToErrorColorConverter}}"/>
        <Label Text="{Binding QuantityError}" TextColor="Red" IsVisible="{Binding QuantityError, Converter={StaticResource NullToFalseConverter}}" FontSize="Small"/>
        <DatePicker Date="{Binding Date}"/>
        <Label Text="{Binding DateError}" TextColor="Red" IsVisible="{Binding DateError, Converter={StaticResource NullToFalseConverter}}" FontSize="Small"/>
        <Button Text="Save" Command="{Binding SaveCommand}" IsEnabled="{Binding CanSave}"/>
        <Button Text="Cancel" Command="{Binding CancelCommand}"/>
        <ActivityIndicator IsRunning="{Binding IsSaving}" IsVisible="{Binding IsSaving}"/>
        <Label Text="{Binding ErrorMessage}" TextColor="Red" IsVisible="{Binding HasError}"/>
    </StackLayout>
</ContentPage>
