<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:viewmodels="clr-namespace:CakeBistro.ViewModels"
             x:Class="CakeBistro.Views.LoadingPage">
    <ContentPage.BindingContext>
        <viewmodels:LoadingViewModel />
    </ContentPage.BindingContext>
    <ScrollView>
        <VerticalStackLayout Padding="16">
            <Label Text="Loading Sections" FontSize="24" FontAttributes="Bold" />
            <Entry Placeholder="Section Name" Text="{Binding NewSectionName}" />
            <Entry Placeholder="Description" Text="{Binding NewSectionDescription}" />
            <Button Text="Create Section" Command="{Binding CreateSectionCommand}" />
            <CollectionView ItemsSource="{Binding LoadingSections}" SelectionMode="Single" SelectedItem="{Binding SelectedSection, Mode=TwoWay}">
                <CollectionView.ItemTemplate>
                    <DataTemplate>
                        <Frame Margin="10" Padding="10" BackgroundColor="White" CornerRadius="8">
                            <StackLayout>
                                <Label Text="{Binding Name}" FontAttributes="Bold" FontSize="18" />
                                <Label Text="{Binding Description}" FontSize="14" />
                                <CollectionView ItemsSource="{Binding Items}" SelectionMode="Single" SelectedItem="{Binding BindingContext.SelectedItem, Source={x:Reference Name=LoadingPage}, Mode=TwoWay}">
                                    <CollectionView.ItemTemplate>
                                        <DataTemplate>
                                            <StackLayout Orientation="Horizontal" Spacing="10">
                                                <Label Text="{Binding Name}" />
                                                <Label Text="Qty: {Binding Quantity}" />
                                                <Button Text="Remove" Command="{Binding BindingContext.RemoveItemCommand, Source={x:Reference Name=LoadingPage}}" CommandParameter="{Binding .}" />
                                                <Button Text="Adjust" Command="{Binding BindingContext.AdjustQuantityCommand, Source={x:Reference Name=LoadingPage}}" CommandParameter="{Binding Quantity}" />
                                            </StackLayout>
                                        </DataTemplate>
                                    </CollectionView.ItemTemplate>
                                </CollectionView>
                                <Entry Placeholder="Item Name" Text="{Binding BindingContext.NewItemName, Source={x:Reference Name=LoadingPage}}" />
                                <Entry Placeholder="Quantity" Keyboard="Numeric" Text="{Binding BindingContext.NewItemQuantity, Source={x:Reference Name=LoadingPage}}" />
                                <Button Text="Add Item" Command="{Binding BindingContext.AddItemCommand, Source={x:Reference Name=LoadingPage}}" />
                            </StackLayout>
                        </Frame>
                    </DataTemplate>
                </CollectionView.ItemTemplate>
            </CollectionView>
            <Label Text="{Binding StatusMessage}"
                   IsVisible="{Binding StatusMessage, Converter={StaticResource EmptyToFalseConverter}}"
                   TextColor="{Binding StatusColor}" />
        </VerticalStackLayout>
    </ScrollView>
</ContentPage>
