using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using CakeBistro.Core.Models;
using CakeBistro.Services;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Maui.Graphics;

namespace CakeBistro.ViewModels
{
    public partial class LoadingViewModel : ObservableObject
    {
        private readonly LoadingService _loadingService;

        [ObservableProperty]
        private ObservableCollection<LoadingSection> loadingSections = new();
        [ObservableProperty]
        private LoadingSection? selectedSection;
        [ObservableProperty]
        private LoadingItem? selectedItem;
        [ObservableProperty]
        private string? newSectionName;
        [ObservableProperty]
        private string? newSectionDescription;
        [ObservableProperty]
        private string? newItemName;
        [ObservableProperty]
        private int newItemQuantity;
        private Color _statusColor = Colors.Transparent;

        public Color StatusColor
        {
            get => _statusColor;
            set => SetProperty(ref _statusColor, value);
        }

        public LoadingViewModel(LoadingService loadingService)
        {
            _loadingService = loadingService;
        }

        [RelayCommand]
        public async Task LoadLoadingSectionsAsync()
        {
            var sections = await _loadingService.GetAllLoadingSectionsAsync();
            LoadingSections = new ObservableCollection<LoadingSection>(sections);
        }

        [RelayCommand]
        public async Task CreateSectionAsync()
        {
            if (!string.IsNullOrWhiteSpace(NewSectionName))
            {
                var section = new LoadingSection { Name = NewSectionName, Description = NewSectionDescription };
                await _loadingService.CreateLoadingSectionAsync(section);
                await LoadLoadingSectionsAsync();
                NewSectionName = null;
                NewSectionDescription = null;
                StatusMessage = "Section created successfully.";
                StatusColor = Color.FromArgb("#388E3C");
            }
        }

        [RelayCommand]
        public async Task AddItemAsync()
        {
            if (SelectedSection != null && !string.IsNullOrWhiteSpace(NewItemName))
            {
                var item = new LoadingItem { Name = NewItemName, Quantity = NewItemQuantity };
                await _loadingService.AddItemToLoadingSectionAsync(SelectedSection.Id, item);
                await LoadLoadingSectionsAsync();
                NewItemName = null;
                NewItemQuantity = 0;
                StatusMessage = "Item added successfully.";
                StatusColor = Color.FromArgb("#388E3C");
            }
        }

        [RelayCommand]
        public async Task RemoveItemAsync()
        {
            if (SelectedSection != null && SelectedItem != null)
            {
                await _loadingService.RemoveItemFromLoadingSectionAsync(SelectedSection.Id, SelectedItem.Id);
                await LoadLoadingSectionsAsync();
                StatusMessage = "Item removed successfully.";
                StatusColor = Color.FromArgb("#388E3C");
            }
        }

        [RelayCommand]
        public async Task AdjustQuantityAsync(int newQuantity)
        {
            if (SelectedSection != null && SelectedItem != null)
            {
                await _loadingService.AdjustLoadingItemQuantityAsync(SelectedSection.Id, SelectedItem.Id, newQuantity);
                await LoadLoadingSectionsAsync();
                StatusMessage = "Quantity adjusted successfully.";
                StatusColor = Color.FromArgb("#388E3C");
            }
        }

        [RelayCommand]
        public void SelectSection(LoadingSection section)
        {
            SelectedSection = section;
            SelectedItem = null;
            NewItemName = null;
            NewItemQuantity = 0;
            StatusMessage = "Section selected. You can now add items.";
            StatusColor = Color.FromArgb("#1976D2");
        }

        [RelayCommand]
        public void DeselectSection()
        {
            SelectedSection = null;
            SelectedItem = null;
            NewItemName = null;
            NewItemQuantity = 0;
            StatusMessage = "Please select a section.";
            StatusColor = Color.FromArgb("#1976D2");
        }

        [ObservableProperty]
        private string? statusMessage;
    }
}
