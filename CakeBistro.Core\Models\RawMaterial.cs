using System;
using System.Collections.Generic;

namespace CakeBistro.Core.Models
{
    public class RawMaterial
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Unit { get; set; } = string.Empty;
        public decimal PricePerUnit { get; set; }
        public int CurrentStock { get; set; }
        public int MinimumStock { get; set; }
        public DateTime? LastStockTake { get; set; }
        public string Notes { get; set; } = string.Empty;
        public DateTime CreatedDate { get; set; }
        public DateTime UpdatedDate { get; set; }
        public bool IsLowStock => CurrentStock < MinimumStock;
        public ICollection<ExpiringBatchAlert> ExpiringBatchAlerts { get; set; } = new List<ExpiringBatchAlert>();
        public ICollection<StockMovement> StockMovements { get; set; } = new List<StockMovement>();
        public ICollection<PurchaseOrderItem> PurchaseOrderItems { get; set; } = new List<PurchaseOrderItem>();
        public ICollection<InventoryBatch> InventoryBatches { get; set; } = new List<InventoryBatch>();
        public ICollection<RawMaterialDocument> Documents { get; set; } = new List<RawMaterialDocument>();
    }
}
