using System;
using System.Collections.Generic;

namespace CakeBistro.Models;

public class FinancialReport
{
    public DateTime ReportDate { get; set; }
    public decimal TotalAssets { get; set; }
    public decimal TotalLiabilities { get; set; }
    public decimal TotalEquity { get; set; }
    public decimal NetIncome { get; set; }
    public decimal CashFlow { get; set; }
    public List<BankAccount> BankAccounts { get; set; } = new();
    public string Notes { get; set; }
    public ReportType ReportType { get; set; }
}
