using Microsoft.EntityFrameworkCore;
using CakeBistro.Core.Models;

namespace CakeBistro.Data
{
    /// <summary>
    /// Represents the database context for inventory management.
    /// </summary>
    public class InventoryContext : DbContext
    {
        /// <summary>
        /// Gets or sets the DbSet for products.
        /// </summary>
        public DbSet<Product> Products { get; set; }

        public DbSet<CakeBistro.Core.Models.RawMaterial> RawMaterials { get; set; }
        public DbSet<CakeBistro.Core.Models.Supplier> Suppliers { get; set; }
        public DbSet<StockMovement> StockMovements { get; set; }
        public DbSet<InterBranchTransfer> InterBranchTransfers { get; set; }
        public DbSet<InventoryBatch> InventoryBatches { get; set; }
        public DbSet<RawMaterialDocument> RawMaterialDocuments { get; set; }
        public DbSet<LowStockAlert> LowStockAlerts { get; set; }
        public DbSet<RawMaterialStockStatement> RawMaterialStockStatements { get; set; }
        public DbSet<CakeBistro.Core.Models.PurchaseOrder> PurchaseOrders { get; set; }
        public DbSet<PurchaseOrderItem> PurchaseOrderItems { get; set; }
        public DbSet<SalesOrder> SalesOrders { get; set; }
        public DbSet<SalesOrderItem> SalesOrderItems { get; set; }
        public DbSet<Customer> Customers { get; set; }
        public DbSet<CakeBistro.Core.Models.GoodsReceipt> GoodsReceipts { get; set; }
        public DbSet<GoodsReceiptItem> GoodsReceiptItems { get; set; }
        public DbSet<ReportBase> Reports { get; set; }
        public DbSet<CakeBistro.Core.Models.User> Users { get; set; }
        public DbSet<CakeBistro.Core.Models.Role> Roles { get; set; }
        public DbSet<Permission> Permissions { get; set; }
        public DbSet<RolePermission> RolePermissions { get; set; }
        public DbSet<UserPermission> UserPermissions { get; set; }
        public DbSet<UserActivity> UserActivities { get; set; }
        
        // Configure the database
        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);
            
            // Configure purchase order
            modelBuilder.Entity<CakeBistro.Core.Models.PurchaseOrder>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.OrderNumber).IsRequired().HasMaxLength(20);
                entity.Property(e => e.Description).HasMaxLength(500);
                entity.HasOne(e => e.CakeBistro.Core.Models.Supplier)
                    .WithMany()
                    .HasForeignKey(e => e.SupplierId)
                    .OnDelete(DeleteBehavior.Restrict);
            });
            
            // Configure purchase order item
            modelBuilder.Entity<PurchaseOrderItem>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Description).HasMaxLength(200);
                entity.HasOne(e => e.CakeBistro.Core.Models.PurchaseOrder)
                    .WithMany(o => o.Items)
                    .HasForeignKey(e => e.PurchaseOrderId)
                    .OnDelete(DeleteBehavior.Cascade);
                
                entity.HasOne(e => e.CakeBistro.Core.Models.RawMaterial)
                    .WithMany()
                    .HasForeignKey(e => e.RawMaterialId)
                    .OnDelete(DeleteBehavior.Restrict);
            });
            
            // Configure sales order
            modelBuilder.Entity<SalesOrder>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.OrderNumber).IsRequired().HasMaxLength(20);
                entity.Property(e => e.CustomerName).HasMaxLength(100);
                entity.HasOne(e => e.Customer)
                    .WithMany(c => c.SalesOrders)
                    .HasForeignKey(e => e.CustomerId)
                    .OnDelete(DeleteBehavior.Restrict);
            });
            
            // Configure sales order item
            modelBuilder.Entity<SalesOrderItem>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Description).HasMaxLength(200);
                entity.HasOne(e => e.SalesOrder)
                    .WithMany(o => o.Items)
                    .HasForeignKey(e => e.SalesOrderId)
                    .OnDelete(DeleteBehavior.Cascade);
                
                entity.HasOne(e => e.Product)
                    .WithMany()
                    .HasForeignKey(e => e.ProductId)
                    .OnDelete(DeleteBehavior.Restrict);
            });
            
            // Configure goods receipt
            modelBuilder.Entity<CakeBistro.Core.Models.GoodsReceipt>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.ReceiptNumber).IsRequired().HasMaxLength(20);
                
                entity.HasOne(e => e.CakeBistro.Core.Models.PurchaseOrder)
                    .WithMany(o => o.GoodsReceipts)
                    .HasForeignKey(e => e.PurchaseOrderId)
                    .OnDelete(DeleteBehavior.Restrict);
            });
            
            // Configure goods receipt item
            modelBuilder.Entity<GoodsReceiptItem>(entity =>
            {
                entity.HasKey(e => e.Id);
                
                entity.HasOne(e => e.CakeBistro.Core.Models.GoodsReceipt)
                    .WithMany(o => o.Items)
                    .HasForeignKey(e => e.GoodsReceiptId)
                    .OnDelete(DeleteBehavior.Cascade);
                
                entity.HasOne(e => e.PurchaseOrderItem)
                    .WithMany()
                    .HasForeignKey(e => e.PurchaseOrderItemId)
                    .OnDelete(DeleteBehavior.Restrict);
            });
            
            // Configure reports
            modelBuilder.Entity<ReportBase>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.ReportName).IsRequired().HasMaxLength(100);
                entity.Property(e => e.GeneratedDate).IsRequired();
                
                // Discriminator column for report types
                entity.HasDiscriminator<string>("ReportType")
                    .HasValue<SalesReport>("SalesReport")
                    .HasValue<InventoryReport>("InventoryReport")
                    .HasValue<PurchaseReport>("PurchaseReport");
            });
            
            // Configure users
            modelBuilder.Entity<CakeBistro.Core.Models.User>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Username).IsRequired().HasMaxLength(50);
                entity.Property(e => e.Email).IsRequired().HasMaxLength(100);
                entity.Property(e => e.PasswordHash).IsRequired();
                
                // Relationships
                entity.HasOne(e => e.CakeBistro.Core.Models.Role)
                    .WithMany(r => r.Users)
                    .HasForeignKey(e => e.RoleId)
                    .OnDelete(DeleteBehavior.SetNull);
            });
            
            // Configure roles
            modelBuilder.Entity<CakeBistro.Core.Models.Role>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Name).IsRequired().HasMaxLength(50);
                entity.Property(e => e.Description).HasMaxLength(200);
            });
            
            // Configure permissions
            modelBuilder.Entity<Permission>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.Name).IsRequired().HasMaxLength(50);
                entity.Property(e => e.Description).HasMaxLength(200);
            });
            
            // Configure CakeBistro.Core.Models.Role permissions
            modelBuilder.Entity<RolePermission>(entity =>
            {
                entity.HasKey(e => new { e.RoleId, e.PermissionId });
                
                entity.HasOne(e => e.CakeBistro.Core.Models.Role)
                    .WithMany(r => r.RolePermissions)
                    .HasForeignKey(e => e.RoleId)
                    .OnDelete(DeleteBehavior.Cascade);
                
                entity.HasOne(e => e.Permission)
                    .WithMany(p => p.RolePermissions)
                    .HasForeignKey(e => e.PermissionId)
                    .OnDelete(DeleteBehavior.Cascade);
            });
            
            // Configure CakeBistro.Core.Models.User permissions
            modelBuilder.Entity<UserPermission>(entity =>
            {
                entity.HasKey(e => new { e.UserId, e.PermissionId });
                
                entity.HasOne(e => e.CakeBistro.Core.Models.User)
                    .WithMany(u => u.UserPermissions)
                    .HasForeignKey(e => e.UserId)
                    .OnDelete(DeleteBehavior.Cascade);
                
                entity.HasOne(e => e.Permission)
                    .WithMany(p => p.UserPermissions)
                    .HasForeignKey(e => e.PermissionId)
                    .OnDelete(DeleteBehavior.Cascade);
            });
            
            // Configure CakeBistro.Core.Models.User activities
            modelBuilder.Entity<UserActivity>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.ActivityType).IsRequired().HasMaxLength(100);
                
                entity.HasOne(e => e.CakeBistro.Core.Models.User)
                    .WithMany(u => u.ActivityLogs)
                    .HasForeignKey(e => e.UserId)
                    .OnDelete(DeleteBehavior.Restrict);
            });
        }
    }
}
