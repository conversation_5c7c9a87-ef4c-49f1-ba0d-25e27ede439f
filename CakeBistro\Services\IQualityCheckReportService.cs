using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using CakeBistro.Core.Models;

namespace CakeBistro.Services
{
    public interface IQualityCheckReportService
    {
        Task<string> GenerateCsvReportAsync(IEnumerable<QualityCheck> checks, string filePath);
        Task<string> GeneratePdfReportAsync(IEnumerable<QualityCheck> checks, string filePath);
        Task<string> GenerateReportAsync(DateTime startDate, DateTime endDate, string checkType, ProductionBatch batch, string exportFormat);
    }
}
