

using CakeBistro.Core.Models;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace CakeBistro.Services;

public interface IPurchaseOrderService
{
    Task<CakeBistro.Core.Models.PurchaseOrder> CreatePurchaseOrderAsync(CakeBistro.Core.Models.PurchaseOrder order);
    Task<CakeBistro.Core.Models.PurchaseOrder> GetPurchaseOrderByIdAsync(int id);
    Task<IEnumerable<CakeBistro.Core.Models.PurchaseOrder>> GetAllPurchaseOrdersAsync();
    Task<CakeBistro.Core.Models.PurchaseOrder> UpdatePurchaseOrderAsync(CakeBistro.Core.Models.PurchaseOrder order);
    Task<bool> DeletePurchaseOrderAsync(int id);
    Task<CakeBistro.Core.Models.PurchaseOrder> SubmitPurchaseOrderAsync(int id);
    Task<CakeBistro.Core.Models.PurchaseOrder> ReceivePurchaseOrderAsync(int id);
    Task<IEnumerable<CakeBistro.Core.Models.PurchaseOrder>> GetRecentOrdersAsync(int days);
}
