using Microsoft.Maui.Controls;
using CakeBistro.ViewModels;

namespace CakeBistro.Views
{
    public partial class SalesOrderDetailPage : ContentPage
    {
        private readonly IInventoryService _inventoryService;
        private Guid _orderId;
        
        public SalesOrderDetailViewModel ViewModel { get; }
        
        public SalesOrderDetailPage(IInventoryService inventoryService)
            : base()
        {
            _inventoryService = inventoryService;
            ViewModel = new SalesOrderDetailViewModel(inventoryService);
            BindingContext = ViewModel;
            
            // Subscribe to messages
            MessagingCenter.Subscribe<SalesOrderDetailViewModel>(this, "NavigateBack", async (sender) =>
            {
                await Navigation.PopAsync();
            });
            
            MessagingCenter.Subscribe<SalesOrderDetailViewModel>(this, "SalesOrderSaved", async (sender, orderId) =>
            {
                // Navigate back after saving
                await Navigation.PopAsync();
            });
            
            MessagingCenter.Subscribe<SalesOrderDetailViewModel>(this, "SalesOrderSubmitted", async (sender, orderId) =>
            {
                // Navigate back after submitting
                await Navigation.PopAsync();
            });
            
            MessagingCenter.Subscribe<SalesOrderDetailViewModel>(this, "LowStockAlerts", (sender, message) =>
            {
                DisplayAlert("Low Stock Alerts", message, "OK");
            });
        }
        
        protected override async void OnNavigatedTo(NavigatedToEventArgs args)
        {
            base.OnNavigatedTo(args);
            
            if (args.Parameter is Guid orderId && orderId != ViewModel.Order?.Id)
            {
                _orderId = orderId;
                await ViewModel.LoadDataAsync(orderId);
            }
        }
        
        private async void DisplayAlert(string title, string message, string cancel)
        {
            await DisplayAlert(title, message, cancel);
        }
    }
}
