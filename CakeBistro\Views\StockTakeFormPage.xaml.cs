using System;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using CakeBistro.ViewModels;
using CakeBistro.Core.Interfaces;

namespace CakeBistro.Views
{
    [QueryProperty(nameof(StockTakeId), "id")]
    public partial class StockTakeFormPage : ContentPage
    {
        public int? StockTakeId { get; set; }
        private StockTakeFormViewModel _viewModel;
        
        public StockTakeFormPage()
        {
            InitializeComponent();

            var inventoryService = MauiProgram.ServiceProvider.GetService<IInventoryService>();
            _viewModel = new StockTakeFormViewModel(inventoryService);
            BindingContext = _viewModel;
        }

        protected override async void OnAppearing()
        {
            base.OnAppearing();
            if (StockTakeId.HasValue)
            {
                await _viewModel.LoadAsync(StockTakeId.Value);
            }
        }
    }
}
