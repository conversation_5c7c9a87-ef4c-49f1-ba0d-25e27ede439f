using CakeBistro;
using CakeBistro.Core.Interfaces;
using CakeBistro.ViewModels;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Maui.Controls;

namespace CakeBistro.Views
{
    public partial class InterBranchTransferFormPage : ContentPage
    {
        private readonly InterBranchTransferFormViewModel _viewModel;

        public InterBranchTransferFormPage()
        {
            InitializeComponent();
            _viewModel = new InterBranchTransferFormViewModel();
            BindingContext = _viewModel;
        }

        public string TransferId
        {
            set
            {
                if (Guid.TryParse(value, out var id))
                {
                    _viewModel.LoadTransferAsync(id);
                }
            }
        }
    }
}
