using System;
using System.Threading.Tasks;
using CakeBistro.ViewModels;
using CakeBistro.Core.Interfaces;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Maui.Controls;

namespace CakeBistro.Views
{
    [QueryProperty(nameof(SupplierId), "id")]
    public partial class SupplierFormPage : ContentPage
    {
        public int? SupplierId { get; set; }
        private SupplierFormViewModel _viewModel;
        public SupplierFormPage()
        {
            InitializeComponent();
            var supplierService = MauiProgram.ServiceProvider.GetService<ISupplierService>();
            _viewModel = new SupplierFormViewModel(supplierService);
            BindingContext = _viewModel;
        }

        protected override async void OnAppearing()
        {
            base.OnAppearing();
            if (SupplierId.HasValue)
            {
                await _viewModel.LoadAsync(SupplierId.Value);
            }
        }
    }
}
