using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using CakeBistro.Core.Interfaces;
using CakeBistro.Core.Models;

namespace CakeBistro.Services
{
    /// <summary>
    /// Enhanced InventoryService that implements PRD 4.1 Store Management requirements
    /// using Entity Framework Core for database persistence
    /// </summary>
    public class InventoryServiceNew : IInventoryService
    {
        private readonly CakeBistroContext _context;

        public InventoryServiceNew(CakeBistroContext context)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
        }

        #region PRD 4.1: Register Raw Materials

        /// <summary>
        /// PRD 4.1: "Register Raw Materials: Easily catalog all items used in production, including their prices"
        /// </summary>
        public async Task<CakeBistro.Core.Models.RawMaterial> RegisterRawMaterialAsync(CakeBistro.Core.Models.RawMaterial CakeBistro.Core.Models.RawMaterial)
        {
            if (CakeBistro.Core.Models.RawMaterial == null)
                throw new ArgumentNullException(nameof(CakeBistro.Core.Models.RawMaterial));

            // Validate required fields
            if (string.IsNullOrWhiteSpace(CakeBistro.Core.Models.RawMaterial.Name))
                throw new ArgumentException("Raw material name is required", nameof(CakeBistro.Core.Models.RawMaterial.Name));

            if (string.IsNullOrWhiteSpace(CakeBistro.Core.Models.RawMaterial.Unit))
                throw new ArgumentException("Unit of measure is required", nameof(CakeBistro.Core.Models.RawMaterial.Unit));

            // Set audit fields
            CakeBistro.Core.Models.RawMaterial.CreatedDate = DateTime.UtcNow;
            CakeBistro.Core.Models.RawMaterial.UpdatedDate = DateTime.UtcNow;

            // Set default values if not provided
            CakeBistro.Core.Models.RawMaterial.CurrentStock = CakeBistro.Core.Models.RawMaterial.CurrentStock;
            CakeBistro.Core.Models.RawMaterial.MinimumStock = CakeBistro.Core.Models.RawMaterial.MinimumStock > 0 ? CakeBistro.Core.Models.RawMaterial.MinimumStock : 10;

            _context.RawMaterials.Add(CakeBistro.Core.Models.RawMaterial);
            await _context.SaveChangesAsync();
            
            return CakeBistro.Core.Models.RawMaterial;
        }

        public async Task<IEnumerable<CakeBistro.Core.Models.RawMaterial>> GetAllRawMaterialsAsync()
        {
            return await _context.RawMaterials
                .Include(rm => rm.StockMovements)
                .Include(rm => rm.InventoryBatches)
                .OrderBy(rm => rm.Name)
                .ToListAsync();
        }

        public async Task<CakeBistro.Core.Models.RawMaterial?> GetRawMaterialByIdAsync(int id)
        {
            return await _context.RawMaterials
                .Include(rm => rm.StockMovements)
                .Include(rm => rm.InventoryBatches)
                .FirstOrDefaultAsync(rm => rm.Id == id);
        }

        public async Task<bool> UpdateRawMaterialAsync(CakeBistro.Core.Models.RawMaterial CakeBistro.Core.Models.RawMaterial)
        {
            if (CakeBistro.Core.Models.RawMaterial == null)
                return false;

            var existing = await _context.RawMaterials.FindAsync(CakeBistro.Core.Models.RawMaterial.Id);
            if (existing == null)
                return false;

            // Update properties
            existing.Name = CakeBistro.Core.Models.RawMaterial.Name;
            existing.Unit = CakeBistro.Core.Models.RawMaterial.Unit;
            existing.PricePerUnit = CakeBistro.Core.Models.RawMaterial.PricePerUnit;
            existing.CurrentStock = CakeBistro.Core.Models.RawMaterial.CurrentStock;
            existing.MinimumStock = CakeBistro.Core.Models.RawMaterial.MinimumStock;
            existing.Notes = CakeBistro.Core.Models.RawMaterial.Notes;
            existing.UpdatedDate = DateTime.UtcNow;

            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> DeleteRawMaterialAsync(int id)
        {
            var material = await _context.RawMaterials.FindAsync(id);
            if (material == null)
                return false;

            // Check if there are any stock movements or batches associated
            var hasMovements = await _context.StockMovements.AnyAsync(sm => sm.RawMaterialId == id);
            var hasBatches = await _context.InventoryBatches.AnyAsync(ib => ib.RawMaterialId == id);

            if (hasMovements || hasBatches)
            {
                throw new InvalidOperationException("Cannot delete raw material with existing stock movements or batches");
            }

            _context.RawMaterials.Remove(material);
            await _context.SaveChangesAsync();
            return true;
        }

        #endregion

        #region PRD 4.1: Track Stock Movement

        /// <summary>
        /// PRD 4.1: "Track Stock Movement: Accurately capture incoming raw material stock and outgoing stock"
        /// </summary>
        public async Task<StockMovement> RecordStockMovementAsync(StockMovement stockMovement)
        {
            if (stockMovement == null)
                throw new ArgumentNullException(nameof(stockMovement));

            // Validate required fields
            if (stockMovement.RawMaterialId <= 0)
                throw new ArgumentException("Valid raw material ID is required", nameof(stockMovement.RawMaterialId));

            if (stockMovement.Quantity <= 0)
                throw new ArgumentException("Quantity must be greater than zero", nameof(stockMovement.Quantity));

            // Find the raw material
            var material = await _context.RawMaterials.FindAsync(stockMovement.RawMaterialId);
            if (material == null)
                throw new ArgumentException("Raw material not found", nameof(stockMovement.RawMaterialId));

            // Set movement date if not provided
            if (stockMovement.MovementDate == default)
                stockMovement.MovementDate = DateTime.UtcNow;

            // Generate reference number if not provided
            if (string.IsNullOrEmpty(stockMovement.ReferenceNumber))
                stockMovement.ReferenceNumber = $"MOV-{DateTime.Now:yyyyMMdd-HHmmss}";

            // Update material stock based on movement type
            if (stockMovement.MovementType == MovementType.Incoming)
            {
                material.CurrentStock += (int)stockMovement.Quantity;
            }
            else if (stockMovement.MovementType == MovementType.Outgoing)
            {
                if (material.CurrentStock < stockMovement.Quantity)
                    throw new InvalidOperationException(
                        $"Insufficient stock for {material.Name}. Available: {material.CurrentStock}, Requested: {stockMovement.Quantity}");
                
                material.CurrentStock -= (int)stockMovement.Quantity;
            }

            // Update last stock take date
            material.LastStockTake = DateTime.UtcNow;
            material.UpdatedDate = DateTime.UtcNow;

            _context.StockMovements.Add(stockMovement);
            await _context.SaveChangesAsync();

            return stockMovement;
        }

        public async Task<IEnumerable<StockMovement>> GetStockMovementsAsync(int? rawMaterialId = null, DateTime? fromDate = null, DateTime? toDate = null)
        {
            var query = _context.StockMovements
                .Include(sm => sm.CakeBistro.Core.Models.RawMaterial)
                .AsQueryable();

            if (rawMaterialId.HasValue)
                query = query.Where(sm => sm.RawMaterialId == rawMaterialId.Value);

            if (fromDate.HasValue)
                query = query.Where(sm => sm.MovementDate >= fromDate.Value);

            if (toDate.HasValue)
                query = query.Where(sm => sm.MovementDate <= toDate.Value);

            return await query
                .OrderByDescending(sm => sm.MovementDate)
                .ToListAsync();
        }

        #endregion

        #region PRD 4.1: Generate Reports

        /// <summary>
        /// PRD 4.1: "Generate detailed stock movement reports and raw material stock statements"
        /// </summary>
        public async Task<IEnumerable<CakeBistro.Core.Models.RawMaterial>> GenerateStockReportAsync()
        {
            return await _context.RawMaterials
                .Include(rm => rm.StockMovements)
                .Include(rm => rm.InventoryBatches)
                .OrderBy(rm => rm.Name)
                .ToListAsync();
        }

        public async Task<object> GenerateStockStatementAsync(int rawMaterialId, DateTime startDate, DateTime endDate)
        {
            var material = await _context.RawMaterials
                .Include(rm => rm.StockMovements.Where(sm => sm.MovementDate >= startDate && sm.MovementDate <= endDate))
                .FirstOrDefaultAsync(rm => rm.Id == rawMaterialId);

            if (material == null)
                throw new ArgumentException("Raw material not found", nameof(rawMaterialId));

            var movements = material.StockMovements.OrderBy(sm => sm.MovementDate).ToList();
            
            var openingStock = await GetStockAtDateAsync(rawMaterialId, startDate);
            var totalIncoming = movements.Where(m => m.MovementType == MovementType.Incoming).Sum(m => m.Quantity);
            var totalOutgoing = movements.Where(m => m.MovementType == MovementType.Outgoing).Sum(m => m.Quantity);
            var closingStock = openingStock + totalIncoming - totalOutgoing;

            return new
            {
                Material = material,
                Period = new { StartDate = startDate, EndDate = endDate },
                OpeningStock = openingStock,
                TotalIncoming = totalIncoming,
                TotalOutgoing = totalOutgoing,
                ClosingStock = closingStock,
                Movements = movements
            };
        }

        private async Task<decimal> GetStockAtDateAsync(int rawMaterialId, DateTime date)
        {
            var movements = await _context.StockMovements
                .Where(sm => sm.RawMaterialId == rawMaterialId && sm.MovementDate < date)
                .ToListAsync();

            var totalIncoming = movements.Where(m => m.MovementType == MovementType.Incoming).Sum(m => m.Quantity);
            var totalOutgoing = movements.Where(m => m.MovementType == MovementType.Outgoing).Sum(m => m.Quantity);

            return totalIncoming - totalOutgoing;
        }

        #endregion

        #region Dashboard and Analytics

        public async Task<IEnumerable<CakeBistro.Core.Models.RawMaterial>> GetLowStockItemsAsync(int threshold = 10)
        {
            return await _context.RawMaterials
                .Where(rm => rm.CurrentStock <= rm.MinimumStock || 
                           rm.CurrentStock <= rm.MinimumStock + threshold)
                .OrderBy(rm => rm.CurrentStock)
                .ThenBy(rm => rm.Name)
                .ToListAsync();
        }

        public async Task<int> GetTotalRawMaterialsCountAsync()
        {
            return await _context.RawMaterials.CountAsync();
        }

        public async Task<decimal> GetTotalInventoryValueAsync()
        {
            return await _context.RawMaterials
                .Where(rm => rm.CurrentStock > 0)
                .SumAsync(rm => rm.CurrentStock * rm.PricePerUnit);
        }

        public async Task<IEnumerable<InventoryBatch>> GetExpiringBatchesAsync(int daysAhead = 30)
        {
            var cutoffDate = DateTime.Today.AddDays(daysAhead);
            return await _context.InventoryBatches
                .Include(ib => ib.CakeBistro.Core.Models.RawMaterial)
                .Where(ib => ib.ExpiryDate <= cutoffDate && ib.Quantity > 0)
                .OrderBy(ib => ib.ExpiryDate)
                .ToListAsync();
        }

        public async Task<int> GetLowStockItemsCountAsync()
        {
            return await _context.RawMaterials
                .CountAsync(rm => rm.CurrentStock <= rm.MinimumStock);
        }

        public async Task<int> GetStockMovementsCountAsync()
        {
            // Implement as needed
            return await _context.StockMovements.CountAsync();
        }

        public async Task<int> GetInterBranchTransfersCountAsync()
        {
            // Implement as needed
            return 0;
        }

        public async Task<int> GetMonthlyStockTakeCountAsync()
        {
            // Implement as needed
            return 0;
        }

        #endregion
    }
}
