using CakeBistro.Services;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;

namespace CakeBistro.Services
{
    /// <summary>
    /// Interface for distributed cache service
    /// </summary>
    public interface IDistributedCacheService : ICacheService
    {
        /// <summary>
        /// Gets a value from the cache with a specific key
        /// </summary>
        new Task<T> GetAsync<T>(string key);

        /// <summary>
        /// Sets a value in the cache with a specific key and expiration time
        /// </summary>
        new Task SetAsync<T>(string key, T value, TimeSpan? expirationTime = null);

        /// <summary>
        /// Tries to get a value from the cache
        /// </summary>
        new Task<bool> TryGetAsync<T>(string key, out T value);
    }

    /// <summary>
    /// Implementation of IDistributedCacheService using Redis
    /// </summary>
    public class RedisCacheService : IDistributedCacheService
    {
        private readonly IDistributedCache _distributedCache;
        private readonly ILogger<RedisCacheService> _logger;
        private readonly CacheSettings _cacheSettings;

        public RedisCacheService(IDistributedCache distributedCache, 
            ILogger<RedisCacheService> logger,
            CacheSettings cacheSettings)
        {
            _distributedCache = distributedCache;
            _logger = logger;
            _cacheSettings = cacheSettings ?? new CacheSettings();
        }

        public async Task<T> GetAsync<T>(string key)
        {
            try
            {
                var cachedValue = await _distributedCache.GetStringAsync(key);
                if (string.IsNullOrEmpty(cachedValue))
                {
                    return default;
                }
                
                // In a real implementation, you would deserialize the value
                // For simplicity, we're using string values only in this example
                return (T)(object)cachedValue;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error retrieving distributed cache item {Key}: {Message}", key, ex.Message);
                return default;
            }
        }

        public async Task SetAsync<T>(string key, T value, TimeSpan? expirationTime = null)
        {
            try
            {
                var options = new DistributedCacheEntryOptions
                {
                    AbsoluteExpirationRelativeToNow = expirationTime ?? _cacheSettings.DefaultCacheDuration
                };
                
                // In a real implementation, you would serialize the value
                // For simplicity, we're using string values only in this example
                await _distributedCache.SetStringAsync(key, value?.ToString(), options);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error setting distributed cache item {Key}: {Message}", key, ex.Message);
            }
        }

        public async Task<bool> TryGetAsync<T>(string key, out T value)
        {
            try
            {
                var cachedValue = await _distributedCache.GetStringAsync(key);
                if (string.IsNullOrEmpty(cachedValue))
                {
                    value = default;
                    return false;
                }
                
                // In a real implementation, you would deserialize the value
                // For simplicity, we're using string values only in this example
                value = (T)(object)cachedValue;
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error trying to get distributed cache item {Key}: {Message}", key, ex.Message);
                value = default;
                return false;
            }
        }

        public T Get<T>(string key)
        {
            try
            {
                var cachedValue = _distributedCache.GetString(key);
                return string.IsNullOrEmpty(cachedValue) ? default : (T)(object)cachedValue;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error retrieving distributed cache item {Key}: {Message}", key, ex.Message);
                return default;
            }
        }

        public void Set<T>(string key, T value, TimeSpan? expirationTime = null)
        {
            try
            {
                var options = new DistributedCacheEntryOptions
                {
                    AbsoluteExpirationRelativeToNow = expirationTime ?? _cacheSettings.DefaultCacheDuration
                };
                
                _distributedCache.SetString(key, value?.ToString(), options);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error setting distributed cache item {Key}: {Message}", key, ex.Message);
            }
        }

        public bool TryGet<T>(string key, out T value)
        {
            try
            {
                var cachedValue = _distributedCache.GetString(key);
                if (string.IsNullOrEmpty(cachedValue))
                {
                    value = default;
                    return false;
                }
                
                value = (T)(object)cachedValue;
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error trying to get distributed cache item {Key}: {Message}", key, ex.Message);
                value = default;
                return false;
            }
        }

        public void Remove(string key)
        {
            try
            {
                _distributedCache.Remove(key);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error removing distributed cache item {Key}: {Message}", key, ex.Message);
            }
        }

        /// <summary>
        /// Invalidates all cache entries with the specified key prefix
        /// </summary>
        /// <param name="keyPrefix">The prefix of keys to invalidate</param>
        public void Invalidate(string keyPrefix)
        {
            try
            {
                // For Redis, we can use a pattern to remove keys matching a prefix
                // Note: This is a simplified implementation - production should use Redis' SCAN command
                var server = GetRedisServer();
                if (server != null)
                {
                    var keys = _distributedCache.GetDatabase().Keys(pattern: $"{keyPrefix}*");
                    foreach (var key in keys)
                    {
                        _distributedCache.Remove(key);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error invalidating distributed cache items with prefix {KeyPrefix}: {Message}", keyPrefix, ex.Message);
            }
        }

        /// <summary>
        /// Invalidates all cache entries with the specified tag
        /// </summary>
        /// <param name="tag">The tag to invalidate</param>
        public void InvalidateByTag(string tag)
        {
            try
            {
                // For Redis, we can use a tagging strategy to remove related keys
                // This is a simplified implementation - production should use Redis' SCAN command
                var server = GetRedisServer();
                if (server != null)
                {
                    // In a real implementation, you would use Redis tags or a tagging strategy
                    // This is just a placeholder for demonstration purposes
                    var keys = _distributedCache.GetDatabase().Keys(pattern: $"{tag}_*");
                    foreach (var key in keys)
                    {
                        _distributedCache.Remove(key);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error invalidating distributed cache items by tag {Tag}: {Message}", tag, ex.Message);
            }
        }
        
        private Microsoft.Extensions.Caching.Redis.RedisCacheServer GetRedisServer()
        {
            // In a real implementation, you would get the Redis server instance
            // This is just a placeholder for demonstration purposes
            return null;
        }
    }
}