<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="CakeBistro.Views.RawMaterialsPage"
             x:Name="RawMaterialsPage"
             Title="Raw Materials Management">

    <Grid RowDefinitions="Auto,*,Auto" Padding="20">
        
        <!-- Header with Search and Add Button -->
        <Grid Grid.Row="0" ColumnDefinitions="*,Auto" Margin="0,0,0,20">
            <SearchBar Grid.Column="0" 
                       x:Name="SearchBar"
                       Placeholder="Search raw materials..."
                       SearchCommand="{Binding SearchCommand}"
                       SearchCommandParameter="{Binding Source={x:Reference SearchBar}, Path=Text}" />
            
            <Button Grid.Column="1" 
                    Text="Add New"
                    BackgroundColor="#4CAF50"
                    TextColor="White"
                    Command="{Binding AddRawMaterialCommand}"
                    Margin="10,0,0,0" />
        </Grid>

        <!-- Materials List -->
        <CollectionView Grid.Row="1" 
                        ItemsSource="{Binding RawMaterials}"
                        SelectionMode="Single"
                        SelectedItem="{Binding SelectedRawMaterial}">
            <CollectionView.ItemTemplate>
                <DataTemplate>
                    <Grid Padding="15" RowDefinitions="Auto,Auto,Auto" ColumnDefinitions="*,Auto,Auto">
                        
                        <!-- Material Info -->
                        <Label Grid.Row="0" Grid.Column="0"
                               Text="{Binding Name}"
                               FontSize="16"
                               FontAttributes="Bold" />
                        
                        <StackLayout Grid.Row="1" Grid.Column="0" Grid.ColumnSpan="3" 
                                     Orientation="Horizontal" 
                                     Spacing="10">
                            <Label Text="Stock:" TextColor="Gray" />
                            <Label Text="{Binding StockLevel}" />
                            <Label Text="{Binding UnitOfMeasure}" />
                            <Label Text="| Min:" TextColor="Gray" />
                            <Label Text="{Binding MinimumStock}" />
                            <Label Text="| Unit Price:" TextColor="Gray" />
                            <Label Text="{Binding UnitPrice, StringFormat='{0:C}'}" />
                        </StackLayout>

                        <!-- Stock Level Indicator -->
                        <Frame Grid.Row="0" Grid.Column="1" 
                               BackgroundColor="{Binding StockLevel, Converter={StaticResource StockLevelColorConverter}}"
                               Padding="8,4"
                               CornerRadius="12"
                               Margin="5,0">
                            <Label Text="{Binding StockLevel, Converter={StaticResource StockLevelTextConverter}}"
                                   TextColor="White"
                                   FontSize="12"
                                   FontAttributes="Bold" />
                        </Frame>

                        <!-- Action Buttons -->
                        <StackLayout Grid.Row="0" Grid.Column="2" 
                                     Orientation="Horizontal" 
                                     Spacing="5">
                            <Button Text="Edit"
                                    BackgroundColor="#2196F3"
                                    TextColor="White"
                                    FontSize="12"
                                    Padding="8,4"
                                    Command="{Binding BindingContext.EditRawMaterialCommand, Source={x:Reference Name=RawMaterialsPage}}"
                                    CommandParameter="{Binding}" />
                            
                            <Button Text="Delete"
                                    BackgroundColor="#F44336"
                                    TextColor="White"
                                    FontSize="12"
                                    Padding="8,4"
                                    Command="{Binding BindingContext.DeleteRawMaterialCommand, Source={x:Reference Name=RawMaterialsPage}}"
                                    CommandParameter="{Binding}" />
                        </StackLayout>

                        <!-- Separator -->
                        <BoxView Grid.Row="2" Grid.ColumnSpan="3" 
                                 HeightRequest="1" 
                                 BackgroundColor="LightGray" 
                                 Margin="0,10,0,0" />
                    </Grid>
                </DataTemplate>
            </CollectionView.ItemTemplate>
        </CollectionView>

        <!-- Status and Refresh -->
        <Grid Grid.Row="2" ColumnDefinitions="*,Auto" Margin="0,20,0,0">
            <Label Grid.Column="0" 
                   Text="{Binding StatusMessage}"
                   TextColor="Gray"
                   VerticalOptions="Center" />
            
            <Button Grid.Column="1"
                    Text="Refresh"
                    BackgroundColor="#FF9800"
                    TextColor="White"
                    Command="{Binding RefreshCommand}" />
        </Grid>

    </Grid>

</ContentPage>
