<?xml version="1.0" encoding="utf-8" ?>
<ResourceDictionary xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
                    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml">
    <!-- Color Palette -->
    <Color x:Key="Primary">#512BD4</Color>
    <Color x:Key="PrimaryDark">#311B92</Color>
    <Color x:Key="Secondary">#FFB300</Color>
    <Color x:Key="Accent">#00BFAE</Color>
    <Color x:Key="Surface">#FFFFFF</Color>
    <Color x:Key="Background">#F5F5F5</Color>
    <Color x:Key="Error">#D32F2F</Color>
    <Color x:Key="Success">#388E3C</Color>
    <Color x:Key="Warning">#FFA000</Color>
    <Color x:Key="Info">#1976D2</Color>
    <Color x:Key="TextPrimary">#222222</Color>
    <Color x:Key="TextSecondary">#666666</Color>

    <!-- Typography -->
    <Style TargetType="Label">
        <Setter Property="FontFamily" Value="OpenSansRegular" />
        <Setter Property="TextColor" Value="{StaticResource TextPrimary}" />
    </Style>
    <Style TargetType="Button">
        <Setter Property="FontFamily" Value="OpenSansSemibold" />
        <Setter Property="BackgroundColor" Value="{StaticResource Primary}" />
        <Setter Property="TextColor" Value="White" />
        <Setter Property="CornerRadius" Value="8" />
        <Setter Property="FontSize" Value="16" />
        <Setter Property="Padding" Value="12,6" />
        <Setter Property="Shadow" Value="True" />
    </Style>
    <Style TargetType="Entry">
        <Setter Property="FontFamily" Value="OpenSansRegular" />
        <Setter Property="FontSize" Value="15" />
        <Setter Property="BackgroundColor" Value="{StaticResource Surface}" />
        <Setter Property="TextColor" Value="{StaticResource TextPrimary}" />
        <Setter Property="Margin" Value="0,0,0,8" />
        <Setter Property="HeightRequest" Value="40" />
        <Setter Property="CornerRadius" Value="6" />
        <Setter Property="VisualStateManager.VisualStateGroups">
            <VisualStateGroupList>
                <VisualStateGroup x:Name="CommonStates">
                    <VisualState x:Name="Normal" />
                    <VisualState x:Name="Focused">
                        <VisualState.Setters>
                            <Setter Property="BackgroundColor" Value="{StaticResource Accent}" />
                            <Setter Property="TextColor" Value="White" />
                        </VisualState.Setters>
                    </VisualState>
                </VisualStateGroup>
            </VisualStateGroupList>
        </Setter>
    </Style>
    <Style TargetType="Picker">
        <Setter Property="FontFamily" Value="OpenSansRegular" />
        <Setter Property="FontSize" Value="15" />
        <Setter Property="BackgroundColor" Value="{StaticResource Surface}" />
        <Setter Property="TextColor" Value="{StaticResource TextPrimary}" />
        <Setter Property="Margin" Value="0,0,0,8" />
        <Setter Property="HeightRequest" Value="40" />
        <Setter Property="CornerRadius" Value="6" />
        <Setter Property="VisualStateManager.VisualStateGroups">
            <VisualStateGroupList>
                <VisualStateGroup x:Name="CommonStates">
                    <VisualState x:Name="Normal" />
                    <VisualState x:Name="Focused">
                        <VisualState.Setters>
                            <Setter Property="BackgroundColor" Value="{StaticResource Accent}" />
                            <Setter Property="TextColor" Value="White" />
                        </VisualState.Setters>
                    </VisualState>
                </VisualStateGroup>
            </VisualStateGroupList>
        </Setter>
    </Style>
    <Style TargetType="CollectionView">
        <Setter Property="BackgroundColor" Value="{StaticResource Background}" />
        <Setter Property="ItemContainerStyle">
            <Setter.Value>
                <Style TargetType="ViewCell">
                    <Setter Property="Opacity" Value="0" />
                    <Setter Property="TranslationY" Value="20" />
                    <Setter Property="VisualStateManager.VisualStateGroups">
                        <VisualStateGroupList>
                            <VisualStateGroup x:Name="CommonStates">
                                <VisualState x:Name="Normal">
                                    <VisualState.Setters>
                                        <Setter Property="Opacity" Value="1" />
                                        <Setter Property="TranslationY" Value="0" />
                                        <Setter Property="Scale" Value="1" />
                                    </VisualState.Setters>
                                </VisualState>
                                <VisualState x:Name="Appearing">
                                    <VisualState.Setters>
                                        <Setter Property="Opacity" Value="0.2" />
                                        <Setter Property="TranslationY" Value="40" />
                                        <Setter Property="Scale" Value="0.96" />
                                    </VisualState.Setters>
                                </VisualState>
                            </VisualStateGroup>
                        </VisualStateGroupList>
                    </Setter>
                </Style>
            </Setter.Value>
        </Setter>
    </Style>
    <Style TargetType="ContentPage">
        <Setter Property="BackgroundColor" Value="{StaticResource Background}" />
    </Style>
    <Style x:Key="PrimaryButton" TargetType="Button">
        <Setter Property="BackgroundColor" Value="{StaticResource Primary}" />
        <Setter Property="TextColor" Value="White" />
        <Setter Property="CornerRadius" Value="8" />
        <Setter Property="FontAttributes" Value="Bold" />
        <Setter Property="FontSize" Value="16" />
        <Setter Property="Padding" Value="12,6" />
        <Setter Property="Shadow" Value="True" />
    </Style>
    <Style x:Key="SecondaryButton" TargetType="Button">
        <Setter Property="BackgroundColor" Value="{StaticResource Secondary}" />
        <Setter Property="TextColor" Value="White" />
        <Setter Property="CornerRadius" Value="8" />
        <Setter Property="FontAttributes" Value="Bold" />
        <Setter Property="FontSize" Value="16" />
        <Setter Property="Padding" Value="12,6" />
        <Setter Property="Shadow" Value="True" />
    </Style>
</ResourceDictionary>
