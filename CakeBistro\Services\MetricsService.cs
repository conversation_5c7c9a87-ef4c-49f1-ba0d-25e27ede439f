using Microsoft.Extensions.Logging;
using System.Diagnostics;

namespace CakeBistro.Services
{
    public interface IMetricsService
    {
        void TrackEvent(string eventName, IDictionary<string, object>? properties = null);
        void TrackDuration(string operationName, Action action);
        T TrackDuration<T>(string operationName, Func<T> func);
    }

    public class MetricsService : IMetricsService
    {
        private readonly ILogger<MetricsService> _logger;
        public MetricsService(ILogger<MetricsService> logger)
        {
            _logger = logger;
        }
        public void TrackEvent(string eventName, IDictionary<string, object>? properties = null)
        {
            _logger.LogInformation($"[Metrics] Event: {eventName} | Properties: {(properties == null ? "null" : System.Text.Json.JsonSerializer.Serialize(properties))}");
        }
        public void TrackDuration(string operationName, Action action)
        {
            var sw = Stopwatch.StartNew();
            try
            {
                action();
            }
            finally
            {
                sw.Stop();
                _logger.LogInformation($"[Metrics] Duration: {operationName} took {sw.ElapsedMilliseconds} ms");
            }
        }
        public T TrackDuration<T>(string operationName, Func<T> func)
        {
            var sw = Stopwatch.StartNew();
            T result = func();
            sw.Stop();
            _logger.LogInformation($"[Metrics] Duration: {operationName} took {sw.ElapsedMilliseconds} ms");
            return result;
        }
    }
}