using CakeBistro.Core.Models;
using CakeBistro.Services;
using System.Collections.ObjectModel;
using System.Windows.Input;
using Microsoft.Maui.Controls;

namespace CakeBistro.ViewModels.Admin
{
    public class UserManagementViewModel : BindableObject
    {
        private readonly IAuthenticationService _authService;
        private readonly IMetricsService _metricsService;
        public ObservableCollection<User> Users { get; set; } = new();
        public ICommand AddUserCommand { get; }
        public ICommand EditUserCommand { get; }
        public ICommand DeleteUserCommand { get; }

        public UserManagementViewModel()
        {
            _authService = Application.Current?.Handler?.MauiContext?.Services.GetService(typeof(IAuthenticationService)) as IAuthenticationService;
            _metricsService = Application.Current?.Handler?.MauiContext?.Services.GetService(typeof(IMetricsService)) as IMetricsService;
            AddUserCommand = new Command(OnAddUser);
            EditUserCommand = new Command<User>(OnEditUser);
            DeleteUserCommand = new Command<User>(OnDeleteUser);
            LoadUsers();
        }

        private async void LoadUsers()
        {
            if (_authService != null)
            {
                var sw = System.Diagnostics.Stopwatch.StartNew();
                var users = await _authService.GetAllUsersAsync();
                sw.Stop();
                Users.Clear();
                foreach (var user in users)
                    Users.Add(user);
                _metricsService?.TrackEvent("UserListLoaded", new Dictionary<string, object> { { "Count", Users.Count }, { "DurationMs", sw.ElapsedMilliseconds } });
            }
        }

        private void OnAddUser()
        {
            _metricsService?.TrackEvent("UserAddInitiated");
            // TODO: Show add user dialog
        }
        private void OnEditUser(User user)
        {
            _metricsService?.TrackEvent("UserEditInitiated", new Dictionary<string, object> { { "UserId", user?.Id } });
            // TODO: Show edit user dialog
        }
        private async void OnDeleteUser(User user)
        {
            if (_authService != null && user != null)
            {
                var sw = System.Diagnostics.Stopwatch.StartNew();
                await _authService.DeleteUserAsync(user.Id);
                sw.Stop();
                _metricsService?.TrackEvent("UserDeleted", new Dictionary<string, object> { { "UserId", user.Id }, { "DurationMs", sw.ElapsedMilliseconds } });
                LoadUsers();
            }
        }
    }
}
