using CakeBistro.Services;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;

namespace CakeBistro.Services
{
    public class MemoryCacheService : ICacheService
    {
        private readonly IMemoryCache _memoryCache;
        private readonly ILogger<MemoryCacheService> _logger;
        private readonly CacheSettings _cacheSettings;

        public MemoryCacheService(IMemoryCache memoryCache, ILogger<MemoryCacheService> logger, CacheSettings cacheSettings)
        {
            _memoryCache = memoryCache;
            _logger = logger;
            _cacheSettings = cacheSettings ?? new CacheSettings();
        }

        public T Get<T>(string key)
        {
            try
            {
                return _memoryCache.Get<T>(key);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error retrieving cache item {Key}: {Message}", key, ex.Message);
                return default;
            }
        }

        public void Set<T>(string key, T value, TimeSpan? expirationTime = null)
        {
            try
            {
                var options = new MemoryCacheEntryOptions
                {
                    AbsoluteExpirationRelativeToNow = expirationTime ?? _cacheSettings.DefaultCacheDuration
                };
                
                _memoryCache.Set(key, value, options);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error setting cache item {Key}: {Message}", key, ex.Message);
            }
        }

        public bool TryGet<T>(string key, out T value)
        {
            try
            {
                return _memoryCache.TryGetValue<T>(key, out value);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error trying to get cache item {Key}: {Message}", key, ex.Message);
                value = default;
                return false;
            }
        }

        public void Remove(string key)
        {
            try
            {
                _memoryCache.Remove(key);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error removing cache item {Key}: {Message}", key, ex.Message);
            }
        }

        // Add new method for cache invalidation based on data changes
        public void Invalidate(string keyPrefix)
        {
            try
            {
                // In a real implementation, you would use Redis's KEYS command or similar
                // For this in-memory implementation, we'll just log that invalidation is not fully implemented
                _logger.LogWarning("Invalidate with key prefix not implemented for memory cache. This should be implemented in a distributed cache implementation.");
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error invalidating cache with prefix {KeyPrefix}: {Message}", keyPrefix, ex.Message);
            }
        }

        public void InvalidateByTag(string tag)
        {
            try
            {
                // In a real implementation, you would use Redis's tagging capabilities
                // For this in-memory implementation, we'll just log that it's not implemented
                _logger.LogWarning("Invalidate by tag not implemented for memory cache. This should be implemented in a distributed cache implementation.");
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error invalidating cache by tag {Tag}: {Message}", tag, ex.Message);
            }
        }
    }


}