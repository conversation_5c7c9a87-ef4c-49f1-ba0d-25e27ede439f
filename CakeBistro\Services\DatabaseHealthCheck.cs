using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using System;
using System.Threading.Tasks;

namespace CakeBistro.Services
{
    public class DatabaseHealthCheck : IHealthCheck
    {
        private readonly IConfiguration _configuration;
        private readonly ILogger<DatabaseHealthCheck> _logger;

        public DatabaseHealthCheck(IConfiguration configuration, ILogger<DatabaseHealthCheck> logger)
        {
            _configuration = configuration;
            _logger = logger;
        }

        public async Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken cancellationToken = default)
        {
            try
            {
                // For SQLite, we can check if we can open the database file
                var connectionString = _configuration.GetConnectionString("DefaultConnection");
                
                if (string.IsNullOrEmpty(connectionString))
                {
                    _logger.LogError("Database connection string is missing");
                    return HealthCheckResult.Unhealthy("Database connection string is missing");
                }

                // Extract the database file path from the connection string
                var dataSourceStart = connectionString.IndexOf("Data Source=") + 11;
                var dataSourceEnd = connectionString.IndexOf(';', dataSourceStart);
                var dataPath = connectionString.Substring(dataSourceStart, dataSourceEnd);

                // Check if the database file exists
                if (!System.IO.File.Exists(dataPath))
                {
                    _logger.LogError($"Database file not found at {dataPath}");
                    return HealthCheckResult.Unhealthy($"Database file not found at {dataPath}");
                }

                // Add a more thorough check by opening the database and running a simple query
                using (var connection = new Microsoft.Data.Sqlite.SqliteConnection(connectionString))
                {
                    await connection.OpenAsync(cancellationToken);
                    
                    // Run a simple query to verify database accessibility
                    using (var command = connection.CreateCommand())
                    {
                        command.CommandText = "SELECT COUNT(*) FROM sqlite_master WHERE type='table' AND name='Products'";
                        var result = await command.ExecuteScalarAsync(cancellationToken);
                        
                        if (result == null || Convert.ToInt32(result) == 0)
                        {
                            _logger.LogError("Products table not found in database");
                            return HealthCheckResult.Unhealthy("Products table not found in database");
                        }
                    }
                }

                return HealthCheckResult.Healthy("Database is available and accessible");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Database health check failed: {Message}", ex.Message);
                return HealthCheckResult.Unhealthy($"Database health check failed: {ex.Message}");
            }
        }
    }
}