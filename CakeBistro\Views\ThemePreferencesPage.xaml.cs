
using Microsoft.Maui.Controls;
using CakeBistro.Services;
using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace CakeBistro.Views
{
    /// <summary>
    /// Page for managing theme preferences
    /// </summary>
    public partial class ThemePreferencesPage : ContentPage, IThemable
    {
        private readonly IThemeService _themeService;
        private ThemeConfiguration _currentTheme;
        private ThemePreferences _preferences;
        
        public ThemePreferencesPage(IThemeService themeService)
        {
            InitializeComponent();
            _themeService = themeService ?? throw new ArgumentNullException(nameof(themeService));
            
            // Initialize the page
            InitializeAsync();
        }

        /// <summary>
        /// Initializes the page asynchronously
        /// </summary>
        private async void InitializeAsync()
        {
            try
            {
                // Get current theme and preferences
                _currentTheme = await _themeService.GetCurrentThemeAsync();
                _preferences = await _themeService.GetThemePreferencesAsync() ?? new ThemePreferences();
                
                // Set binding context
                BindingContext = this;
            }
            catch (Exception ex)
            {
                // Handle error during initialization
                await DisplayAlert("Error", $"Failed to load theme preferences: {ex.Message}", "OK");
            }
        }

        /// <inheritdoc />
        public void ApplyTheme(ThemeConfiguration theme)
        {
            if (theme != null)
            {
                // Update UI with new theme
                CurrentTheme = theme;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// Gets or sets the current theme configuration
        /// </summary>
        public ThemeConfiguration CurrentTheme
        {
            get => _currentTheme;
            set
            {
                if (_currentTheme != value)
                {
                    _currentTheme = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// Gets or sets the theme preferences
        /// </summary>
        public ThemePreferences ThemePreferences
        {
            get => _preferences;
            set
            {
                if (_preferences != value)
                {
                    _preferences = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// Event handler for when the user changes a preference
        /// </summary>
        private async void OnPreferenceChanged(object sender, EventArgs e)
        {
            try
            {
                // Save updated preferences
                await _themeService.SaveThemePreferencesAsync(_preferences);
                
                // Show success message
                await DisplayAlert("Success", "Theme preferences saved successfully.", "OK");
            }
            catch (Exception ex)
            {
                // Handle error
                await DisplayAlert("Error", $"Failed to save theme preferences: {ex.Message}", "OK");
            }
        }

        /// <summary>
        /// Event handler for when the user wants to reset preferences
        /// </summary>
        private async void OnResetPreferencesClicked(object sender, EventArgs e)
        {
            var confirm = await DisplayAlert(
                "Reset Preferences",
                "Are you sure you want to reset all preferences to default? This action cannot be undone.",
                "Reset",
                "Cancel");
            
            if (confirm)
            {
                // Reset preferences to default
                var defaultPreferences = new ThemePreferences
                {
                    PreferredTheme = "Default",
                    UseDarkMode = false,
                    PreferredAccentColor = "#FF6F00"
                };
                
                // Save default preferences
                await _themeService.SaveThemePreferencesAsync(defaultPreferences);
                
                // Refresh the page
                await InitializeAsync();
                
                // Show success message
                await DisplayAlert("Success", "Preferences have been reset to default.", "OK");
            }
        }

    }
}