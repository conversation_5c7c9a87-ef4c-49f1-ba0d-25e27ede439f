<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:vm="clr-namespace:CakeBistro.ViewModels"
             x:Class="CakeBistro.Views.DamageTrackingPage"
             Title="Damage Tracking">
    <ContentPage.BindingContext>
        <vm:DamageTrackingViewModel />
    </ContentPage.BindingContext>
    <ScrollView>
        <VerticalStackLayout Padding="20">
            <Label Text="Damage Tracking" FontSize="24" HorizontalOptions="Center" />
            <Picker Title="Select Batch" ItemsSource="{Binding Batches}" ItemDisplayBinding="{Binding Id}" SelectedItem="{Binding SelectedBatch}" />
            <Entry Placeholder="Damage Quantity" Text="{Binding DamageQuantity}" Keyboard="Numeric" />
            <Editor Placeholder="Notes (optional)" Text="{Binding DamageNotes}" />
            <Button Text="Record Damage" Command="{Binding RecordDamageCommand}" />
        </VerticalStackLayout>
    </ScrollView>
</ContentPage>
