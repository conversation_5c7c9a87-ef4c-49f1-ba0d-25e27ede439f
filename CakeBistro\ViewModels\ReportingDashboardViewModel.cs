
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using CakeBistro.Services;
using System.Threading.Tasks;
using Microcharts;
using SkiaSharp;

namespace CakeBistro.ViewModels
{
public partial class ReportingDashboardViewModel : ObservableObject
{
    // Parameterless constructor for XAML designer support only
    public ReportingDashboardViewModel() : this(null!, null!)
    {
        // Allow instantiation in XAML designer, but throw at runtime only
        if (!IsInDesignMode())
            throw new InvalidOperationException("ReportingDashboardViewModel parameterless constructor is for XAML designer only. Use dependency injection at runtime.");
    }

    private static bool IsInDesignMode()
    {
#if WINDOWS
        return System.ComponentModel.DesignerProperties.GetIsInDesignMode(new System.Windows.DependencyObject());
#else
        return false;
#endif
    }
    [ObservableProperty]
    private decimal totalAssets;

    private readonly FinanceService _financeService;
    private readonly ReportingService _reportingService;
    [ObservableProperty]
    private decimal netIncome;
    [ObservableProperty]
    private decimal inventoryValue;
    [ObservableProperty]
    private int lowStockCount;

    [ObservableProperty]
    private Chart? kpiChart;


    [ObservableProperty]
    private Chart? trendChart;

    [ObservableProperty]
    private Chart? topProductsChart;

    [ObservableProperty]
    private Chart? salesByCategoryChart;

    [ObservableProperty]
    private DateTime startDate = DateTime.Today.AddDays(-30);
    [ObservableProperty]
    private DateTime endDate = DateTime.Today;

    public IRelayCommand GoToFinancialReportsCommand { get; }
    public IRelayCommand GoToInventoryReportsCommand { get; }
    public IRelayCommand RefreshTrendsCommand { get; }
    public IRelayCommand ExportTrendChartCommand { get; }
    public IRelayCommand ExportTopProductsChartCommand { get; }
    public IRelayCommand ExportSalesByCategoryChartCommand { get; }

    public ReportingDashboardViewModel(
        FinanceService financeService,
        ReportingService reportingService)
    {
        _financeService = financeService;
        _reportingService = reportingService;
        GoToFinancialReportsCommand = new RelayCommand(GoToFinancialReports);
        GoToInventoryReportsCommand = new RelayCommand(GoToInventoryReports);
        RefreshTrendsCommand = new RelayCommand(async () => await LoadTrendChartAsync());
        ExportTrendChartCommand = new RelayCommand(ExportTrendChartAsync);
        ExportTopProductsChartCommand = new RelayCommand(ExportTopProductsChartAsync);
        ExportSalesByCategoryChartCommand = new RelayCommand(ExportSalesByCategoryChartAsync);
        LoadDashboardDataAsync();
        LoadTrendChartAsync();
        LoadTopProductsChartAsync();
        LoadSalesByCategoryChartAsync();
    }

    private async void LoadDashboardDataAsync()
    {
        var financial = await _financeService.GenerateBalanceSheetAsync(DateTime.Today);
        TotalAssets = financial.Assets;
        var incomeStatement = await _financeService.GenerateIncomeStatementAsync(DateTime.Today.AddMonths(-1), DateTime.Today);
        NetIncome = incomeStatement.NetIncome;
        var inventory = await _reportingService.GenerateInventoryValuationReportAsync();
        InventoryValue = inventory.TotalInventoryValue;
        LowStockCount = inventory.LowStockItemCount;

        // Build KPI chart
        KpiChart = new Microcharts.DonutChart
        {
            Entries = new[]
            {
                new Microcharts.ChartEntry((float)TotalAssets)
                {
                    Label = "Assets",
                    ValueLabel = TotalAssets.ToString("C0"),
                    Color = SKColor.Parse("#2ecc71"),
                },
                new Microcharts.ChartEntry((float)NetIncome)
                {
                    Label = "Net Income",
                    ValueLabel = NetIncome.ToString("C0"),
                    Color = SKColor.Parse("#3498db"),
                },
                new Microcharts.ChartEntry((float)InventoryValue)
                {
                    Label = "Inventory",
                    ValueLabel = InventoryValue.ToString("C0"),
                    Color = SKColor.Parse("#f1c40f"),
                },
                new Microcharts.ChartEntry((float)LowStockCount)
                {
                    Label = "Low Stock",
                    ValueLabel = LowStockCount.ToString(),
                    Color = SKColor.Parse("#e74c3c"),
                },
            },
        };
    }

    private async Task LoadTrendChartAsync()
    {
        // Example: show Net Income trend for the selected date range (daily)
        var entries = new List<Microcharts.ChartEntry>();
        var days = (EndDate - StartDate).Days;
        for (int i = 0; i <= days; i++)
        {
            var day = StartDate.AddDays(i);
            var incomeStatement = await _financeService.GenerateIncomeStatementAsync(day, day);
            var income = incomeStatement.NetIncome;
            entries.Add(new Microcharts.ChartEntry((float)income)
            {
                Label = day.ToString("MM-dd"),
                ValueLabel = income.ToString("C0"),
                Color = SKColor.Parse("#3498db"),
            });
        }
        TrendChart = new Microcharts.LineChart
        {
            Entries = entries,
            LineMode = LineMode.Straight,
            LineSize = 4,
            PointMode = PointMode.Circle,
            PointSize = 8
        };
    }

    private async Task LoadTopProductsChartAsync()
    {
        // Example: show top 5 selling products in the selected date range
        var topProducts = await _reportingService.GetTopSellingProductsAsync(5);
        var entries = topProducts.Select(p => new Microcharts.ChartEntry((float)p.TotalSold)
        {
            Label = p.Name,
            ValueLabel = p.TotalSold.ToString(),
            Color = SKColor.Parse("#8e44ad")
        }).ToList();
        TopProductsChart = new Microcharts.BarChart
        {
            Entries = entries,
            LabelTextSize = 28,
            ValueLabelOrientation = Orientation.Horizontal
        };
    }

    private async Task LoadSalesByCategoryChartAsync()
    {
        // Example: show sales by category as a pie chart
        // If GetSalesByCategoryAsync is not implemented, stub with empty chart
        // No implementation for GetSalesByCategoryAsync; stub with empty chart
        SalesByCategoryChart = new Microcharts.PieChart { Entries = new List<Microcharts.ChartEntry>() };
    }

    private void ExportTrendChartAsync()
    {
        // Example: export trend chart as image (PNG)
        if (TrendChart == null) return;
        // Microcharts does not provide GetImage directly; use SkiaSharp to render
        var chart = TrendChart;
        using (var surface = SKSurface.Create(new SKImageInfo(800, 400)))
        {
            chart.DrawContent(surface.Canvas, 800, 400);
            using (var image = surface.Snapshot())
            using (var data = image.Encode(SKEncodedImageFormat.Png, 100))
            {
                var fileName = $"TrendChart_{StartDate:yyyyMMdd}_{EndDate:yyyyMMdd}.png";
                var filePath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments), fileName);
                using (var stream = File.OpenWrite(filePath))
                {
                    data.SaveTo(stream);
                }
            }
        }
        // Optionally, show a message to the user (implementation depends on your app's notification system)
    }

    private void ExportTopProductsChartAsync()
    {
        if (TopProductsChart == null) return;
        var chart = TopProductsChart;
        using (var surface = SKSurface.Create(new SKImageInfo(800, 400)))
        {
            chart.DrawContent(surface.Canvas, 800, 400);
            using (var image = surface.Snapshot())
            using (var data = image.Encode(SKEncodedImageFormat.Png, 100))
            {
                var fileName = $"TopProductsChart_{StartDate:yyyyMMdd}_{EndDate:yyyyMMdd}.png";
                var filePath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments), fileName);
                using (var stream = File.OpenWrite(filePath))
                {
                    data.SaveTo(stream);
                }
            }
        }
    }

    private void ExportSalesByCategoryChartAsync()
    {
        if (SalesByCategoryChart == null) return;
        var chart = SalesByCategoryChart;
        using (var surface = SKSurface.Create(new SKImageInfo(800, 400)))
        {
            chart.DrawContent(surface.Canvas, 800, 400);
            using (var image = surface.Snapshot())
            using (var data = image.Encode(SKEncodedImageFormat.Png, 100))
            {
                var fileName = $"SalesByCategoryChart_{StartDate:yyyyMMdd}_{EndDate:yyyyMMdd}.png";
                var filePath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments), fileName);
                using (var stream = File.OpenWrite(filePath))
                {
                    data.SaveTo(stream);
                }
            }
        }
    }

    private async void GoToFinancialReports()
    {
        await Shell.Current.GoToAsync("//financialreports");
    }

    private async void GoToInventoryReports()
    {
        await Shell.Current.GoToAsync("//reporting");
    }
    }
// Add missing closing brace for namespace
}
