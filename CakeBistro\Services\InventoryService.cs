using System;
using System.Collections.Generic;

namespace CakeBistro.Core.Models
{
    /// <summary>
    /// Represents a paginated result set
    /// </summary>
    /// <typeparam name="T">Type of items in the result</typeparam>
    public class PaginatedResult<T>
    {
        /// <summary>
        /// Gets or sets the current page number
        /// </summary>
        public int Page { get; set; }
        
        /// <summary>
        /// Gets or sets the number of items per page
        /// </summary>
        public int PageSize { get; set; }
        
        /// <summary>
        /// Gets or sets the total number of items
        /// </summary>
        public int TotalCount { get; set; }
        
        /// <summary>
        /// Gets or sets the items for this page
        /// </summary>
        public List<T> Items { get; set; } = new List<T>();
        
        /// <summary>
        /// Calculates the total number of pages
        /// </summary>
        public int TotalPages => (int)Math.Ceiling(TotalCount / (double)PageSize);
        
        /// <summary>
        /// Indicates if there is a previous page
        /// </summary>
        public bool HasPreviousPage => Page > 1;
        
        /// <summary>
        /// Indicates if there is a next page
        /// </summary>
        public bool HasNextPage => Page < TotalPages;
    }
}
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging; // Added for logging support
using CakeBistro.Core.Interfaces;
using CakeBistro.Core.Models;
using CakeBistro.Core.Exceptions; // Import namespace for custom exceptions
using CakeBistro.Services.Interfaces; // Add this line if IInventoryService is defined elsewhere
using CakeBistro.Repositories; // Assuming Repository<T> is moved here

namespace CakeBistro.Services
{
    /// <summary>
    /// Enhanced InventoryService that implements PRD 4.1 Store Management requirements
    /// using Entity Framework Core for database persistence.
    /// Provides comprehensive inventory management functionality including raw material
    /// management, stock movement tracking, and reporting capabilities.
    /// </summary>
    public class InventoryService : BaseService<RawMaterial>, IInventoryService
    {
        private readonly CakeBistroContext _context;
        private readonly ILogger<InventoryService> _logger; // Added logger field

        /// <summary>
        /// Initializes a new instance of the <see cref="InventoryService"/> class.
        /// </summary>
        /// <param name="context">The database context to use for inventory operations</param>
        /// <param name="logger">The logger to use for logging</param>
        public InventoryService(CakeBistroContext context, ILogger<InventoryService> logger) 
            : base(new Repository<RawMaterial>(context))
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// Registers a new raw material in the system with validation of required fields.
        /// Implements PRD 4.1 requirement for cataloging production items.
        /// </summary>
        /// <param name="rawMaterial">The raw material to register</param>
        /// <returns>The registered raw material with system-assigned properties</returns>
        /// <exception cref="BusinessRuleValidationException">
        /// Thrown when validation fails:
        /// - Raw material object is null (ruleName: "RawMaterialNullCheck")
        /// - Name is empty or whitespace (ruleName: "RawMaterialNameRequired")
        /// - Unit is empty or whitespace (ruleName: "RawMaterialUnitRequired")
        /// </exception>
        public async Task<RawMaterial> RegisterRawMaterialAsync(RawMaterial rawMaterial)
        {
            // Validate input parameters using base class helpers
            CheckEntityExists(rawMaterial, "RawMaterialNullCheck", "Raw material cannot be null");
            ValidateString(rawMaterial.Name, "RawMaterialNameRequired", "Raw material name is required");
            ValidateString(rawMaterial.Unit, "RawMaterialUnitRequired", "Unit of measure is required");

            // Set audit fields
            rawMaterial.CreatedDate = DateTime.UtcNow;
            rawMaterial.UpdatedDate = DateTime.UtcNow;

            // Validate minimum stock threshold
            if (rawMaterial.MinimumStockThreshold <= 0)
                throw new BusinessRuleValidationException(
                    "InvalidMinimumStockThreshold", 
                    "Minimum stock threshold must be greater than zero");

            // Set default values if not provided
            rawMaterial.CurrentStock = rawMaterial.CurrentStock > 0 ? rawMaterial.CurrentStock : 0;
            rawMaterial.MinimumStock = rawMaterial.MinimumStock > 0 ? rawMaterial.MinimumStock : 10;

            await _repository.AddAsync(rawMaterial);
            await _context.SaveChangesAsync();
            
            return rawMaterial;
        }

        /// <summary>
        /// Retrieves all raw materials from the system with their associated movements and batches.
        /// </summary>
        /// <returns>An enumerable collection of raw materials</returns>
        public async Task<IEnumerable<RawMaterial>> GetAllRawMaterialsAsync()
        {
            var materials = await _repository.GetAllAsync();
            return materials ?? Enumerable.Empty<RawMaterial>();
        }

        /// <summary>
        /// Retrieves a specific raw material by its unique identifier.
        /// </summary>
        /// <param name="id">The unique identifier of the raw material</param>
        /// <returns>The raw material if found</returns>
        /// <exception cref="BusinessRuleValidationException">
        /// Thrown when the raw material with specified ID is not found
        /// ruleName: "RawMaterialNotFound"
        /// </exception>
        public async Task<RawMaterial?> GetRawMaterialByIdAsync(int id)
        {
            var material = await _repository.GetByIdAsync(id);
            CheckEntityExists(material, "RawMaterialNotFound", $"Raw material with ID {id} was not found");
            
            return material;
        }

        /// <summary>
        /// Updates an existing raw material's information after validating it exists.
        /// </summary>
        /// <param name="rawMaterial">The updated raw material information</param>
        /// <returns>True if the update was successful, false otherwise</returns>
        public async Task<bool> UpdateRawMaterialAsync(RawMaterial rawMaterial)
        {
            // Validate input parameter
            CheckEntityExists(rawMaterial, "RawMaterialNullCheck", "Raw material cannot be null");

            var existing = await _repository.GetByIdAsync(rawMaterial.Id);
            if (existing == null)
                return false;

            // Update properties
            // Validate minimum stock threshold
            if (rawMaterial.MinimumStockThreshold <= 0)
                throw new BusinessRuleValidationException(
                    "InvalidMinimumStockThreshold", 
                    "Minimum stock threshold must be greater than zero");

            existing.Name = rawMaterial.Name;
            existing.Unit = rawMaterial.Unit;
            existing.PricePerUnit = rawMaterial.PricePerUnit;
            existing.MinimumStockThreshold = rawMaterial.MinimumStockThreshold;
            existing.CurrentStock = rawMaterial.CurrentStock;
            existing.MinimumStock = rawMaterial.MinimumStock;
            existing.Notes = rawMaterial.Notes;
            existing.UpdatedDate = DateTime.UtcNow;

            await _repository.UpdateAsync(existing);
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> DeleteRawMaterialAsync(int id)
        {
            var material = await _repository.GetByIdAsync(id);
            if (material == null)
                return false;

            // Check if there are any stock movements or batches associated
            var hasMovements = await _context.StockMovements.AnyAsync(sm => sm.RawMaterialId == id);
            var hasBatches = await _context.InventoryBatches.AnyAsync(ib => ib.RawMaterialId == id);

            if (hasMovements || hasBatches)
            {
                throw new BusinessRuleValidationException(
                    "RawMaterialHasDependencies",
                    $"Cannot delete raw material with existing stock movements or batches: {id}");
            }

            await _repository.DeleteAsync(id);
            await _context.SaveChangesAsync();
            return true;
        }

        #endregion

        #region PRD 4.1: Track Stock Movement

        /// <summary>
        /// PRD 4.1: "Track Stock Movement: Accurately capture incoming raw material stock and outgoing stock"
        /// </summary>
        public async Task<StockMovement> RecordStockMovementAsync(StockMovement stockMovement)
        {
            try
            {
                return await ExecuteWithRetry(async () =>
                {
                    if (stockMovement == null)
                        throw new BusinessRuleValidationException("StockMovementNullCheck", "Stock movement cannot be null");

                    // Validate required fields
                    if (stockMovement.RawMaterialId <= 0)
                        throw new BusinessRuleValidationException("RawMaterialIdRequired", "Valid raw material ID is required");

                    if (stockMovement.Quantity <= 0)
                        throw new BusinessRuleValidationException("QuantityMustBePositive", "Quantity must be greater than zero");

            // Find the raw material
            var material = await _repository.GetByIdAsync(stockMovement.RawMaterialId);
            if (material == null)
                throw new BusinessRuleValidationException("RawMaterialNotFound", $"Raw material not found: {stockMovement.RawMaterialId}");

            // Set movement date if not provided
            if (stockMovement.MovementDate == default)
                stockMovement.MovementDate = DateTime.UtcNow;

            // Generate reference number if not provided
            if (string.IsNullOrEmpty(stockMovement.ReferenceNumber))
                stockMovement.ReferenceNumber = $"MOV-{DateTime.Now:yyyyMMdd-HHmmss}";

            // Update material stock based on movement type
            if (stockMovement.MovementType == MovementType.Incoming)
            {
                material.CurrentStock += (int)stockMovement.Quantity;
            }
            else if (stockMovement.MovementType == MovementType.Outgoing)
            {
                if (material.CurrentStock < stockMovement.Quantity)
                    throw new BusinessRuleValidationException(
                        "InsufficientStock",
                        $"Insufficient stock for {material.Name}. Available: {material.CurrentStock}, Requested: {stockMovement.Quantity}");
            
                material.CurrentStock -= (int)stockMovement.Quantity;
            }

            // Update last stock take date
            material.LastStockTake = DateTime.UtcNow;
            material.UpdatedDate = DateTime.UtcNow;

            _context.StockMovements.Add(stockMovement);
            await _context.SaveChangesAsync();
            
            return stockMovement;
        }

        public async Task<IEnumerable<StockMovement>> GetStockMovementsAsync(int? rawMaterialId = null, DateTime? fromDate = null, DateTime? toDate = null)
        {
            try
            {
                return await ExecuteWithRetry(async () =>
                {
                    var query = _context.StockMovements.AsQueryable();

                    if (rawMaterialId.HasValue)
                        query = query.Where(sm => sm.RawMaterialId == rawMaterialId.Value);

                    if (fromDate.HasValue)
                        query = query.Where(sm => sm.MovementDate >= fromDate.Value);

                    if (toDate.HasValue)
                        query = query.Where(sm => sm.MovementDate <= toDate.Value);

                    return await query.OrderByDescending(sm => sm.MovementDate).ToListAsync();
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting stock movements: {Message}", ex.Message);
                throw;
            }
        }

        #endregion

        #region PRD 4.1: Generate Reports

        /// <summary>
        /// PRD 4.1: "Generate detailed stock movement reports and raw material stock statements"
        /// </summary>
        public async Task<IEnumerable<CakeBistro.Core.Models.RawMaterial>> GenerateStockReportAsync()
        {
            return await _context.RawMaterials
                .Include(rm => rm.StockMovements)
                .Include(rm => rm.InventoryBatches)
                .OrderBy(rm => rm.Name)
                .ToListAsync();
        }

        public async Task<object> GenerateStockStatementAsync(int rawMaterialId, DateTime startDate, DateTime endDate)
        {
            var material = await _context.RawMaterials
                .Include(rm => rm.StockMovements.Where(sm => sm.MovementDate >= startDate && sm.MovementDate <= endDate))
                .FirstOrDefaultAsync(rm => rm.Id == rawMaterialId);

            if (material == null)
                throw new ArgumentException("Raw material not found", nameof(rawMaterialId));

            var movements = material.StockMovements.OrderBy(sm => sm.MovementDate).ToList();
            
            var openingStock = await GetStockAtDateAsync(rawMaterialId, startDate);
            var totalIncoming = movements.Where(m => m.MovementType == MovementType.Incoming).Sum(m => m.Quantity);
            var totalOutgoing = movements.Where(m => m.MovementType == MovementType.Outgoing).Sum(m => m.Quantity);
            var closingStock = openingStock + totalIncoming - totalOutgoing;

            return new
            {
                Material = material,
                Period = new { StartDate = startDate, EndDate = endDate },
                OpeningStock = openingStock,
                TotalIncoming = totalIncoming,
                TotalOutgoing = totalOutgoing,
                ClosingStock = closingStock,
                Movements = movements
            };
        }

        private async Task<decimal> GetStockAtDateAsync(int rawMaterialId, DateTime date)
        {
            var movements = await _context.StockMovements
                .Where(sm => sm.RawMaterialId == rawMaterialId && sm.MovementDate < date)
                .ToListAsync();

            var totalIncoming = movements.Where(m => m.MovementType == MovementType.Incoming).Sum(m => m.Quantity);
            var totalOutgoing = movements.Where(m => m.MovementType == MovementType.Outgoing).Sum(m => m.Quantity);

            return totalIncoming - totalOutgoing;
        }

        #endregion

        #region Dashboard and Analytics

        public async Task<PaginatedResult<RawMaterial>> GetLowStockItemsAsync(int threshold = 10, int page = 1, int pageSize = 20)
        {
            try
            {
                var query = _context.RawMaterials
                    .Where(rm => rm.CurrentStock <= rm.MinimumStock || 
                               rm.CurrentStock <= rm.MinimumStock + threshold)
                    .OrderBy(rm => rm.CurrentStock)
                    .ThenBy(rm => rm.Name);
                
                var totalCount = await query.CountAsync();
                
                var items = await query
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .ToListAsync();
                
                return new PaginatedResult<RawMaterial>
                {
                    Items = items,
                    TotalCount = totalCount,
                    Page = page,
                    PageSize = pageSize
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting low stock items with pagination: {Message}", ex.Message);
                throw;
            }
        }

        public async Task<int> GetTotalRawMaterialsCountAsync()
        {
            return await _context.RawMaterials.CountAsync();
        }

        public async Task<decimal> GetTotalInventoryValueAsync()
        {
            return await _context.RawMaterials
                .Where(rm => rm.CurrentStock > 0)
                .SumAsync(rm => rm.CurrentStock * rm.PricePerUnit);
        }

        public async Task<IEnumerable<InventoryBatch>> GetExpiringBatchesAsync(int daysAhead = 30)
        {
            var cutoffDate = DateTime.Today.AddDays(daysAhead);
            return await _context.InventoryBatches
                .Include(ib => ib.CakeBistro.Core.Models.RawMaterial)
                .Where(ib => ib.ExpiryDate <= cutoffDate && ib.Quantity > 0)
                .OrderBy(ib => ib.ExpiryDate)
                .ToListAsync();
        }

        public async Task<int> GetLowStockItemsCountAsync()
        {
            try
            {
                return await ExecuteWithRetry(async () =>
                    await _context.RawMaterials
                        .CountAsync(rm => rm.CurrentStock <= rm.MinimumStock));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting low stock items count: {Message}", ex.Message);
                throw;
            }
        }

        public async Task<int> GetStockMovementsCountAsync()
        {
            try
            {
                return await ExecuteWithRetry(async () => 
                    await _context.StockMovements.CountAsync());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting stock movements count: {Message}", ex.Message);
                throw;
            }
        }

        public async Task<int> GetInterBranchTransfersCountAsync()
        {
            try
            {
                return await ExecuteWithRetry(async () => 
                    await _context.InterBranchTransfers.CountAsync());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting inter-branch transfers count: {Message}", ex.Message);
                throw;
            }
        }

        public async Task<int> GetMonthlyStockTakeCountAsync()
        {
            try
            {
                return await ExecuteWithRetry(async () =>
                {
                    var now = DateTime.UtcNow;
                    return await _context.StockTakes.CountAsync(st => 
                        st.Date.Year == now.Year && st.Date.Month == now.Month);
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting monthly stock take count: {Message}", ex.Message);
                throw;
            }
        }

        public async Task<int> GetStockAdjustmentsCountAsync()
        {
            try
            {
                return await ExecuteWithRetry(async () => 
                    await _context.StockAdjustments.CountAsync());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting stock adjustments count: {Message}", ex.Message);
                throw;
            }
        }

        #endregion

        #region Private Helper Methods for Retry and Logging

        /// <summary>
        /// Executes the provided async action with retry logic for transient failures.
        /// </summary>
        /// <typeparam name="T">The return type of the operation</typeparam>
        /// <param name="action">The async function to execute</param>
        /// <param name="maxRetries">Maximum number of retry attempts</param>
        /// <param name="delayMs">Delay between retries in milliseconds</param>
        /// <returns>The result of the operation</returns>
        private async Task<T> ExecuteWithRetry<T>(Func<Task<T>> action, int maxRetries = 3, int delayMs = 500)
        {
            var retryCount = 0;
            while (true)
            {
                try
                {
                    return await action();
                }
                catch (Exception ex) when (IsTransientException(ex))
                {
                    _logger.LogWarning("Transient error encountered. Retrying... Attempt {RetryCount}/{MaxRetries}", 
                        retryCount + 1, maxRetries);
                    
                    retryCount++;
                    
                    if (retryCount >= maxRetries)
                        throw;

                    await Task.Delay(delayMs);
                }
            }
        }

        /// <summary>
        /// Determines if an exception is transient (i.e., worth retrying).
        /// </summary>
        /// <param name="ex">The exception to check</param>
        /// <returns>True if the exception is transient, false otherwise</returns>
        private bool IsTransientException(Exception ex)
        {
            // Check for common transient database errors
            if (ex is Microsoft.EntityFrameworkCore.DbUpdateException dbUpdateEx &&
                dbUpdateEx.InnerException is System.Data.Common.DbException)
            {
                return true;
            }

            // Check for SQL Server specific transient errors
            if (ex is System.Data.SqlClient.SqlException sqlEx)
            {
                foreach (System.Data.SqlClient.SqlError error in sqlEx.Errors)
                {
                    if (new[] { 
                            121,  // The semaphore timeout period has expired
                            233,  // The connection for the pool was not available
                            644,  // Connection reset by server
                            10053, // Software caused connection abort
                            10054, // Connection reset by peer
                            10060, // A connection attempt failed
                            10928, // The service has encountered an error processing your request
                            10929, // Please retry your request
                            40197, // The service has encountered an error processing your request
                            40501, // The service is currently busy
                            40613, // Database XXXX on server XXXX is not currently available
                            42252, // The session limit for the pool was reached
                            49918, // Cannot open database
                            49919  // Login failed for user
                        }.Contains(error.Number))
                    {
                        return true;
                    }
                }
            }

            return false;
        }

        #endregion

        #region Legacy Support Methods (for backward compatibility)
        
        // These methods provide backward compatibility with existing ViewModels
        public async Task<CakeBistro.Core.Models.RawMaterial> AddRawMaterialAsync(CakeBistro.Core.Models.RawMaterial material)
        {
            return await RegisterRawMaterialAsync(material);
        }

        public async Task<List<CakeBistro.Core.Models.RawMaterial>> GetAllRawMaterialsAsync()
        {
            var materials = await GetAllRawMaterialsAsync();
            return materials.ToList();
        }

        public async Task<List<CakeBistro.Core.Models.Supplier>> GetAllSuppliersAsync()
        {
            return await _context.Suppliers.ToListAsync();
        }

        public async Task<IEnumerable<CakeBistro.Core.Models.RawMaterial>> GetLowStockItemsAsync(int threshold)
        {
            return await GetLowStockItemsAsync(threshold);
        }

        #endregion

        #region IBaseService Implementation Placeholders (to be implemented later)
        public Task<CakeBistro.Core.Models.RawMaterial> GetByIdAsync(Guid id) => throw new NotImplementedException("Use GetRawMaterialByIdAsync instead");
        public Task<IEnumerable<CakeBistro.Core.Models.RawMaterial>> GetAllAsync() => GetAllRawMaterialsAsync();
        public Task<CakeBistro.Core.Models.RawMaterial> CreateAsync(CakeBistro.Core.Models.RawMaterial entity) => throw new NotImplementedException("Use RegisterRawMaterialAsync instead");
        public Task<CakeBistro.Core.Models.RawMaterial> UpdateAsync(CakeBistro.Core.Models.RawMaterial entity) => throw new NotImplementedException("Use UpdateRawMaterialAsync instead");
        public Task DeleteAsync(Guid id) => throw new NotImplementedException("Use DeleteRawMaterialAsync instead");
        public Task<bool> ExistsAsync(Guid id) => throw new NotImplementedException();
        #endregion

        // Stock Take CRUD
        public async Task<StockTake> AddStockTakeAsync(StockTake stockTake)
        {
            if (stockTake == null)
                throw new ArgumentNullException(nameof(stockTake));
            _context.StockTakes.Add(stockTake);
            await _context.SaveChangesAsync();
            return stockTake;
        }

        public async Task<bool> UpdateStockTakeAsync(StockTake stockTake)
        {
            if (stockTake == null)
                return false;
            var existing = await _context.StockTakes.FindAsync(stockTake.Id);
            if (existing == null)
                return false;
            existing.Date = stockTake.Date;
            existing.Notes = stockTake.Notes;
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> DeleteStockTakeAsync(int id)
        {
            var stockTake = await _context.StockTakes.FindAsync(id);
            if (stockTake == null)
                return false;
            _context.StockTakes.Remove(stockTake);
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<StockTake?> GetStockTakeByIdAsync(int id)
        {
            return await _context.StockTakes.FindAsync(id);
        }

        // Stock Adjustment CRUD
        public async Task<StockAdjustment> AddStockAdjustmentAsync(StockAdjustment stockAdjustment)
        {
            if (stockAdjustment == null)
                throw new ArgumentNullException(nameof(stockAdjustment));
            _context.StockAdjustments.Add(stockAdjustment);
            await _context.SaveChangesAsync();
            return stockAdjustment;
        }

        public async Task<bool> UpdateStockAdjustmentAsync(StockAdjustment stockAdjustment)
        {
            if (stockAdjustment == null)
                return false;
            var existing = await _context.StockAdjustments.FindAsync(stockAdjustment.Id);
            if (existing == null)
                return false;
            existing.Date = stockAdjustment.Date;
            existing.Reason = stockAdjustment.Reason;
            existing.Notes = stockAdjustment.Notes;
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> DeleteStockAdjustmentAsync(int id)
        {
            var stockAdjustment = await _context.StockAdjustments.FindAsync(id);
            if (stockAdjustment == null)
                return false;
            _context.StockAdjustments.Remove(stockAdjustment);
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<StockAdjustment?> GetStockAdjustmentByIdAsync(int id)
        {
            return await _context.StockAdjustments.FindAsync(id);
        }

        #region PRD 4.1: InterBranchTransfer Management

        public async Task<InterBranchTransfer> CreateInterBranchTransferAsync(InterBranchTransfer transfer)
        {
            if (transfer == null)
                throw new ArgumentNullException(nameof(transfer));
            if (transfer.SourceBranchId == transfer.DestinationBranchId)
                throw new ArgumentException("Source and destination branches must be different.");
            if (transfer.Items == null || !transfer.Items.Any())
                throw new ArgumentException("At least one item must be included in the transfer.");

            transfer.Status = InterBranchTransferStatus.Pending;
            transfer.CreatedDate = DateTime.UtcNow;
            transfer.UpdatedDate = DateTime.UtcNow;
            transfer.ReferenceNumber = string.IsNullOrEmpty(transfer.ReferenceNumber) ? $"TRF-{DateTime.Now:yyyyMMdd-HHmmss}" : transfer.ReferenceNumber;

            // Deduct stock from source branch (if multi-branch stock is implemented)
            foreach (var item in transfer.Items)
            {
                var material = await _context.RawMaterials.FindAsync(item.RawMaterialId);
                if (material == null)
                    throw new ArgumentException($"Raw material not found: {item.RawMaterialId}");
                if (material.CurrentStock < item.Quantity)
                    throw new InvalidOperationException($"Insufficient stock for {material.Name}. Available: {material.CurrentStock}, Requested: {item.Quantity}");
                material.CurrentStock -= (int)item.Quantity;
                material.UpdatedDate = DateTime.UtcNow;
            }

            _context.InterBranchTransfers.Add(transfer);
            await _context.SaveChangesAsync();
            return transfer;
        }

        public async Task<bool> UpdateInterBranchTransferStatusAsync(int transferId, InterBranchTransferStatus status)
        {
            var transfer = await _context.InterBranchTransfers.Include(t => t.Items).FirstOrDefaultAsync(t => t.Id == transferId);
            if (transfer == null)
                return false;
            transfer.Status = status;
            transfer.UpdatedDate = DateTime.UtcNow;
            if (status == InterBranchTransferStatus.Completed)
            {
                // Add stock to destination branch (if multi-branch stock is implemented)
                foreach (var item in transfer.Items)
                {
                    var material = await _context.RawMaterials.FindAsync(item.RawMaterialId);
                    if (material != null)
                    {
                        material.CurrentStock += (int)item.Quantity;
                        material.UpdatedDate = DateTime.UtcNow;
                    }
                }
                transfer.IsReconciled = true;
                transfer.ReconciledDate = DateTime.UtcNow;
            }
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<InterBranchTransfer?> GetInterBranchTransferByIdAsync(int id)
        {
            return await _context.InterBranchTransfers
                .Include(t => t.Items)
                .Include(t => t.SourceBranch)
                .Include(t => t.DestinationBranch)
                .FirstOrDefaultAsync(t => t.Id == id);
        }

        public async Task<IEnumerable<InterBranchTransfer>> GetAllInterBranchTransfersAsync()
        {
            return await _context.InterBranchTransfers
                .Include(t => t.Items)
                .Include(t => t.SourceBranch)
                .Include(t => t.DestinationBranch)
                .OrderByDescending(t => t.TransferDate)
                .ToListAsync();
        }

        #endregion

        #region PRD 4.1: Raw Material Document Management

        /// <summary>
        /// Uploads a document for a raw material, with validation and error handling.
        /// </summary>
        public async Task<RawMaterialDocument> UploadRawMaterialDocumentAsync(int rawMaterialId, string fileName, string contentType, long fileSize, byte[] fileContent, string category = "", string description = "")
        {
            var material = await _context.RawMaterials.FindAsync(rawMaterialId);
            if (material == null)
                throw new ArgumentException("Raw material not found", nameof(rawMaterialId));

            var document = new RawMaterialDocument
            {
                RawMaterialId = rawMaterialId,
                FileName = fileName,
                ContentType = contentType,
                FileSize = fileSize,
                FileContent = fileContent,
                Category = category,
                Description = description,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };
            _context.RawMaterialDocuments.Add(document);
            await _context.SaveChangesAsync();
            return document;
        }

        /// <summary>
        /// Gets all documents for a raw material.
        /// </summary>
        public async Task<IEnumerable<RawMaterialDocument>> GetRawMaterialDocumentsAsync(int rawMaterialId)
        {
            return await _context.RawMaterialDocuments
                .Where(d => d.RawMaterialId == rawMaterialId)
                .OrderByDescending(d => d.CreatedAt)
                .ToListAsync();
        }

        /// <summary>
        /// Gets a specific document by its ID.
        /// </summary>
        public async Task<RawMaterialDocument?> GetRawMaterialDocumentByIdAsync(int documentId)
        {
            return await _context.RawMaterialDocuments.FindAsync(documentId);
        }

        /// <summary>
        /// Deletes a document by its ID.
        /// </summary>
        public async Task<bool> DeleteRawMaterialDocumentAsync(int documentId)
        {
            var doc = await _context.RawMaterialDocuments.FindAsync(documentId);
            if (doc == null)
                return false;
            _context.RawMaterialDocuments.Remove(doc);
            await _context.SaveChangesAsync();
            return true;
        }

        #endregion

        #region PRD 4.1: Low Stock Alerting

        /// <summary>
        /// Checks for low stock items and triggers an alert callback for each.
        /// </summary>
        /// <param name="thresholdOverride">Optional: override minimum stock threshold for all materials.</param>
        /// <param name="onLowStockAlert">Optional: callback invoked for each low stock item found.</param>
        public async Task<List<RawMaterial>> CheckAndAlertLowStockAsync(int? thresholdOverride = null, Action<RawMaterial>? onLowStockAlert = null)
        {
            var lowStockItems = await _context.RawMaterials
                .Where(rm => rm.CurrentStock <= (thresholdOverride ?? rm.MinimumStock))
                .ToListAsync();
            if (onLowStockAlert != null)
            {
                foreach (var item in lowStockItems)
                {
                    onLowStockAlert(item);
                }
            }
            // Optionally: log, notify, or persist alerts here
            return lowStockItems;
        }

        #endregion

        #region PRD 4.1: Batch Expiry Tracking & Alerts

        /// <summary>
        /// Checks for expiring inventory batches and triggers an alert callback for each.
        /// </summary>
        /// <param name="daysAhead">Number of days ahead to check for expiry (default 30).</param>
        /// <param name="onExpiringBatchAlert">Optional: callback invoked for each expiring batch found.</param>
        public async Task<List<InventoryBatch>> CheckAndAlertExpiringBatchesAsync(int daysAhead = 30, Action<InventoryBatch>? onExpiringBatchAlert = null)
        {
            var cutoffDate = DateTime.Today.AddDays(daysAhead);
            var expiringBatches = await _context.InventoryBatches
                .Include(ib => ib.RawMaterial)
                .Where(ib => ib.ExpiryDate <= cutoffDate && ib.Quantity > 0)
                .OrderBy(ib => ib.ExpiryDate)
                .ToListAsync();
            if (onExpiringBatchAlert != null)
            {
                foreach (var batch in expiringBatches)
                {
                    onExpiringBatchAlert(batch);
                }
            }
            // Optionally: log, notify, or persist alerts here
            return expiringBatches;
        }

        #endregion

        #region PRD 4.1: Monthly Stock Take & Audit Trail

        /// <summary>
        /// Performs a monthly stock take for all raw materials, records adjustments, and logs an audit trail.
        /// </summary>
        /// <param name="stockTakes">List of stock take entries (materialId, counted quantity, notes).</param>
        /// <param name="performedBy">User or system performing the stock take.</param>
        /// <returns>List of StockTake records created.</returns>
        public async Task<List<StockTake>> PerformMonthlyStockTakeAsync(List<(int materialId, int countedQuantity, string notes)> stockTakes, string performedBy)
        {
            var results = new List<StockTake>();
            foreach (var entry in stockTakes)
            {
                var material = await _context.RawMaterials.FindAsync(entry.materialId);
                if (material == null) continue;
                var adjustment = entry.countedQuantity - material.CurrentStock;
                // Record stock take
                var stockTake = new StockTake
                {
                    RawMaterialId = entry.materialId,
                    Date = DateTime.UtcNow,
                    CountedQuantity = entry.countedQuantity,
                    PreviousQuantity = material.CurrentStock,
                    Adjustment = adjustment,
                    Notes = entry.notes,
                    PerformedBy = performedBy
                };
                _context.StockTakes.Add(stockTake);
                // Update material stock
                material.CurrentStock = entry.countedQuantity;
                material.LastStockTake = DateTime.UtcNow;
                material.UpdatedDate = DateTime.UtcNow;
                // Log adjustment if any
                if (adjustment != 0)
                {
                    var stockAdjustment = new StockAdjustment
                    {
                        RawMaterialId = entry.materialId,
                        Date = DateTime.UtcNow,
                        Quantity = adjustment,
                        Reason = "Monthly Stock Take Reconciliation",
                        Notes = entry.notes,
                        PerformedBy = performedBy
                    };
                    _context.StockAdjustments.Add(stockAdjustment);
                }
                results.Add(stockTake);
            }
            await _context.SaveChangesAsync();
            return results;
        }

        #endregion

        #region PRD 4.1: Inventory Reporting & Scheduling

        /// <summary>
        /// Exports the inventory report for the given period.
        /// </summary>
        public async Task<string> ExportInventoryReportAsync(ReportType reportType, DateTime startDate, DateTime endDate)
        {
            // Example: Export inventory report as PDF or Excel using EPPlus/iTextSharp
            // Actual implementation would generate the file and return the file path or download link
            string fileType = reportType == ReportType.Excel ? "xlsx" : "pdf";
            string fileName = $"InventoryReport_{reportType}_{startDate:yyyyMMdd}_{endDate:yyyyMMdd}.{fileType}";
            // TODO: Generate and save the file using EPPlus/iTextSharp
            // For now, return a simulated file path
            return $"/exports/{fileName}";
        }

        public async Task<(bool Success, string Message)> ScheduleInventoryReportAsync(ReportSchedule schedule)
        {
            // Example: Save schedule to database and set up background job
            // Actual implementation would persist the schedule and trigger background generation
            // For now, simulate scheduling
            return (true, $"Inventory report scheduled: {schedule.ReportType} on {schedule.ScheduleDate:yyyy-MM-dd}");
        }

        #endregion

        #region PRD 4.1: Advanced Features (To be implemented)

        public async Task<StockReport> GenerateAdvancedStockReportAsync(DateTime startDate, DateTime endDate)
        {
            // TODO: Implement advanced stock reporting logic
            return new StockReport();
        }

        public async Task<MonthlyStockTakeResult> PerformMonthlyStockTakeAsync(DateTime month)
        {
            // TODO: Implement monthly stock take logic
            return new MonthlyStockTakeResult();
        }

        public async Task<TransferResult> TransferStockToBranchAsync(int branchId, List<StockMovement> items)
        {
            // TODO: Implement inter-branch transfer logic
            return new TransferResult();
        }

        public async Task<DocumentResult> UploadRawMaterialDocumentAsync(int rawMaterialId, Stream fileStream, string fileName)
        {
            // TODO: Implement document/file management for raw material documents
            return new DocumentResult();
        }

        public async Task<List<LowStockAlert>> GetLowStockAlertsAsync()
        {
            // TODO: Implement low stock alerting logic
            return new List<LowStockAlert>();
        }

        public async Task<List<ExpiringBatchAlert>> GetBatchExpiryAlertsAsync()
        {
            // TODO: Implement batch expiry tracking logic
            return new List<ExpiringBatchAlert>();
        }