﻿using Microsoft.Extensions.Logging;
using CakeBistro.Services;
using CakeBistro.ViewModels;
using CakeBistro.Views;
using Microsoft.EntityFrameworkCore;
using CommunityToolkit.Maui;
using CakeBistro.Data;

namespace CakeBistro;

public static class MauiProgram
{
    public static MauiApp CreateMauiApp()
    {
        var builder = MauiApp.CreateBuilder();
        builder
            .UseMauiApp<App>()
            .UseMauiCommunityToolkit()
            .ConfigureFonts(fonts =>
            {
                fonts.AddFont("OpenSans-Regular.ttf", "OpenSansRegular");
                fonts.AddFont("OpenSans-Semibold.ttf", "OpenSansSemibold");
            });

#if DEBUG
        builder.Logging.AddDebug();
#endif

        // Register DbContext
        builder.Services.AddDbContext<CakeBistroContext>(options =>
            options.UseSqlite("Data Source=cake_bistro.db"));

        // Register repositories
        builder.Services.AddScoped(typeof(IRepository<>), typeof(BaseRepository<>));
        builder.Services.AddScoped<IInventoryRepository, InventoryRepository>();
        builder.Services.AddScoped<ISupplierRepository, SupplierRepository>();
        builder.Services.AddScoped<IAssetRepository, AssetRepository>();
        builder.Services.AddScoped<IReportRepository, ReportRepository>();
        builder.Services.AddScoped<IUserRepository, UserRepository>();
        builder.Services.AddScoped<ICustomerRepository, CustomerRepository>();
        builder.Services.AddScoped<ISalesOrderRepository, SalesOrderRepository>();
        builder.Services.AddScoped<ISalesOrderItemRepository, SalesOrderItemRepository>();
        builder.Services.AddScoped<IPurchaseOrderItemRepository, PurchaseOrderItemRepository>();
        builder.Services.AddScoped<IRawMaterialRepository, RawMaterialRepository>();
        builder.Services.AddScoped<IGoodsReceiptRepository, GoodsReceiptRepository>();
        builder.Services.AddScoped<IGoodsReceiptItemRepository, GoodsReceiptItemRepository>();
        builder.Services.AddScoped<IRecipeRepository, RecipeRepository>();
        builder.Services.AddScoped<IProductionBatchRepository, ProductionBatchRepository>();
        builder.Services.AddScoped<IInventoryBatchRepository, InventoryBatchRepository>();

        // Register services
        builder.Services.AddScoped<IInventoryService, InventoryServiceNew>();
        builder.Services.AddScoped<ISupplierService, SupplierService>();
        builder.Services.AddScoped<IAssetService, AssetService>();
        builder.Services.AddScoped<IFinanceService, FinanceService>();
        builder.Services.AddScoped<IProductionService, ProductionService>();
        builder.Services.AddScoped<ISalesService, SalesService>();
        builder.Services.AddScoped<IInterBranchTransferService, InterBranchTransferService>();
        builder.Services.AddScoped<IReportingService, ReportingService>();
        builder.Services.AddScoped<IAuthenticationService, AuthenticationService>();
        builder.Services.AddScoped<IRawMaterialService, RawMaterialService>();
        builder.Services.AddScoped<IPurchaseOrderService, PurchaseOrderService>();
        builder.Services.AddScoped<IReportExportService, ReportExportService>();
        builder.Services.AddScoped<INumberService, NumberService>();
        builder.Services.AddScoped<IQualityControlService, QualityControlService>();
        builder.Services.AddScoped<INotificationService, NotificationService>();
        builder.Services.AddScoped<IQualityCheckReportService, QualityCheckReportService>();
        builder.Services.AddScoped<ILogisticsService, LogisticsService>();
        builder.Services.AddScoped<ProductionControlService>();
        builder.Services.AddScoped<PackingService>();
        builder.Services.AddScoped<LoadingService>();
        builder.Services.AddScoped<VehicleService>();
        builder.Services.AddScoped<DriverService>();
        builder.Services.AddScoped<DeliveryManifestService>();

        // Register ViewModels
        builder.Services.AddTransient<MainViewModel>();
        builder.Services.AddTransient<RawMaterialsViewModel>();
        // builder.Services.AddTransient<CakeBistro.Core.ViewModels.InventoryViewModel>();
        builder.Services.AddTransient<SupplierViewModel>();
        builder.Services.AddTransient<SupplierDetailViewModel>();
        builder.Services.AddTransient<RawMaterialDetailViewModel>();
        builder.Services.AddTransient<ProductionViewModel>();
        builder.Services.AddTransient<SalesOrderViewModel>();
        builder.Services.AddTransient<PurchaseOrderViewModel>();
        builder.Services.AddTransient<AssetViewModel>();
        builder.Services.AddTransient<FinanceViewModel>();
        builder.Services.AddTransient<ReportingViewModel>();
        builder.Services.AddTransient<ReportViewModel>();
        builder.Services.AddTransient<AuthenticationViewModel>();
        builder.Services.AddTransient<UserManagementViewModel>();
        builder.Services.AddTransient<InterBranchTransferViewModel>();
        builder.Services.AddTransient<InterBranchTransferFormViewModel>();
        builder.Services.AddTransient<ProductionFormViewModel>();
        builder.Services.AddTransient<RecipeViewModel>();
        builder.Services.AddTransient<RecipeDetailViewModel>();
        builder.Services.AddTransient<ProductionBatchViewModel>();
        builder.Services.AddTransient<ProductionBatchDetailViewModel>();
        builder.Services.AddTransient<QualityCheckViewModel>();
        builder.Services.AddTransient<QualityCheckDetailViewModel>();
        builder.Services.AddTransient<QualityCheckFormViewModel>();
        builder.Services.AddTransient<QualityControlViewModel>();
        builder.Services.AddTransient<NotificationsViewModel>();
        builder.Services.AddTransient<NotificationSettingsViewModel>();
        builder.Services.AddTransient<VehicleListViewModel>();
        builder.Services.AddTransient<VehicleDetailViewModel>();
        builder.Services.AddTransient<DriverListViewModel>();
        builder.Services.AddTransient<DriverDetailViewModel>();
        builder.Services.AddTransient<DeliveryManifestListViewModel>();
        builder.Services.AddTransient<QualityCheckReportViewModel>();
        builder.Services.AddTransient<Views.QualityCheckReportPage>();
        builder.Services.AddTransient<RawMaterialRegistrationViewModel>();
        builder.Services.AddTransient<SupplierManagementViewModel>();
        builder.Services.AddTransient<StockMovementTrackingViewModel>();
        builder.Services.AddTransient<RecipeManagementViewModel>();
        builder.Services.AddTransient<CostCalculationDashboardViewModel>();
        builder.Services.AddTransient<ProfitabilityAnalysisReportViewModel>();
        builder.Services.AddTransient<PackingViewModel>();
        builder.Services.AddTransient<LoadingViewModel>();
        builder.Services.AddTransient<DriverListViewModel>();
        builder.Services.AddTransient<VehicleListViewModel>();
        builder.Services.AddTransient<SalesTransactionListViewModel>();
        builder.Services.AddTransient<AssetListViewModel>();
        builder.Services.AddScoped<ReconciliationViewModel>();
        builder.Services.AddScoped<FinancialReportsViewModel>();
        builder.Services.AddScoped<BankingViewModel>();
        builder.Services.AddTransient<SupplierPaymentViewModel>();
        builder.Services.AddTransient<SalesAnalysisViewModel>();
        builder.Services.AddTransient<DebtorCreditorReportViewModel>();
        builder.Services.AddTransient<AccountStatementViewModel>();

        // Register Views
        builder.Services.AddTransient<MainPage>();
        builder.Services.AddTransient<RawMaterialsPage>();
        builder.Services.AddTransient<InventoryPage>();
        builder.Services.AddTransient<SupplierPage>();
        builder.Services.AddTransient<SupplierDetailPage>();
        builder.Services.AddTransient<RawMaterialDetailPage>();
        builder.Services.AddTransient<ProductionPage>();
        builder.Services.AddTransient<SalesOrderPage>();
        builder.Services.AddTransient<PurchaseOrderPage>();
        builder.Services.AddTransient<AssetPage>();
        builder.Services.AddTransient<FinancePage>();
        builder.Services.AddTransient<ReportingPage>();
        builder.Services.AddTransient<ReportPage>();
        builder.Services.AddTransient<AuthenticationPage>();
        builder.Services.AddTransient<UserManagementPage>();
        builder.Services.AddTransient<InterBranchTransferPage>();
        builder.Services.AddTransient<InterBranchTransferFormPage>();
        builder.Services.AddTransient<ProductionFormPage>();
        builder.Services.AddTransient<RecipesPage>();
        builder.Services.AddTransient<RecipeDetailPage>();
        builder.Services.AddTransient<ProductionBatchPage>();
        builder.Services.AddTransient<ProductionBatchDetailPage>();
        builder.Services.AddTransient<QualityCheckFormPage>();
        builder.Services.AddTransient<QualityControlPage>();
        builder.Services.AddTransient<NotificationsPage>();
        builder.Services.AddTransient<NotificationSettingsPage>();
        builder.Services.AddTransient<QualityCheckReportPage>();
        builder.Services.AddTransient<RawMaterialRegistrationPage>();
        builder.Services.AddTransient<SupplierManagementPage>();
        builder.Services.AddTransient<StockMovementTrackingPage>();
        builder.Services.AddTransient<RecipeManagementPage>();
        builder.Services.AddTransient<CostCalculationDashboard>();
        builder.Services.AddTransient<ProfitabilityAnalysisReport>();
        builder.Services.AddTransient<Views.PackingPage>();
        builder.Services.AddTransient<Views.LoadingPage>();
        builder.Services.AddTransient<Views.VehicleListPage>();
        builder.Services.AddTransient<Views.DriverListPage>();
        builder.Services.AddTransient<Views.DeliveryManifestListPage>();
        builder.Services.AddTransient<Views.SalesTransactionListPage>();
        builder.Services.AddTransient<Views.AssetListPage>();
        builder.Services.AddScoped<ReportingPage>();
        builder.Services.AddScoped<ReconciliationPage>();

        // Register App with DI
        builder.Services.AddTransient<App>();

        // Register background services
        builder.Services.AddHostedService<QualityCheckBackgroundService>();

        var app = builder.Build();
        ServiceProvider = app.Services;

        // Seed test data for StockTake and StockAdjustment
        // using (var scope = app.Services.CreateScope())
        // {
        //     var db = scope.ServiceProvider.GetRequiredService<CakeBistroContext>();
        //
        //     // Seed StockTake
        //     if (!db.StockTakes.Any())
        //     {
        //         db.StockTakes.Add(new StockTake { Date = DateTime.UtcNow, Notes = "Initial stock take" });
        //         db.StockTakes.Add(new StockTake { Date = DateTime.UtcNow.AddDays(-7), Notes = "Weekly stock take" });
        //     }
        //
        //     // Seed StockAdjustment
        //     if (!db.StockAdjustments.Any())
        //     {
        //         db.StockAdjustments.Add(new StockAdjustment { Date = DateTime.UtcNow, Reason = "Correction", Notes = "Adjusted for loss" });
        //         db.StockAdjustments.Add(new StockAdjustment { Date = DateTime.UtcNow.AddDays(-3), Reason = "Inventory error", Notes = "Manual adjustment" });
        //     }
        //
        //     db.SaveChanges();
        // }

        return app;
    }

    public static IServiceProvider ServiceProvider { get; private set; }
}
