<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:controls="clr-namespace:CakeBistro.Controls"
             x:Class="CakeBistro.Views.ProfitabilityAnalysisReport"
             Title="Profitability Analysis Report">
    <VerticalStackLayout Padding="20" Spacing="16">
        <controls:CardView>
            <StackLayout>
                <Label Text="Select Product" FontAttributes="Bold"/>
                <Picker x:Name="ProductPicker" />
                <Button Text="Analyze Profitability" Clicked="OnAnalyzeProfitabilityClicked" StyleClass="PrimaryButton AnimatedButton" />
                <Label x:Name="ProfitabilityResultLabel" FontAttributes="Bold" FontSize="18" TextColor="{StaticResource Primary}" />
            </StackLayout>
        </controls:CardView>
    </VerticalStackLayout>
</ContentPage>
