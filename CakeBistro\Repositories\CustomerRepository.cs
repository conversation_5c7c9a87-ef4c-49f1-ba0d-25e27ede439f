using CakeBistro.Core.Models;
using CakeBistro.data;
using Microsoft.EntityFrameworkCore;

namespace CakeBistro.Repositories
{
    public class CustomerRepository : BaseRepository<Customer>, ICustomerRepository
    {
        public CustomerRepository(CakeBistroContext context) : base(context)
        {
        }

        public override async Task DeleteAsync(Guid id)
        {
            var customer = await _context.Customers.FindAsync(id);
            if (customer != null)
            {
                _context.Customers.Remove(customer);
                await _context.SaveChangesAsync();
            }
        }
        
        public async Task<IEnumerable<Customer>> SearchCustomersAsync(string searchTerm)
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
                return new List<Customer>();
            
            return await _context.Customers
                .Where(c => c.Name.Contains(searchTerm) || 
                            c.Email.Contains(searchTerm) ||
                            c.Phone.Contains(searchTerm))
                .ToListAsync();
        }
        
        public async Task<IEnumerable<Customer>> GetActiveCustomersAsync()
        {
            // In a real application, you would have an IsActive flag
            // For now, returning all customers as active
            return await _context.Customers.ToListAsync();
        }
    }
}
