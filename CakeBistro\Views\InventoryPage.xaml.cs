using CakeBistro.ViewModels;
using Microsoft.Maui.Controls;

namespace CakeBistro.Views;

public partial class InventoryPage : ContentPage
{
    private readonly InventoryViewModel _viewModel;
    
    public InventoryPage(InventoryViewModel viewModel)
    {
        InitializeComponent();
        BindingContext = _viewModel = viewModel;
    }
    
    protected override async void OnAppearing()
    {
        base.OnAppearing();
        
        if (_viewModel != null && (_viewModel.RawMaterials == null || !_viewModel.RawMaterials.Any()))
        {
            await _viewModel.LoadDataAsync();
        }
    }
}
