<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="CakeBistro.Views.RawMaterialDetailPage"
             Title="Material Details">
    <VerticalStackLayout Margin="20">
        <!-- Material Information -->
        <Frame BackgroundColor="#F5F5F5" Margin="0,10">
            <VerticalStackLayout>
                <Label Text="{Binding Material.Name}" FontAttributes="Bold" FontSize="Large" />
                <Label Text="{Binding Material.Category}" />
                <Label Text="{Binding Material.Description}" />
                <Label Text="{Binding Material.UnitType}" />
                <Label Text="{Binding Material.MinimumThreshold, StringFormat='Minimum Threshold: {0:F2}'}" />
                <Label Text="{Binding Material.ReorderLevel, StringFormat='Reorder Level: {0:F2}'}" />
                <Label Text="{Binding Material.UnitPrice, StringFormat='Unit Price: {0:C}'}" />
            </VerticalStackLayout>
        </Frame>
        
        <!-- Current Stock -->
        <Label Text="Current Stock" FontAttributes="Bold" Margin="0,20,0,10" />
        <Label Text="{Binding Material.Quantity, StringFormat='Total Stock: {0:F2}'}" />
        
        <!-- Inventory Batches -->
        <Label Text="Inventory Batches" FontAttributes="Bold" Margin="0,20,0,10" />
        <CollectionView ItemsSource="{Binding Batches}"
                        IsVisible="{Binding Batches.Count, Converter={StaticResource EmptyToFalseConverter}}">
            <CollectionView.ItemsLayout>
                <LinearItemsLayout Orientation="Vertical" />
            </CollectionView.ItemsLayout>
            <CollectionView.ItemTemplate>
                <DataTemplate>
                    <Frame Margin="0,5">
                        <StackLayout Orientation="Horizontal">
                            <StackLayout>
                                <Label Text="{Binding BatchNumber}" FontAttributes="Bold" />
                                <Label Text="{Binding ProductionDate, StringFormat='Production Date: {0:yyyy-MM-dd}'}" />
                            </StackLayout>
                            <StackLayout HorizontalOptions="EndAndExpand">
                                <Label Text="{Binding ExpiryDate, StringFormat='Expiry Date: {0:yyyy-MM-dd}'}" />
                                <Label Text="{Binding Quantity, StringFormat='Quantity: {0:F2}'}" />
                            </StackLayout>
                        </StackLayout>
                    </Frame>
                </DataTemplate>
            </CollectionView.ItemTemplate>
        </CollectionView>
        <Label Text="No batches available" 
               IsVisible="{Binding Batches.Count, Converter={StaticResource ZeroToTrueConverter}}"
               Margin="0,20" />
        
        <!-- Recent Stock Movements -->
        <Label Text="Recent Stock Movements" FontAttributes="Bold" Margin="0,20,0,10" />
        <CollectionView ItemsSource="{Binding StockMovements}"
                        IsVisible="{Binding StockMovements.Count, Converter={StaticResource EmptyToFalseConverter}}">
            <CollectionView.ItemsLayout>
                <LinearItemsLayout Orientation="Vertical" />
            </CollectionView.ItemsLayout>
            <CollectionView.ItemTemplate>
                <DataTemplate>
                    <Frame Margin="0,5">
                        <StackLayout Orientation="Horizontal">
                            <StackLayout>
                                <Label Text="{Binding MovementType}" FontAttributes="Bold" />
                                <Label Text="{Binding MovementDate, StringFormat='{0:yyyy-MM-dd HH:mm}'}" />
                            </StackLayout>
                            <StackLayout HorizontalOptions="EndAndExpand">
                                <Label Text="{Binding Quantity, StringFormat='Qty: {0:F2}'}" />
                                <Label Text="{Binding ReferenceNumber}" />
                            </StackLayout>
                        </StackLayout>
                    </Frame>
                </DataTemplate>
            </CollectionView.ItemTemplate>
        </CollectionView>
        <Label Text="No stock movements recorded" 
               IsVisible="{Binding StockMovements.Count, Converter={StaticResource ZeroToTrueConverter}}"
               Margin="0,20" />
        
        <!-- Loading Indicator -->
        <ActivityIndicator IsRunning="{Binding IsBusy}"
                           IsVisible="{Binding IsBusy}"
                           HorizontalOptions="Center"
                           VerticalOptions="Center" />
        
        <!-- Action Buttons -->
        <HorizontalStackLayout Spacing="10" Margin="0,20">
            <Button Text="Refresh"
                    Command="{Binding RefreshCommand}"
                    HorizontalOptions="FillAndExpand" />
            <Button Text="Adjust Stock"
                    Command="{Binding MakeAdjustmentCommand}"
                    HorizontalOptions="FillAndExpand" />
            <Button Text="Back"
                    Command="{Binding GoBackCommand}"
                    HorizontalOptions="FillAndExpand" />
        </HorizontalStackLayout>
    </VerticalStackLayout>
</ContentPage>