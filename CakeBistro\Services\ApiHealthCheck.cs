using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.Extensions.Configuration;
using System;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;

namespace CakeBistro.Services
{
    public class ApiHealthCheck : IHealthCheck
    {
        private readonly ILogger<ApiHealthCheck> _logger;
        private readonly HttpClient _httpClient;
        private readonly string _apiUrl;
        private readonly TimeSpan _timeout;

        public ApiHealthCheck(ILogger<ApiHealthCheck> logger, HttpClient httpClient, string apiUrl)
        {
            _logger = logger;
            _httpClient = httpClient;
            _apiUrl = apiUrl;

            // Get timeout from configuration or use default
            var config = new ConfigurationBuilder()
                .AddJsonFile("appsettings.json")
                .Build();

            var timeoutSeconds = config.GetValue<int>("HealthCheck:ApiTimeoutSeconds", 10);
            _timeout = TimeSpan.FromSeconds(timeoutSeconds);
        }

        public async Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken cancellationToken = default)
        {
            try
            {
                using (var cts = new CancellationTokenSource(_timeout))
                {
                    var linkedToken = CancellationTokenSource.CreateLinkedTokenSource(cts.Token, cancellationToken).Token;

                    var response = await _httpClient.GetAsync(_apiUrl, linkedToken);

                    if (response.IsSuccessStatusCode)
                    {
                        return HealthCheckResult.Healthy("API is responsive and returning successful status codes");
                    }
                    else
                    {
                        return HealthCheckResult.Unhealthy($"API returned non-success status code: {response.StatusCode}");
                    }
                }
            }
            catch (OperationCanceledException ex) when (ex.CancellationToken.IsCancellationRequested)
            {
                _logger.LogError(ex, "API health check timed out: {Message}", ex.Message);
                return HealthCheckResult.Unhealthy($"API health check timed out: {ex.Message}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "API health check failed: {Message}", ex.Message);
                return HealthCheckResult.Unhealthy($"API health check failed: {ex.Message}");
            }
        }
    }
}