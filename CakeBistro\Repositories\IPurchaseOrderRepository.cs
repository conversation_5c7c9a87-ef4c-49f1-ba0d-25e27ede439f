using CakeBistro.Core.Models;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace CakeBistro.Repositories;

public interface IPurchaseOrderRepository : IRepository<CakeBistro.Core.Models.PurchaseOrder>
{
    Task<CakeBistro.Core.Models.PurchaseOrder> GetByIdAsync(int id);
    Task<IEnumerable<CakeBistro.Core.Models.PurchaseOrder>> GetBySupplierIdAsync(int supplierId);
    Task<IEnumerable<CakeBistro.Core.Models.PurchaseOrder>> GetByStatusAsync(string status);
    Task<CakeBistro.Core.Models.PurchaseOrder> AddAsync(CakeBistro.Core.Models.PurchaseOrder order);
    Task<CakeBistro.Core.Models.PurchaseOrder> UpdateAsync(CakeBistro.Core.Models.PurchaseOrder order);
    Task<bool> DeleteAsync(int id);
    Task<bool> SubmitPurchaseOrderAsync(int id);
    Task<bool> ReceivePurchaseOrderAsync(int id);
}
