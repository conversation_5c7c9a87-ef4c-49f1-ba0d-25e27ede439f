
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using CakeBistro.Models;

namespace CakeBistro.Services
{
    /// <summary>
    /// Interface defining methods for theme management operations
    /// </summary>
    public interface IThemeService
    {
        /// <summary>
        /// Gets a list of available themes
        /// </summary>
        /// <returns>A list of theme information</returns>
        Task<List<ThemeInfo>> GetAvailableThemesAsync();

        /// <summary>
        /// Gets the current application theme
        /// </summary>
        /// <returns>The current theme configuration</returns>
        Task<ThemeConfiguration> GetCurrentThemeAsync();

        /// <summary>
        /// Applies a theme by name
        /// </summary>
        /// <param name="themeName">The name of the theme to apply</param>
        /// <returns>True if the theme was successfully applied</returns>
        Task<bool> ApplyThemeAsync(string themeName);

        /// <summary>
        /// Saves user's theme preferences
        /// </summary>
        /// <param name="preferences">The theme preferences to save</param>
        /// <returns>True if the preferences were successfully saved</returns>
        Task<bool> SaveThemePreferencesAsync(ThemePreferences preferences);

        /// <summary>
        /// Gets user's saved theme preferences
        /// </summary>
        /// <returns>The user's theme preferences</returns>
        Task<ThemePreferences> GetThemePreferencesAsync();

        /// <summary>
        /// Creates a new custom theme
        /// </summary>
        /// <param name="theme">The theme to create</param>
        /// <returns>The created theme</returns>
        Task<CustomTheme> CreateCustomThemeAsync(CustomTheme theme);

        /// <summary>
        /// Updates an existing custom theme
        /// </summary>
        /// <param name="theme">The theme with updated information</param>
        /// <returns>The updated theme</returns>
        Task<CustomTheme> UpdateCustomThemeAsync(CustomTheme theme);

        /// <summary>
        /// Deletes a custom theme
        /// </summary>
        /// <param name="themeId">The ID of the theme to delete</param>
        /// <returns>True if the theme was successfully deleted</returns>
        Task<bool> DeleteCustomThemeAsync(int themeId);
    }

    /// <summary>
    /// Represents information about an available theme
    /// </summary>
    public class ThemeInfo
    {
        /// <summary>
        /// Gets or sets the unique identifier of the theme
        /// </summary>
        public int Id { get; set; }
        
        /// <summary>
        /// Gets or sets the name of the theme
        /// </summary>
        public string Name { get; set; }
        
        /// <summary>
        /// Gets or sets the description of the theme
        /// </summary>
        public string Description { get; set; }
        
        /// <summary>
        /// Gets or sets the version of the theme
        /// </summary>
        public string Version { get; set; }
        
        /// <summary>
        /// Gets or sets the author of the theme
        /// </summary>
        public string Author { get; set; }
    }

    /// <summary>
    /// Represents the current theme configuration
    /// </summary>
    public class ThemeConfiguration
    {
        /// <summary>
        /// Gets or sets the name of the current theme
        /// </summary>
        public string CurrentThemeName { get; set; }
        
        /// <summary>
        /// Gets or sets a value indicating whether dark mode is enabled
        /// </summary>
        public bool IsDarkModeEnabled { get; set; }
        
        /// <summary>
        /// Gets or sets the accent color in hexadecimal format
        /// </summary>
        public string AccentColor { get; set; }
        
        /// <summary>
        /// Gets or sets the background color in hexadecimal format
        /// </summary>
        public string BackgroundColor { get; set; }
        
        /// <summary>
        /// Gets or sets the text color in hexadecimal format
        /// </summary>
        public string TextColor { get; set; }
        
        /// <summary>
        /// Gets or sets the date and time when the theme was last changed
        /// </summary>
        public DateTime LastChangedDate { get; set; }
    }

    /// <summary>
    /// Represents user preferences for theme settings
    /// </summary>
    public class ThemePreferences
    {
        /// <summary>
        /// Gets or sets the preferred theme name
        /// </summary>
        public string PreferredTheme { get; set; }
        
        /// <summary>
        /// Gets or sets a value indicating whether to use dark mode by default
        /// </summary>
        public bool UseDarkMode { get; set; }
        
        /// <summary>
        /// Gets or sets the preferred accent color in hexadecimal format
        /// </summary>
        public string PreferredAccentColor { get; set; }
        
        /// <summary>
        /// Gets or sets the date and time when the preferences were last updated
        /// </summary>
        public DateTime LastUpdated { get; set; }
    }

    /// <summary>
    /// Represents a custom theme definition
    /// </summary>
    public class CustomTheme
    {
        /// <summary>
        /// Gets or sets the unique identifier of the theme
        /// </summary>
        public int Id { get; set; }
        
        /// <summary>
        /// Gets or sets the name of the theme
        /// </summary>
        public string Name { get; set; }
        
        /// <summary>
        /// Gets or sets the description of the theme
        /// </summary>
        public string Description { get; set; }
        
        /// <summary>
        /// Gets or sets the version of the theme
        /// </summary>
        public string Version { get; set; }
        
        /// <summary>
        /// Gets or sets the author of the theme
        /// </summary>
        public string Author { get; set; }
        
        /// <summary>
        /// Gets or sets the base theme that this theme inherits from
        /// </summary>
        public string BaseTheme { get; set; }
        
        /// <summary>
        /// Gets or sets the accent color in hexadecimal format
        /// </summary>
        public string AccentColor { get; set; }
        
        /// <summary>
        /// Gets or sets the background color in hexadecimal format
        /// </summary>
        public string BackgroundColor { get; set; }
        
        /// <summary>
        /// Gets or sets the text color in hexadecimal format
        /// </summary>
        public string TextColor { get; set; }
        
        /// <summary>
        /// Gets or sets the primary font family
        /// </summary>
        public string PrimaryFontFamily { get; set; }
        
        /// <summary>
        /// Gets or sets the secondary font family
        /// </summary>
        public string SecondaryFontFamily { get; set; }
    
        /// <summary>
        /// Event that is raised when the theme changes
        /// </summary>
        public event EventHandler<ThemeEventArgs> ThemeChanged;
    }
}