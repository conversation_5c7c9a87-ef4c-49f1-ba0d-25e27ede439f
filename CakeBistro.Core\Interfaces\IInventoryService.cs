using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using CakeBistro.Core.Models;

namespace CakeBistro.Core.Interfaces
{
    /// <summary>
    /// Interface for inventory management operations
    /// </summary>
    public interface IInventoryService
    {
        Task<RawMaterial> RegisterRawMaterialAsync(RawMaterial rawMaterial);
        Task<IEnumerable<RawMaterial>> GetAllRawMaterialsAsync();
        Task<RawMaterial?> GetRawMaterialByIdAsync(int id);
        Task<bool> UpdateRawMaterialAsync(RawMaterial rawMaterial);
        Task<bool> DeleteRawMaterialAsync(int id);
        Task<StockMovement> RecordStockMovementAsync(StockMovement stockMovement);
        Task<IEnumerable<StockMovement>> GetStockMovementsAsync(int? rawMaterialId = null, DateTime? fromDate = null, DateTime? toDate = null);
        Task<IEnumerable<RawMaterial>> GenerateStockReportAsync();
        Task<object> GenerateStockStatementAsync(int rawMaterialId, DateTime startDate, DateTime endDate);
        Task<IEnumerable<RawMaterial>> GetLowStockItemsAsync(int threshold = 10);
        Task<int> GetTotalRawMaterialsCountAsync();
        Task<decimal> GetTotalInventoryValueAsync();
        Task<IEnumerable<InventoryBatch>> GetExpiringBatchesAsync(int daysAhead = 30);
        Task<int> GetLowStockItemsCountAsync();
        Task<int> GetStockMovementsCountAsync();
        Task<int> GetInterBranchTransfersCountAsync();
        Task<int> GetMonthlyStockTakeCountAsync();
    }
}
