using System.Collections.ObjectModel;
using CommunityToolkit.Mvvm.ComponentModel;
using CakeBistro.Core.Models;

namespace CakeBistro.Services
{
    public interface INotificationService
    {
        Task SendNotificationAsync(string title, string message, string type = "info");
        Task SendQualityCheckNotificationAsync(QualityCheck check);
        Task<IEnumerable<AppNotification>> GetRecentNotificationsAsync(int count = 10);
        Task MarkNotificationAsReadAsync(int notificationId);
        Task<int> GetUnreadNotificationCountAsync();
    }

    public class NotificationService : INotificationService
    {
        private readonly CakeBistroContext _context;
        private readonly IServiceProvider _serviceProvider;

        public NotificationService(CakeBistroContext context, IServiceProvider serviceProvider)
        {
            _context = context;
            _serviceProvider = serviceProvider;
        }

        public async Task SendNotificationAsync(string title, string message, string type = "info")
        {
            var notification = new AppNotification
            {
                Title = title,
                Message = message,
                Type = type,
                CreatedAt = DateTime.UtcNow,
                IsRead = false
            };

            _context.Notifications.Add(notification);
            await _context.SaveChangesAsync();

            // If we're on a platform that supports local notifications, send one
            if (Application.Current?.Handler?.MauiContext != null)
            {
                await ShowLocalNotificationAsync(title, message);
            }
        }

        public async Task SendQualityCheckNotificationAsync(QualityCheck check)
        {
            var prefs = LoadPreferences();
            if ((check.Status == "Failed" && !prefs.EnableFailedCheckNotifications) ||
                (check.Status == "NeedsReview" && !prefs.EnablePendingCheckNotifications))
                return;

            string title = $"Quality Check {check.Status}";
            string message = $"Batch {check.ProductionBatch?.BatchNumber ?? check.ProductionBatchId.ToString()} " +
                           $"{check.Type} check {check.Status.ToLower()}. ";

            if (check.Status == "Failed")
            {
                message += $"Issue: {check.DefectDescription ?? "No details provided"}";
                if (!string.IsNullOrEmpty(check.CorrectiveAction))
                {
                    message += $" Action: {check.CorrectiveAction}";
                }
            }

            await SendNotificationAsync(title, message, check.Status.ToLower());
        }

        public async Task<IEnumerable<AppNotification>> GetRecentNotificationsAsync(int count = 10)
        {
            return await _context.Notifications
                .OrderByDescending(n => n.CreatedAt)
                .Take(count)
                .ToListAsync();
        }

        public async Task MarkNotificationAsReadAsync(int notificationId)
        {
            var notification = await _context.Notifications.FindAsync(notificationId);
            if (notification != null)
            {
                notification.IsRead = true;
                notification.ReadAt = DateTime.UtcNow;
                await _context.SaveChangesAsync();
            }
        }

        public async Task<int> GetUnreadNotificationCountAsync()
        {
            return await _context.Notifications.CountAsync(n => !n.IsRead);
        }

        private async Task ShowLocalNotificationAsync(string title, string message)
        {
            try
            {
                INotificationService platformNotificationService = 
                    _serviceProvider.GetService<INotificationService>();
                
                if (platformNotificationService != null)
                {
                    // Schedule the notification immediately
                    await platformNotificationService.SendNotificationAsync(title, message);
                }
            }
            catch (Exception ex)
            {
                // Log the error but don't throw - notifications are non-critical
                System.Diagnostics.Debug.WriteLine($"Failed to show local notification: {ex.Message}");
            }
        }

        private NotificationPreferences LoadPreferences()
        {
            var json = Preferences.Default.Get("NotificationPreferences", string.Empty);
            if (!string.IsNullOrEmpty(json))
            {
                return System.Text.Json.JsonSerializer.Deserialize<NotificationPreferences>(json) ?? new NotificationPreferences();
            }
            return new NotificationPreferences();
        }
    }
}
