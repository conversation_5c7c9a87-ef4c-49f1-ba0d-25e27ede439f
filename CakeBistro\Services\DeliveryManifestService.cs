using CakeBistro.Models;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq;

namespace CakeBistro.Services
{
    public class DeliveryManifestService
    {
        private readonly List<DeliveryManifest> _manifests = new();

        public Task<DeliveryManifest> CreateManifestAsync(DeliveryManifest manifest)
        {
            manifest.Id = _manifests.Count > 0 ? _manifests.Max(m => m.Id) + 1 : 1;
            _manifests.Add(manifest);
            return Task.FromResult(manifest);
        }

        public Task<List<DeliveryManifest>> GetAllManifestsAsync()
        {
            return Task.FromResult(_manifests.ToList());
        }

        public Task<bool> RemoveManifestAsync(int id)
        {
            var manifest = _manifests.FirstOrDefault(m => m.Id == id);
            if (manifest == null) return Task.FromResult(false);
            _manifests.Remove(manifest);
            return Task.FromResult(true);
        }
    }
}
