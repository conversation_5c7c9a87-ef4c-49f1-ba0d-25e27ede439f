<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="MCakeBistro.Views.RoleManagementPage"
             Title="Role Management">
    <VerticalStackLayout>
        <!-- Search and add -->
        <HorizontalStackLayout Padding="10" Spacing="10">
            <Entry Placeholder="Search roles..."
                   Text="{Binding SearchText}"
                   HorizontalOptions="FillAndExpand"
                   ReturnType="Search"/>
            <Button Text="Add Role"
                    Command="{Binding AddRoleCommand}"
                    BackgroundColor="#e74c3c"
                    TextColor="White"/>
        </HorizontalStackLayout>
        
        <!-- Roles list -->
        <CollectionView ItemsSource="{Binding Roles}"
                        SelectedItem="{Binding SelectedRole}"
                        SelectionMode="Single">
            <CollectionView.ItemTemplate>
                <DataTemplate>
                    <Grid Padding="10">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        
                        <VerticalStackLayout>
                            <Label Text="{Binding Name}"
                                   FontAttributes="Bold"/>
                            <Label Text="{Binding Description}"
                                   TextColor="Gray"/>
                            <Label Text="{Binding RolePermissions.Count} Permissions" TextColor="Gray"/>
                        </VerticalStackLayout>
                        
                        <Image Grid.Column="1"
                               Source="delete.png"
                               HeightRequest="20"
                               WidthRequest="20"
                               HorizontalOptions="End"
                               VerticalOptions="Center"
                               Command="{Binding BindingContext.DeleteRoleCommand, Source={x:Reference RoleManagementPage}}"
                               CommandParameter="{Binding .}"
                               Margin="0,0,10,0"/>
                    </Grid>
                </DataTemplate>
            </CollectionView.ItemTemplate>
        </CollectionView>
        
        <!-- Role details -->
        <ContentView IsVisible="{Binding SelectedRole != null}">
            <VerticalStackLayout Padding="10">
                <Label Text="Role Details" FontSize="Large" FontAttributes="Bold"/>
                
                <Grid ColumnDefinitions="*,*" RowDefinitions="Auto,Auto,Auto" Spacing="10">
                    <Label Text="Name:" Grid.Row="0" Grid.Column="0" TextColor="Gray"/>
                    <Entry Text="{Binding SelectedRole.Name}"
                           Grid.Row="0" Grid.Column="1"/>
                    
                    <Label Text="Description:" Grid.Row="1" Grid.Column="0" TextColor="Gray"/>
                    <Entry Text="{Binding SelectedRole.Description}"
                           Grid.Row="1" Grid.Column="1"/>
                    
                    <Switch IsToggled="{Binding SelectedRole.IsSystemRole}"
                            Grid.Row="2" Grid.Column="1"/>
                    <Label Text="System Role" Grid.Row="2" Grid.Column="0" TextColor="Gray"/>
                </Grid>
                
                <!-- Assigned permissions -->
                <Label Text="Assigned Permissions" FontSize="Medium" FontAttributes="Bold" Margin="0,20,0,10"/>
                
                <CollectionView ItemsSource="{Binding SelectedRolePermissions}"
                                SelectionMode="None">
                    <CollectionView.ItemTemplate>
                        <DataTemplate>
                            <Grid Padding="5">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                
                                <Label Text="{Binding Name}"
                                       Grid.Column="0"/>
                                
                                <Image Grid.Column="1"
                                       Source="delete.png"
                                       HeightRequest="20"
                                       WidthRequest="20"
                                       HorizontalOptions="End"
                                       VerticalOptions="Center"
                                       Command="{Binding BindingContext.RemovePermissionCommand, Source={x:Reference RoleManagementPage}}"
                                       CommandParameter="{Binding .}"
                                       Margin="0,0,10,0"/>
                            </Grid>
                        </DataTemplate>
                    </CollectionView.ItemTemplate>
                </CollectionView>
                
                <!-- Available permissions dropdown -->
                <Label Text="Available Permissions" FontSize="Medium" FontAttributes="Bold" Margin="0,20,0,10"/>
                
                <Picker ItemsSource="{Binding Permissions}"
                         ItemDisplayBinding="{Binding Name}"
                         SelectedItem="{Binding SelectedPermission}"
                         HorizontalOptions="FillAndExpand"/>
                
                <HorizontalStackLayout Spacing="10" Margin="0,10,0,0">
                    <Button Text="Assign Permission"
                            Command="{Binding AssignPermissionsCommand}"
                            BackgroundColor="#e74c3c"
                            TextColor="White"
                            HorizontalOptions="FillAndExpand"/>
                    <Button Text="Cancel"
                            Command="{Binding CancelEditCommand}"
                            BackgroundColor="#bdc3c7"
                            TextColor="White"
                            HorizontalOptions="FillAndExpand"/>
                </HorizontalStackLayout>
                
                <HorizontalStackLayout Spacing="10" Margin="0,20,0,0">
                    <Button Text="Save"
                            Command="{Binding SaveRoleCommand}"
                            BackgroundColor="#e74c3c"
                            TextColor="White"
                            HorizontalOptions="FillAndExpand"/>
                    <Button Text="Delete"
                            Command="{Binding DeleteRoleCommand}"
                            BackgroundColor="#e74c3c"
                            TextColor="White"
                            HorizontalOptions="FillAndExpand"
                            IsEnabled="{Binding SelectedRole.IsSystemRole, Converter={StaticResource InverseBooleanConverter}}"/>
                </HorizontalStackLayout>
            </VerticalStackLayout>
        </ContentView>
        
        <!-- Navigation -->
        <Button Text="Back to Main"
                Command="{Binding NavigateBackCommand}"
                BackgroundColor="#bdc3c7"
                TextColor="White"
                HorizontalOptions="Center"
                Margin="10"/>
    </VerticalStackLayout>
</ContentPage>