<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:vm="clr-namespace:CakeBistro.ViewModels"
             x:Class="CakeBistro.Views.QualityCheckReportPage"
             x:DataType="vm:QualityCheckReportViewModel"
             Title="Quality Check Report">

    <ScrollView>
        <VerticalStackLayout Spacing="16" Padding="20">
            <Label Text="Generate Quality Check Report" FontSize="20" FontAttributes="Bold"/>

            <!-- Date Range -->
            <Label Text="Date Range"/>
            <HorizontalStackLayout Spacing="10">
                <DatePicker Date="{Binding StartDate}"/>
                <Label Text="to"/>
                <DatePicker Date="{Binding EndDate}"/>
            </HorizontalStackLayout>

            <!-- Check Type Filter -->
            <Label Text="Check Type"/>
            <Picker ItemsSource="{Binding CheckTypes}"
                    SelectedItem="{Binding SelectedCheckType}"
                    Title="All Types"/>

            <!-- Batch Filter -->
            <Label Text="Production Batch"/>
            <Picker ItemsSource="{Binding Batches}"
                    SelectedItem="{Binding SelectedBatch}"
                    ItemDisplayBinding="{Binding BatchNumber}"
                    Title="All Batches"/>

            <!-- Export Format -->
            <Label Text="Export Format"/>
            <Picker ItemsSource="{Binding ExportFormats}"
                    SelectedItem="{Binding SelectedExportFormat}"
                    Title="Select Format"/>

            <!-- Generate Button -->
            <Button Text="Generate Report"
                    Command="{Binding GenerateReportCommand}"
                    IsEnabled="{Binding IsNotBusy}"
                    Margin="0,20,0,0"/>

            <!-- Activity Indicator -->
            <ActivityIndicator IsRunning="{Binding IsBusy}"
                               IsVisible="{Binding IsBusy}"
                               HorizontalOptions="Center"/>

            <!-- Status Message -->
            <Label Text="{Binding StatusMessage}"
                   IsVisible="{Binding StatusMessage, Converter={StaticResource EmptyToFalseConverter}}"
                   TextColor="{Binding StatusColor}" />
        </VerticalStackLayout>
    </ScrollView>
</ContentPage>
