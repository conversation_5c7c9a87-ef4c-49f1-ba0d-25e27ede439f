using CakeBistro.Core.Models;
using CakeBistro.Repositories;
// FinanceService.cs
using Microsoft.EntityFrameworkCore;

namespace CakeBistro.Services;

public class FinanceService : IFinanceService
{
    private readonly IRepository<BankAccount> _accountRepository;
    private readonly IRepository<Transaction> _transactionRepository;
    private readonly ISupplierRepository _supplierRepository;
    private readonly ISalesRepository _salesRepository;
    private readonly IInventoryRepository _inventoryRepository;
    private readonly IAssetRepository _assetRepository;
    private readonly IDepreciationRepository _depreciationRepository;
    private readonly IMaintenanceRepository _maintenanceRepository;
    private readonly IReportExportService _reportExportService;

    public FinanceService(
        IRepository<BankAccount> accountRepository,
        IRepository<Transaction> transactionRepository,
        ISupplierRepository supplierRepository,
        ISalesRepository salesRepository,
        IInventoryRepository inventoryRepository,
        IAssetRepository assetRepository,
        IDepreciationRepository depreciationRepository,
        IMaintenanceRepository maintenanceRepository,
        IReportExportService reportExportService // Inject export service
    )
    {
        _accountRepository = accountRepository;
        _transactionRepository = transactionRepository;
        _supplierRepository = supplierRepository;
        _salesRepository = salesRepository;
        _inventoryRepository = inventoryRepository;
        _assetRepository = assetRepository;
        _depreciationRepository = depreciationRepository;
        _maintenanceRepository = maintenanceRepository;
        _reportExportService = reportExportService;
    }

    public async Task<IEnumerable<BankAccount>> GetAllAccountsAsync()
    {
        return await _accountRepository.GetAllAsync();
    }

    public async Task<BankAccount> GetAccountByIdAsync(int id)
    {
        return await _accountRepository.GetByIdAsync(id);
    }

    public async Task<BankAccount> AddAccountAsync(BankAccount account)
    {
        // Set default values if not provided
        account.CreatedDate ??= DateTime.Now;
        account.Status ??= "Active";
        
        return await _accountRepository.AddAsync(account);
    }

    public async Task UpdateAccountAsync(BankAccount account)
    {
        // Validate input
        if (account == null)
            throw new ArgumentNullException(nameof(account));

        // Ensure we have a valid ID
        if (account.Id <= 0)
            throw new ArgumentException("Invalid account ID");

        await _accountRepository.UpdateAsync(account);
    }

    public async Task DeleteAccountAsync(int id)
    {
        await _accountRepository.DeleteAsync(id);
    }

    public async Task<decimal> GetAccountBalanceAsync(int accountId)
    {
        var transactions = await _transactionRepository.GetAllAsync();
        return transactions
            .Where(t => t.AccountId == accountId)
            .Sum(t => t.Amount);
    }

    public async Task<(bool Success, string Message)> RecordTransactionAsync(Transaction transaction)
    {
        // Validate transaction
        if (transaction == null)
            throw new ArgumentNullException(nameof(transaction), "Transaction cannot be null");

        if (transaction.AccountId <= 0)
            throw new ArgumentException("Valid account ID is required", nameof(transaction.AccountId));

        if (string.IsNullOrWhiteSpace(transaction.Description))
            throw new ArgumentException("Transaction description is required", nameof(transaction.Description));

        if (transaction.Amount == 0)
            throw new ArgumentException("Transaction amount cannot be zero", nameof(transaction.Amount));

        if (string.IsNullOrWhiteSpace(transaction.Type))
            throw new ArgumentException("Transaction type is required", nameof(transaction.Type));

        var validTypes = new[] { "Deposit", "Withdrawal", "Transfer", "Payment", "Fee" };
        if (!validTypes.Contains(transaction.Type))
            throw new ArgumentException($"Invalid transaction type. Must be one of: {string.Join(", ", validTypes)}", nameof(transaction.Type));

        // Set transaction date if not provided
        if (transaction.Date == default)
            transaction.Date = DateTime.Today;

        try
        {
            // Verify account exists
            var account = await _accountRepository.GetByIdAsync(transaction.AccountId);
            if (account == null)
                throw new ArgumentException($"Account with ID {transaction.AccountId} not found", nameof(transaction.AccountId));

            // For withdrawals, check that we're not overdrawing the account
            if (transaction.Amount < 0 && transaction.Type != "Fee") // Allow fees to be negative
            {
                if (await GetAccountBalanceAsync(transaction.AccountId) + transaction.Amount < -1000)
                {
                    throw new InvalidOperationException(
                        $"Transaction would overdraw the account. Current balance: {await GetAccountBalanceAsync(transaction.AccountId):C2}, Attempted withdrawal: {Math.Abs(transaction.Amount):C2}");
                }
            }
            
            // Calculate running balance
            var currentBalance = await GetAccountBalanceAsync(transaction.AccountId);
            transaction.RunningBalance = currentBalance + transaction.Amount;
            
            // Save the transaction
            await _transactionRepository.AddAsync(transaction);

            return (true, $"Transaction recorded successfully. New balance: {transaction.RunningBalance:C2}");
        }
        catch (Exception ex)
        {
            // Log the error (in a real application)
            // For now, just return the error message
            return (false, $"Error recording transaction: {ex.Message}");
        }
    }

    public async Task ReconcileAccountAsync(int accountId, DateTime statementDate, decimal statementBalance)
    {
        var account = await _accountRepository.GetByIdAsync(accountId);
        if (account != null)
        {
            account.LastReconciled = statementDate;
            account.ReconciledBalance = statementBalance;
            await _accountRepository.UpdateAsync(account);
        }
    }

    public async Task<IEnumerable<Transaction>> GetTransactionHistoryAsync(int accountId, DateTime startDate, DateTime endDate)
    {
        return await _transactionRepository.GetAllAsync();
    }

    public async Task<FinancialReport> GenerateBalanceSheetAsync(DateTime reportDate)
    {
        // Calculate assets, liabilities, and equity
        var assets = await _assetRepository.GetAllAsync();
        var totalAssets = assets.Sum(a => a.AcquisitionCost - a.AccumulatedDepreciation);
        var liabilities = await _supplierRepository.GetAllAsync();
        var totalLiabilities = liabilities.Sum(s => s.OutstandingPayable);
        var equity = totalAssets - totalLiabilities;
        return new FinancialReport
        {
            ReportType = "Balance Sheet",
            ReportDate = reportDate,
            Assets = totalAssets,
            Liabilities = totalLiabilities,
            Equity = equity
        };
    }

    public async Task<FinancialReport> GenerateIncomeStatementAsync(DateTime startDate, DateTime endDate)
    {
        // Calculate revenue and expenses
        var allSales = await _salesRepository.GetAllAsync();
        var revenue = allSales.Where(s => s.OrderDate >= startDate && s.OrderDate <= endDate).Sum(s => s.TotalAmount) ?? 0;
        var allExpenses = await _transactionRepository.GetAllAsync();
        var expenses = allExpenses.Where(t => t.Type == "Payment" && t.Date >= startDate && t.Date <= endDate).Sum(t => t.Amount);
        var netIncome = revenue - expenses;
        return new FinancialReport
        {
            ReportType = "Income Statement",
            ReportDate = endDate,
            Revenue = revenue,
            Expenses = expenses,
            NetIncome = netIncome
        };
    }

    public async Task<FinancialReport> GenerateCashFlowStatementAsync(DateTime startDate, DateTime endDate)
    {
        // Calculate cash inflows and outflows
        var allTransactions = await _transactionRepository.GetAllAsync();
        var inflows = allTransactions.Where(t => t.Amount > 0 && t.Date >= startDate && t.Date <= endDate).Sum(t => t.Amount);
        var outflows = allTransactions.Where(t => t.Amount < 0 && t.Date >= startDate && t.Date <= endDate).Sum(t => Math.Abs(t.Amount));
        var netCashFlow = inflows - outflows;
        return new FinancialReport
        {
            ReportType = "Cash Flow Statement",
            ReportDate = endDate,
            CashInflows = inflows,
            CashOutflows = outflows,
            NetCashFlow = netCashFlow
        };
    }

    public async Task<SalesSummary> GetDailySalesSummaryAsync(DateTime date)
    {
        // Implementation would go here
        return new SalesSummary();
    }

    public async Task<ProductDistribution> GetProductDistributionByRegionAsync(DateTime startDate, DateTime endDate)
    {
        // Implementation would go here
        return new ProductDistribution();
    }

    public async Task<IEnumerable<Debtor>> GetDebtorsListAsync()
    {
        // Implementation would go here
        return new List<Debtor>();
    }

    public async Task<IEnumerable<Creditor>> GetCreditorsListAsync()
    {
        // Get all suppliers with outstanding payables
        var allSuppliers = await _supplierRepository.GetAllAsync();
        var creditors = allSuppliers
            .Where(s => s.OutstandingPayable > 0)
            .Select(s => new Creditor
            {
                SupplierId = s.Id,
                SupplierName = s.Name,
                OutstandingAmount = s.OutstandingPayable
            })
            .ToList();
        return new CreditorList { Creditors = creditors, ReportDate = DateTime.Now };
    }

    public async Task<decimal> CalculateTotalDepreciationAsync(int assetId)
    {
        var allDepreciation = await _depreciationRepository.GetAllAsync();
        return allDepreciation
            .Where(d => d.AssetId == assetId)
            .Sum(d => d.Amount);
    }

    public async Task<AssetRegister> GetAssetRegisterAsync()
    {
        var allAssets = await _assetRepository.GetAllAsync();
        
        return new AssetRegister
        {
            TotalAssets = allAssets.Count(),
            TotalValue = allAssets.Sum(a => a.AcquisitionCost),
            NetBookValue = allAssets.Sum(a => a.AcquisitionCost - a.AccumulatedDepreciation),
            ByCategory = allAssets.GroupBy(a => a.Category)
                .ToDictionary(g => g.Key, g => g.Sum(a => a.AcquisitionCost - a.AccumulatedDepreciation)),
            DepreciationSchedule = await _depreciationRepository.GetAllAsync()
        };
    }

    public async Task<LocationReport> GetLocationReportAsync(string location)
    {
        var assets = await ((IAssetRepository)_assetRepository).GetByLocationAsync(location);
        
        return new LocationReport
        {
            Location = location,
            AssetCount = assets.Count(),
            TotalAssetValue = assets.Sum(a => a.AcquisitionCost - a.AccumulatedDepreciation),
            Assets = assets.ToList()
        };
    }

    public async Task<MaintenanceSchedule> GetMaintenanceScheduleAsync(DateTime startDate, DateTime endDate)
    {
        // Implementation would go here
        return new MaintenanceSchedule();
    }

    public async Task<FixedAsset> GetAssetByIdAsync(int id)
    {
        return await _assetRepository.GetByIdAsync(id);
    }

    public async Task<IEnumerable<FixedAsset>> GetAssetsByLocationAsync(string location)
    {
        return await ((IAssetRepository)_assetRepository).GetByLocationAsync(location);
    }

    public async Task<decimal> CalculateNetBookValueAsync(int assetId)
    {
        var asset = await _assetRepository.GetByIdAsync(assetId);
        if (asset == null) return 0;

        var depreciationRecords = await _depreciationRepository.GetAllAsync();
        var totalDepreciation = depreciationRecords
            .Where(d => d.AssetId == assetId)
            .Sum(d => d.Amount);

        return asset.AcquisitionCost - totalDepreciation;
    }

    public async Task<decimal> CalculateTotalDepreciationAsync(int assetId)
    {
        var allDepreciation = await _depreciationRepository.GetAllAsync();
        return allDepreciation
            .Where(d => d.AssetId == assetId)
            .Sum(d => d.Amount);
    }

    public async Task<(bool Success, string Message)> TransferFundsAsync(FundTransfer transfer)
    {
        try
        {
            // Implementation would go here
            return (true, "Funds transferred successfully");
        }
        catch (Exception ex)
        {
            return (false, $"Error transferring funds: {ex.Message}");
        }
    }

    public async Task<(bool Success, string Message)> ProcessPaymentAsync(Payment payment)
    {
        try
        {
            // Implementation would go here
            return (true, "Payment processed successfully");
        }
        catch (Exception ex)
        {
            return (false, $"Error processing payment: {ex.Message}");
        }
    }

    public async Task<(bool Success, string Message)> ProcessSupplierPaymentAsync(SupplierPayment payment)
    {
        try
        {
            // Implementation would go here
            return (true, "CakeBistro.Core.Models.Supplier payment processed successfully");
        }
        catch (Exception ex)
        {
            return (false, $"Error processing CakeBistro.Core.Models.Supplier payment: {ex.Message}");
        }
    }

    public async Task<DepreciationRecord> RecordDepreciationAsync(DepreciationRecord record)
    {
        // Validate input
        if (record == null)
            throw new ArgumentNullException(nameof(record));

        // Calculate depreciation amount if not provided
        if (record.Amount == 0)
        {
            var asset = await _assetRepository.GetByIdAsync(record.AssetId);
            if (asset == null)
                throw new ArgumentException("Invalid asset ID");

            // For simplicity, we're using straight-line depreciation over 5 years
            record.Amount = asset.AcquisitionCost / 5;
        }

        // Save the depreciation record
        return await _depreciationRepository.AddAsync(record);
    }

    public async Task<MaintenanceRecord> ScheduleMaintenanceAsync(MaintenanceRecord schedule)
    {
        // Validate input
        if (schedule == null)
            throw new ArgumentNullException(nameof(schedule));

        // Set default values if not provided
        schedule.ScheduledDate ??= DateTime.Today.AddDays(7);
        schedule.Status ??= "Scheduled";
        
        // Save the schedule
        return await _maintenanceRepository.AddAsync(schedule);
    }

    public async Task<MaintenanceRecord> PerformMaintenanceAsync(MaintenanceRecord record)
    {
        // Validate input
        if (record == null)
            throw new ArgumentNullException(nameof(record));

        // Update maintenance record with completion details
        record.CompletedDate = DateTime.Now;
        record.Status = "Completed";
        
        // Save changes
        await _maintenanceRepository.UpdateAsync(record);
        
        return record;
    }

    public async Task<AccountStatement> GetAccountStatementAsync(int accountId, DateTime startDate, DateTime endDate)
    {
        var transactions = await _transactionRepository.GetAllAsync();
        
        var statement = new AccountStatement
        {
            AccountId = accountId,
            StartDate = startDate,
            EndDate = endDate,
            Transactions = transactions.Where(t => t.AccountId == accountId && t.Date >= startDate && t.Date <= endDate).ToList(),
            OpeningBalance = 0,
            ClosingBalance = 0
        };

        // Calculate opening and closing balances
        if (statement.Transactions.Any())
        {
            statement.OpeningBalance = statement.Transactions.Min(t => t.RunningBalance);
            statement.ClosingBalance = statement.Transactions.Max(t => t.RunningBalance);
        }

        return statement;
    }

    public async Task<StockValuation> GetStockValuationAsync()
    {
        var allProducts = await _productRepository.GetAllAsync();
        var lowStockProducts = await _productRepository.GetLowStockProductsAsync(10);
        
        return new StockValuation
        {
            TotalStockValue = allProducts.Sum(p => p.CurrentStock * p.PricePerUnit),
            LowStockCount = lowStockProducts.Count(),
            LastStockTake = DateTime.Today.AddDays(-7),
            NextReorderDate = DateTime.Today.AddDays(30)
        };
    }

    public async Task<DebtorList> GetDebtorListAsync()
    {
        var allOrders = await _salesRepository.GetAllAsync();
        
        return new DebtorList
        {
            TotalDebt = allOrders.Where(o => o.PaymentStatus == "Unpaid").Sum(o => o.Balance),
            Debtors = allOrders
                .Where(o => o.PaymentStatus == "Unpaid")
                .GroupBy(o => o.CustomerId)
                .Select(g => new Debtor
                {
                    CustomerId = g.Key,
                    AmountOwing = g.Sum(o => o.Balance)
                })
                .ToList(),
            ReportDate = DateTime.Now
        };
    }

    public async Task<CreditorList> GetCreditorListAsync()
    {
        // Get all suppliers with outstanding payables
        var allSuppliers = await _supplierRepository.GetAllAsync();
        var creditors = allSuppliers
            .Where(s => s.OutstandingPayable > 0)
            .Select(s => new Creditor
            {
                SupplierId = s.Id,
                SupplierName = s.Name,
                OutstandingAmount = s.OutstandingPayable
            })
            .ToList();
        return new CreditorList { Creditors = creditors, ReportDate = DateTime.Now };
    }

    public async Task<SalesSummary> GetSalesAnalysisAsync(DateTime startDate, DateTime endDate)
    {
        var allSales = await _salesRepository.GetAllAsync();
        
        return new SalesSummary
        {
            Date = DateTime.Now,
            TotalSales = allSales.Where(s => s.OrderDate >= startDate && s.OrderDate <= endDate).Sum(s => s.TotalAmount) ?? 0,
            OrderCount = allSales.Count(s => s.OrderDate >= startDate && s.OrderDate <= endDate),
            AverageOrderValue = allSales
                .Where(s => s.OrderDate >= startDate && s.OrderDate <= endDate)
                .Average(s => s.TotalAmount) ?? 0,
            TopSellingProduct = await GetTopSellingProduct(startDate, endDate)
        };
    }

    public async Task<string> ExportReportAsync(ReportType reportType, DateTime startDate, DateTime endDate)
    {
        // Example: Export report as PDF or Excel using EPPlus/iTextSharp
        // Actual implementation would generate the file and return the file path or download link
        string fileType = reportType == ReportType.Excel ? "xlsx" : "pdf";
        string fileName = $"FinancialReport_{reportType}_{startDate:yyyyMMdd}_{endDate:yyyyMMdd}.{fileType}";
        // TODO: Generate and save the file using EPPlus/iTextSharp
        // For now, return a simulated file path
        return $"/exports/{fileName}";
    }

    public async Task<(bool Success, string Message)> ScheduleReportAsync(ReportSchedule schedule)
    {
        // Example: Save schedule to database and set up background job
        // Actual implementation would persist the schedule and trigger background generation
        // For now, simulate scheduling
        return (true, $"Financial report scheduled: {schedule.ReportType} on {schedule.ScheduleDate:yyyy-MM-dd}");
    }

    private async Task<Product> GetTopSellingProduct(DateTime startDate, DateTime endDate)
    {
        var allSales = await _salesRepository.GetAllAsync();
        
        var topProductId = allSales
            .Where(s => s.OrderDate >= startDate && s.OrderDate <= endDate)
            .SelectMany(s => s.Items)
            .GroupBy(i => i.ProductId)
            .OrderByDescending(g => g.Sum(i => i.Quantity))
            .Select(g => g.Key)
            .FirstOrDefault();
            
        if (topProductId.HasValue)
        {
            return await _productRepository.GetByIdAsync(topProductId.Value);
        }
        
        return null;
    }

    public async Task<BankingResult> ProcessBankingTransactionAsync(BankingTransaction transaction)
    {
        // TODO: Implement banking transaction logic
        return new BankingResult();
    }

    public async Task<ReconciliationResult> ReconcileAccountsAsync(DateTime date)
    {
        // TODO: Implement reconciliation logic
        return new ReconciliationResult();
    }


    public async Task<FinancialReport> GenerateTrialBalanceAsync(DateTime asOfDate)
    {
        // TODO: Implement trial balance generation logic
        return new FinancialReport();
    }

    public async Task<FinancialReport> GenerateIntegratedReportAsync(DateTime asOfDate)
    {
        // TODO: Integrate with asset and inventory data for accurate reporting
        return new FinancialReport();
    }

    public async Task<string> ExportBalanceSheetAsync(DateTime reportDate, string filePath)
    {
        var report = await GenerateBalanceSheetAsync(reportDate);
        return await _reportExportService.ExportFinancialReportToPdfAsync(report, filePath);
    }

    public async Task<string> ExportIncomeStatementAsync(DateTime startDate, DateTime endDate, string filePath)
    {
        var report = await GenerateIncomeStatementAsync(startDate, endDate);
        return await _reportExportService.ExportFinancialReportToPdfAsync(report, filePath);
    }

    public async Task<string> ExportCashFlowStatementAsync(DateTime startDate, DateTime endDate, string filePath)
    {
        var report = await GenerateCashFlowStatementAsync(startDate, endDate);
        return await _reportExportService.ExportFinancialReportToPdfAsync(report, filePath);
    }

    public async Task<string> ExportTrialBalanceAsync(DateTime asOfDate, string filePath)
    {
        var report = await GenerateTrialBalanceAsync(asOfDate);
        return await _reportExportService.ExportFinancialReportToPdfAsync(report, filePath);
    }
}
