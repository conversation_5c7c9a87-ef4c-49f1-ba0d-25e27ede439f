using CakeBistro.Core.Models;
using CakeBistro.Core.Interfaces;
using Microsoft.EntityFrameworkCore;
using System.Linq.Expressions;

namespace CakeBistro.Repositories
{
    public class DepreciationRepository : BaseRepository<DepreciationRecord>, IDepreciationRepository
    {
        public DepreciationRepository(CakeBistroContext context) : base(context)
        {
        }

        public async Task<IEnumerable<DepreciationRecord>> GetByAssetAsync(int assetId)
        {
            return await _context.DepreciationRecords
                .Where(d => d.AssetId == assetId)
                .ToListAsync();
        }

        public async Task<decimal> GetTotalDepreciationAsync(int assetId)
        {
            return await _context.DepreciationRecords
                .Where(d => d.AssetId == assetId)
                .SumAsync(d => d.Amount);
        }
    }
}
