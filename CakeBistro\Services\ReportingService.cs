using CakeBistro.Models;
using CakeBistro.Repositories;
using CakeBistro.Services;

public class ReportingService : IReportingService
{
    private readonly IReportRepository _reportRepository;
    private readonly IRawMaterialRepository _rawMaterialRepository;
    private readonly IReportExportService _reportExportService;
    private readonly IRepository<SalesTransaction> _salesRepository;
    private readonly IRepository<FinishedProduct> _productRepository;
    private readonly IInventoryRepository _inventoryRepository;
    private readonly IFinanceRepository _financeRepository;
    private readonly IAssetRepository _assetRepository;

    public ReportingService(
        IRepository<SalesTransaction> salesRepository,
        IRepository<FinishedProduct> productRepository,
        IInventoryRepository inventoryRepository,
        IFinanceRepository financeRepository,
        IAssetRepository assetRepository,
        IReportRepository reportRepository,
        IRawMaterialRepository rawMaterialRepository,
        IReportExportService reportExportService
    )
    {
        _salesRepository = salesRepository;
        _productRepository = productRepository;
        _inventoryRepository = inventoryRepository;
        _financeRepository = financeRepository;
        _assetRepository = assetRepository;
        _reportRepository = reportRepository;
        _rawMaterialRepository = rawMaterialRepository;
        _reportExportService = reportExportService;
    }

    public async Task<SalesSummaryReport> GenerateSalesSummaryReportAsync(
        DateTime startDate,
        DateTime endDate
    )
    {
        // Fetch all sales transactions in the date range
        var sales = (await _salesRepository.GetAllAsync())
            .Where(s => s.Date >= startDate && s.Date <= endDate)
            .ToList();

        var report = new SalesSummaryReport
        {
            StartDate = startDate,
            EndDate = endDate,
            TotalSales = sales.Sum(s => s.Total),
            OrderCount = sales.Count,
            AverageOrderValue = sales.Count > 0 ? sales.Average(s => s.Total) : 0,
            ProductSummaries = sales
                .GroupBy(s => s.ProductName)
                .Select(g => new ProductSalesSummary
                {
                    ProductId = 0, // ProductId not available in SalesTransaction
                    ProductName = g.Key,
                    TotalSales = g.Sum(x => x.Total),
                    TotalProfit = 0, // Profit calculation requires cost data
                })
                .OrderByDescending(ps => ps.TotalSales)
                .ToList(),
        };

        // Set top selling product
        report.TopSellingProduct = report.ProductSummaries.FirstOrDefault()?.ProductName;

        // TotalProfit calculation would require cost/profit data per transaction
        report.TotalProfit = report.ProductSummaries.Sum(ps => ps.TotalProfit);

        return report;
    }

    public async Task<VehicleReport> GenerateVehicleReportAsync(
        DateTime startDate,
        DateTime endDate
    )
    {
        return await Task.FromResult(
            new VehicleReport { StartDate = startDate, EndDate = endDate }
        );
    }

    public async Task<DistributionReport> GenerateDistributionReportAsync(
        DateTime startDate,
        DateTime endDate
    )
    {
        return await Task.FromResult(
            new DistributionReport { StartDate = startDate, EndDate = endDate }
        );
    }

    public async Task<AnalysisReport> GenerateAnalysisReportAsync(
        DateTime startDate,
        DateTime endDate
    )
    {
        return await Task.FromResult(
            new AnalysisReport { StartDate = startDate, EndDate = endDate }
        );
    }

    public async Task<StockValuation> GetStockValuationAsync(DateTime asOfDate)
    {
        // Fetch all inventory batches (with navigation property Product)
        var batches = (await _inventoryRepository.GetAllAsync()).ToList();
        var profitability = new List<CakeBistro.Models.ProfitabilityAnalysis>();
        var items = batches
            .GroupBy(b => b.ProductId)
            .Select(g =>
            {
                var productId = g.Key;
                var productName = $"Product {productId}";
                var quantity = g.Sum(b => b.Quantity);
                var pa = profitability.FirstOrDefault(p => p.ProductId == productId);
                decimal unitCost = pa?.ProductionCost ?? 0;
                decimal totalCost = quantity * unitCost;
                return new StockItemValuation
                {
                    ProductId = productId,
                    ProductName = productName,
                    Quantity = quantity,
                    UnitCost = unitCost,
                    TotalCost = totalCost,
                };
            })
            .ToList();

        var totalStockValue = items.Sum(i => i.TotalCost);
        // LowStockCount cannot be calculated without MinimumStockThreshold per product
        var report = new StockValuation
        {
            AsOfDate = asOfDate,
            Items = items,
            TotalStockValue = totalStockValue,
            LowStockCount = 0, // Not available without threshold
        };
        return report;
    }

    public async Task<ExpiringBatchReport> GetExpiringBatchReportAsync(DateTime asOfDate)
    {
        // Fetch all inventory batches
        var batches = (await _inventoryRepository.GetAllAsync()).ToList();
        var now = asOfDate;
        var expiring30 = batches
            .Where(b => b.ExpiryDate > now && b.ExpiryDate <= now.AddDays(30))
            .ToList();
        var expiring7 = batches
            .Where(b => b.ExpiryDate > now && b.ExpiryDate <= now.AddDays(7))
            .ToList();
        var expired = batches.Where(b => b.ExpiryDate <= now).ToList();

        ExpiringBatchDetail ToDetail(dynamic b) =>
            new ExpiringBatchDetail
            {
                RawMaterialId = 0, // Not available from batch
                RawMaterialName = null, // Not available from batch
                BatchNumber = b.BatchNumber,
                Quantity = b.Quantity,
                ExpiryDate = b.ExpiryDate,
                StorageLocation = null, // Not available from batch
            };

        var report = new ExpiringBatchReport
        {
            ReportDate = asOfDate,
            BatchesExpiringWithin30Days = expiring30.Select(b => ToDetail(b)).ToList(),
            BatchesExpiringWithin7Days = expiring7.Select(b => ToDetail(b)).ToList(),
            ExpiredBatches = expired.Select(b => ToDetail(b)).ToList(),
            TotalExpiringBatches = expiring30.Count + expiring7.Count,
            Recommendations = new List<string>(),
        };
        return report;
    }

    public async Task<AccountStatementReport> GetAccountStatementReportAsync(
        int accountId,
        DateTime startDate,
        DateTime endDate
    )
    {
        // Example logic: populate with dummy data, replace with real logic as needed
        var report = new AccountStatementReport
        {
            AccountId = accountId,
            AccountName = $"Account {accountId}",
            StartDate = startDate,
            EndDate = endDate,
            Transactions = new List<Transaction>()
            {
                new Transaction
                {
                    Date = startDate,
                    Description = "Opening Balance",
                    Amount = 0,
                    RunningBalance = 0,
                },
                new Transaction
                {
                    Date = endDate,
                    Description = "Closing Balance",
                    Amount = 0,
                    RunningBalance = 0,
                },
            },
        };
        return await Task.FromResult(report);
    }

    public async Task<DebtorList> GetDebtorListAsync()
    {
        // Example logic: populate with dummy data, replace with real logic as needed
        var list = new DebtorList
        {
            Debtors = new List<Debtor>
            {
                new Debtor
                {
                    CustomerId = 1,
                    CustomerName = "Customer A",
                    OutstandingAmount = 100.50m,
                },
                new Debtor
                {
                    CustomerId = 2,
                    CustomerName = "Customer B",
                    OutstandingAmount = 250.00m,
                },
            },
        };
        return await Task.FromResult(list);
    }

    public async Task<CreditorList> GetCreditorListAsync()
    {
        // Example logic: populate with dummy data, replace with real logic as needed
        var list = new CreditorList
        {
            Creditors = new List<Creditor>
            {
                new Creditor
                {
                    SupplierId = 1,
                    SupplierName = "Supplier X",
                    OutstandingAmount = 300.00m,
                },
                new Creditor
                {
                    SupplierId = 2,
                    SupplierName = "Supplier Y",
                    OutstandingAmount = 150.75m,
                },
            },
        };
        return await Task.FromResult(list);
    }
}
