<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2022/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:local="clr-namespace:CakeBistro.Views"
             x:Class="CakeBistro.Views.MainPage"
             Title="M-CakeBistro">
    <ContentPage.Content>
        <TabbedPage x:Class="CakeBistro.Views.MainPage.TabbedMainPage">
            <!-- Inventory Management -->
            <NavigationPage Title="Inventory">
                <x:Arguments>
                    <local:InventoryPage />
                </x:Arguments>
            </NavigationPage>
            
            <!-- Production Control -->
            <NavigationPage Title="Production">
                <x:Arguments>
                    <local:ProductionPage />
                </x:Arguments>
            </NavigationPage>
            
            <!-- Sales & Distribution -->
            <NavigationPage Title="Sales">
                <x:Arguments>
                    <local:SalesOrderPage />
                </x:Arguments>
            </NavigationPage>
            
            <!-- Reporting -->
            <NavigationPage Title="Reporting">
                <x:Arguments>
                    <local:ReportingPage />
                </x:Arguments>
            </NavigationPage>
            
            <!-- Accounting -->
            <NavigationPage Title="Finance">
                <x:Arguments>
                    <local:FinancePage />
                </x:Arguments>
            </NavigationPage>
            
            <!-- Asset Management -->
            <NavigationPage Title="Assets">
                <x:Arguments>
                    <local:AssetPage />
                </x:Arguments>
            </NavigationPage>
        </TabbedPage>
    </ContentPage.Content>
    <ContentPage.BottomBar>
        <Button Text="Barcode Scanner"
                Command="{Binding NavigateCommand}"
                CommandParameter="BarcodeScannerPage"
                Margin="20"
                HorizontalOptions="Center" />
    </ContentPage.BottomBar>
</ContentPage>