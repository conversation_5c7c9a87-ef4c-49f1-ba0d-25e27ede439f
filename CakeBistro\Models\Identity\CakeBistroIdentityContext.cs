using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using System;

namespace CakeBistro.Models.Identity
{
    public class ApplicationUser : IdentityUser
    {
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public DateTime DateOfBirth { get; set; }
        public string Position { get; set; }
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    }

    public class ApplicationRole : IdentityRole
    {
        public string Description { get; set; }
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    }

    public class CakeBistroIdentityContext : IdentityDbContext<ApplicationUser, ApplicationRole, string>
    {
        public CakeBistroIdentityContext(DbContextOptions<CakeBistroIdentityContext> options)
            : base(options)
        {
        }

        protected override void OnModelCreating(ModelBuilder builder)
        {
            base.OnModelCreating(builder);
            
            // Configure table names to match our migration
            builder.Entity<ApplicationUser>().ToTable("AspNetUsers");
            builder.Entity<ApplicationRole>().ToTable("AspNetRoles");
            builder.Entity<IdentityUserClaim<string>>().ToTable("AspNetUserClaims");
            builder.Entity<IdentityUserRole<string>>().ToTable("AspNetUserRoles");
            builder.Entity<IdentityUserLogin<string>>().ToTable("AspNetUserLogins");
            builder.Entity<IdentityRoleClaim<string>>().ToTable("AspNetRoleClaims");
            builder.Entity<IdentityUserToken<string>>().ToTable("AspNetUserTokens");
            
            // Configure entity relationships and constraints
            // User relationships
            builder.Entity<ApplicationUser>(entity =>
            {
                entity.Property(u => u.CreatedAt).HasColumnType("TEXT").IsRequired();
                entity.Property(u => u.UpdatedAt).HasColumnType("TEXT").IsRequired();
                
                entity.HasMany(u => u.UserClaims)
                    .WithOne()
                    .HasForeignKey(uc => uc.UserId)
                    .IsRequired();
                
                entity.HasMany(u => u.UserLogins)
                    .WithOne()
                    .HasForeignKey(ul => ul.UserId)
                    .IsRequired();
                
                entity.HasMany(u => u.UserTokens)
                    .WithOne()
                    .HasForeignKey(ut => ut.UserId)
                    .IsRequired();
                
                entity.HasMany(u => u.UserRoles)
                    .WithOne()
                    .HasForeignKey(ur => ur.UserId)
                    .IsRequired();
            });
            
            // Role relationships
            builder.Entity<ApplicationRole>(entity =>
            {
                entity.Property(r => r.Description).HasColumnType("TEXT").HasMaxLength(500);
                entity.Property(r => r.CreatedAt).HasColumnType("TEXT").IsRequired();
                entity.Property(r => r.UpdatedAt).HasColumnType("TEXT").IsRequired();
                
                entity.HasMany(r => r.RoleClaims)
                    .WithOne()
                    .HasForeignKey(rc => rc.RoleId)
                    .IsRequired();
                
                entity.HasMany(r => r.RoleUsers)
                    .WithOne()
                    .HasForeignKey(ru => ru.RoleId)
                    .IsRequired();
            });
        }
    }
}