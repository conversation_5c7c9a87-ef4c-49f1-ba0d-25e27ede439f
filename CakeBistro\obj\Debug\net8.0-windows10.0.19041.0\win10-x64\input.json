{"ProjectPath": "c:\\Users\\<USER>\\Desktop\\CakeBistro\\CakeBistro\\CakeBistro.csproj", "Language": "C#", "LanguageSourceExtension": ".cs", "OutputPath": "obj\\Debug\\net8.0-windows10.0.19041.0\\win10-x64\\", "ReferenceAssemblies": [{"DependentUpon": "", "FullPath": "C:\\Users\\<USER>\\.nuget\\packages\\bcrypt.net-next\\4.0.3\\lib\\net6.0\\BCrypt.Net-Next.dll", "ItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\bcrypt.net-next\\4.0.3\\lib\\net6.0\\BCrypt.Net-Next.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Users\\<USER>\\.nuget\\packages\\bouncycastle.cryptography\\2.6.1\\lib\\net6.0\\BouncyCastle.Cryptography.dll", "ItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\bouncycastle.cryptography\\2.6.1\\lib\\net6.0\\BouncyCastle.Cryptography.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "c:\\Users\\<USER>\\Desktop\\CakeBistro\\CakeBistro.Core\\bin\\Debug\\net8.0-windows10.0.19041.0\\CakeBistro.Core.dll", "ItemSpec": "c:\\Users\\<USER>\\Desktop\\CakeBistro\\CakeBistro.Core\\bin\\Debug\\net8.0-windows10.0.19041.0\\CakeBistro.Core.dll", "IsSystemReference": false, "IsNuGetReference": false, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Users\\<USER>\\.nuget\\packages\\communitytoolkit.maui.core\\7.0.1\\lib\\net8.0-windows10.0.19041\\CommunityToolkit.Maui.Core.dll", "ItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\communitytoolkit.maui.core\\7.0.1\\lib\\net8.0-windows10.0.19041\\CommunityToolkit.Maui.Core.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Users\\<USER>\\.nuget\\packages\\communitytoolkit.maui\\7.0.1\\lib\\net8.0-windows10.0.19041\\CommunityToolkit.Maui.dll", "ItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\communitytoolkit.maui\\7.0.1\\lib\\net8.0-windows10.0.19041\\CommunityToolkit.Maui.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Users\\<USER>\\.nuget\\packages\\communitytoolkit.mvvm\\8.2.2\\lib\\net6.0\\CommunityToolkit.Mvvm.dll", "ItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\communitytoolkit.mvvm\\8.2.2\\lib\\net6.0\\CommunityToolkit.Mvvm.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Users\\<USER>\\.nuget\\packages\\epplus\\8.0.7\\lib\\net8.0\\EPPlus.dll", "ItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\epplus\\8.0.7\\lib\\net8.0\\EPPlus.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Users\\<USER>\\.nuget\\packages\\epplus.interfaces\\8.0.0\\lib\\net8.0\\EPPlus.Interfaces.dll", "ItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\epplus.interfaces\\8.0.0\\lib\\net8.0\\EPPlus.Interfaces.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Users\\<USER>\\.nuget\\packages\\humanizer.core\\2.14.1\\lib\\net6.0\\Humanizer.dll", "ItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\humanizer.core\\2.14.1\\lib\\net6.0\\Humanizer.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Users\\<USER>\\.nuget\\packages\\itextsharp.lgplv2.core\\3.7.4\\lib\\net8.0\\iTextSharp.LGPLv2.Core.dll", "ItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\itextsharp.lgplv2.core\\3.7.4\\lib\\net8.0\\iTextSharp.LGPLv2.Core.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Users\\<USER>\\.nuget\\packages\\microcharts.maui\\1.0.1\\lib\\net8.0-windows10.0.19041\\Microcharts.Maui.dll", "ItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microcharts.maui\\1.0.1\\lib\\net8.0-windows10.0.19041\\Microcharts.Maui.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.bcl.asyncinterfaces\\6.0.0\\lib\\netstandard2.1\\Microsoft.Bcl.AsyncInterfaces.dll", "ItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.bcl.asyncinterfaces\\6.0.0\\lib\\netstandard2.1\\Microsoft.Bcl.AsyncInterfaces.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.codeanalysis.csharp\\4.5.0\\lib\\netcoreapp3.1\\Microsoft.CodeAnalysis.CSharp.dll", "ItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.codeanalysis.csharp\\4.5.0\\lib\\netcoreapp3.1\\Microsoft.CodeAnalysis.CSharp.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.codeanalysis.csharp.workspaces\\4.5.0\\lib\\netcoreapp3.1\\Microsoft.CodeAnalysis.CSharp.Workspaces.dll", "ItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.codeanalysis.csharp.workspaces\\4.5.0\\lib\\netcoreapp3.1\\Microsoft.CodeAnalysis.CSharp.Workspaces.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.codeanalysis.common\\4.5.0\\lib\\netcoreapp3.1\\Microsoft.CodeAnalysis.dll", "ItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.codeanalysis.common\\4.5.0\\lib\\netcoreapp3.1\\Microsoft.CodeAnalysis.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.codeanalysis.workspaces.common\\4.5.0\\lib\\netcoreapp3.1\\Microsoft.CodeAnalysis.Workspaces.dll", "ItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.codeanalysis.workspaces.common\\4.5.0\\lib\\netcoreapp3.1\\Microsoft.CodeAnalysis.Workspaces.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\Microsoft.CSharp.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\Microsoft.CSharp.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.data.sqlite.core\\8.0.1\\lib\\net8.0\\Microsoft.Data.Sqlite.dll", "ItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.data.sqlite.core\\8.0.1\\lib\\net8.0\\Microsoft.Data.Sqlite.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.entityframeworkcore.abstractions\\8.0.1\\lib\\net8.0\\Microsoft.EntityFrameworkCore.Abstractions.dll", "ItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.entityframeworkcore.abstractions\\8.0.1\\lib\\net8.0\\Microsoft.EntityFrameworkCore.Abstractions.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.entityframeworkcore.design\\8.0.1\\lib\\net8.0\\Microsoft.EntityFrameworkCore.Design.dll", "ItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.entityframeworkcore.design\\8.0.1\\lib\\net8.0\\Microsoft.EntityFrameworkCore.Design.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.entityframeworkcore\\8.0.1\\lib\\net8.0\\Microsoft.EntityFrameworkCore.dll", "ItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.entityframeworkcore\\8.0.1\\lib\\net8.0\\Microsoft.EntityFrameworkCore.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.entityframeworkcore.relational\\8.0.1\\lib\\net8.0\\Microsoft.EntityFrameworkCore.Relational.dll", "ItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.entityframeworkcore.relational\\8.0.1\\lib\\net8.0\\Microsoft.EntityFrameworkCore.Relational.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.entityframeworkcore.sqlite.core\\8.0.1\\lib\\net8.0\\Microsoft.EntityFrameworkCore.Sqlite.dll", "ItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.entityframeworkcore.sqlite.core\\8.0.1\\lib\\net8.0\\Microsoft.EntityFrameworkCore.Sqlite.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.caching.abstractions\\8.0.0\\lib\\net8.0\\Microsoft.Extensions.Caching.Abstractions.dll", "ItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.caching.abstractions\\8.0.0\\lib\\net8.0\\Microsoft.Extensions.Caching.Abstractions.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.caching.memory\\8.0.0\\lib\\net8.0\\Microsoft.Extensions.Caching.Memory.dll", "ItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.caching.memory\\8.0.0\\lib\\net8.0\\Microsoft.Extensions.Caching.Memory.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.abstractions\\8.0.0\\lib\\net8.0\\Microsoft.Extensions.Configuration.Abstractions.dll", "ItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.abstractions\\8.0.0\\lib\\net8.0\\Microsoft.Extensions.Configuration.Abstractions.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration\\8.0.0\\lib\\net8.0\\Microsoft.Extensions.Configuration.dll", "ItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration\\8.0.0\\lib\\net8.0\\Microsoft.Extensions.Configuration.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.fileextensions\\8.0.1\\lib\\net8.0\\Microsoft.Extensions.Configuration.FileExtensions.dll", "ItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.fileextensions\\8.0.1\\lib\\net8.0\\Microsoft.Extensions.Configuration.FileExtensions.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.json\\8.0.1\\lib\\net8.0\\Microsoft.Extensions.Configuration.Json.dll", "ItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.json\\8.0.1\\lib\\net8.0\\Microsoft.Extensions.Configuration.Json.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencyinjection.abstractions\\8.0.2\\lib\\net8.0\\Microsoft.Extensions.DependencyInjection.Abstractions.dll", "ItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencyinjection.abstractions\\8.0.2\\lib\\net8.0\\Microsoft.Extensions.DependencyInjection.Abstractions.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencyinjection\\8.0.1\\lib\\net8.0\\Microsoft.Extensions.DependencyInjection.dll", "ItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencyinjection\\8.0.1\\lib\\net8.0\\Microsoft.Extensions.DependencyInjection.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencymodel\\8.0.0\\lib\\net8.0\\Microsoft.Extensions.DependencyModel.dll", "ItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencymodel\\8.0.0\\lib\\net8.0\\Microsoft.Extensions.DependencyModel.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.fileproviders.abstractions\\8.0.0\\lib\\net8.0\\Microsoft.Extensions.FileProviders.Abstractions.dll", "ItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.fileproviders.abstractions\\8.0.0\\lib\\net8.0\\Microsoft.Extensions.FileProviders.Abstractions.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.fileproviders.physical\\8.0.0\\lib\\net8.0\\Microsoft.Extensions.FileProviders.Physical.dll", "ItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.fileproviders.physical\\8.0.0\\lib\\net8.0\\Microsoft.Extensions.FileProviders.Physical.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.filesystemglobbing\\8.0.0\\lib\\net8.0\\Microsoft.Extensions.FileSystemGlobbing.dll", "ItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.filesystemglobbing\\8.0.0\\lib\\net8.0\\Microsoft.Extensions.FileSystemGlobbing.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging.abstractions\\8.0.2\\lib\\net8.0\\Microsoft.Extensions.Logging.Abstractions.dll", "ItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging.abstractions\\8.0.2\\lib\\net8.0\\Microsoft.Extensions.Logging.Abstractions.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging.debug\\8.0.1\\lib\\net8.0\\Microsoft.Extensions.Logging.Debug.dll", "ItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging.debug\\8.0.1\\lib\\net8.0\\Microsoft.Extensions.Logging.Debug.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging\\8.0.1\\lib\\net8.0\\Microsoft.Extensions.Logging.dll", "ItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging\\8.0.1\\lib\\net8.0\\Microsoft.Extensions.Logging.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.options\\8.0.2\\lib\\net8.0\\Microsoft.Extensions.Options.dll", "ItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.options\\8.0.2\\lib\\net8.0\\Microsoft.Extensions.Options.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.primitives\\8.0.0\\lib\\net8.0\\Microsoft.Extensions.Primitives.dll", "ItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.primitives\\8.0.0\\lib\\net8.0\\Microsoft.Extensions.Primitives.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.graphics.win2d\\1.2.0\\lib\\net6.0-windows10.0.19041.0\\Microsoft.Graphics.Canvas.Interop.dll", "ItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.graphics.win2d\\1.2.0\\lib\\net6.0-windows10.0.19041.0\\Microsoft.Graphics.Canvas.Interop.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.windowsappsdk\\1.5.240802000\\lib\\net6.0-windows10.0.18362.0\\Microsoft.InteractiveExperiences.Projection.dll", "ItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.windowsappsdk\\1.5.240802000\\lib\\net6.0-windows10.0.18362.0\\Microsoft.InteractiveExperiences.Projection.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.io.recyclablememorystream\\3.0.1\\lib\\net6.0\\Microsoft.IO.RecyclableMemoryStream.dll", "ItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.io.recyclablememorystream\\3.0.1\\lib\\net6.0\\Microsoft.IO.RecyclableMemoryStream.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.maui.controls.compatibility\\8.0.100\\lib\\net8.0-windows10.0.19041\\Microsoft.Maui.Controls.Compatibility.dll", "ItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.maui.controls.compatibility\\8.0.100\\lib\\net8.0-windows10.0.19041\\Microsoft.Maui.Controls.Compatibility.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.maui.controls.core\\8.0.100\\lib\\net8.0-windows10.0.19041\\Microsoft.Maui.Controls.dll", "ItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.maui.controls.core\\8.0.100\\lib\\net8.0-windows10.0.19041\\Microsoft.Maui.Controls.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.maui.controls.xaml\\8.0.100\\lib\\net8.0-windows10.0.19041\\Microsoft.Maui.Controls.Xaml.dll", "ItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.maui.controls.xaml\\8.0.100\\lib\\net8.0-windows10.0.19041\\Microsoft.Maui.Controls.Xaml.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.maui.core\\8.0.100\\lib\\net8.0-windows10.0.19041\\Microsoft.Maui.dll", "ItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.maui.core\\8.0.100\\lib\\net8.0-windows10.0.19041\\Microsoft.Maui.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.maui.essentials\\8.0.100\\lib\\net8.0-windows10.0.19041\\Microsoft.Maui.Essentials.dll", "ItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.maui.essentials\\8.0.100\\lib\\net8.0-windows10.0.19041\\Microsoft.Maui.Essentials.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.maui.graphics\\8.0.100\\lib\\net8.0-windows10.0.19041\\Microsoft.Maui.Graphics.dll", "ItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.maui.graphics\\8.0.100\\lib\\net8.0-windows10.0.19041\\Microsoft.Maui.Graphics.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.maui.graphics.win2d.winui.desktop\\8.0.100\\lib\\net8.0-windows10.0.19041\\Microsoft.Maui.Graphics.Win2D.WinUI.Desktop.dll", "ItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.maui.graphics.win2d.winui.desktop\\8.0.100\\lib\\net8.0-windows10.0.19041\\Microsoft.Maui.Graphics.Win2D.WinUI.Desktop.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\Microsoft.VisualBasic.Core.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\Microsoft.VisualBasic.Core.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\Microsoft.VisualBasic.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\Microsoft.VisualBasic.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\Microsoft.Win32.Primitives.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\Microsoft.Win32.Primitives.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\Microsoft.Win32.Registry.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\Microsoft.Win32.Registry.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.windowsappsdk\\1.5.240802000\\lib\\net6.0-windows10.0.18362.0\\Microsoft.Windows.ApplicationModel.DynamicDependency.Projection.dll", "ItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.windowsappsdk\\1.5.240802000\\lib\\net6.0-windows10.0.18362.0\\Microsoft.Windows.ApplicationModel.DynamicDependency.Projection.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.windowsappsdk\\1.5.240802000\\lib\\net6.0-windows10.0.18362.0\\Microsoft.Windows.ApplicationModel.Resources.Projection.dll", "ItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.windowsappsdk\\1.5.240802000\\lib\\net6.0-windows10.0.18362.0\\Microsoft.Windows.ApplicationModel.Resources.Projection.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.windowsappsdk\\1.5.240802000\\lib\\net6.0-windows10.0.18362.0\\Microsoft.Windows.ApplicationModel.WindowsAppRuntime.Projection.dll", "ItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.windowsappsdk\\1.5.240802000\\lib\\net6.0-windows10.0.18362.0\\Microsoft.Windows.ApplicationModel.WindowsAppRuntime.Projection.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.windowsappsdk\\1.5.240802000\\lib\\net6.0-windows10.0.18362.0\\Microsoft.Windows.AppLifecycle.Projection.dll", "ItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.windowsappsdk\\1.5.240802000\\lib\\net6.0-windows10.0.18362.0\\Microsoft.Windows.AppLifecycle.Projection.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.windowsappsdk\\1.5.240802000\\lib\\net6.0-windows10.0.18362.0\\Microsoft.Windows.AppNotifications.Builder.Projection.dll", "ItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.windowsappsdk\\1.5.240802000\\lib\\net6.0-windows10.0.18362.0\\Microsoft.Windows.AppNotifications.Builder.Projection.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.windowsappsdk\\1.5.240802000\\lib\\net6.0-windows10.0.18362.0\\Microsoft.Windows.AppNotifications.Projection.dll", "ItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.windowsappsdk\\1.5.240802000\\lib\\net6.0-windows10.0.18362.0\\Microsoft.Windows.AppNotifications.Projection.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.windowsappsdk\\1.5.240802000\\lib\\net6.0-windows10.0.18362.0\\Microsoft.Windows.Management.Deployment.Projection.dll", "ItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.windowsappsdk\\1.5.240802000\\lib\\net6.0-windows10.0.18362.0\\Microsoft.Windows.Management.Deployment.Projection.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.windowsappsdk\\1.5.240802000\\lib\\net6.0-windows10.0.18362.0\\Microsoft.Windows.PushNotifications.Projection.dll", "ItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.windowsappsdk\\1.5.240802000\\lib\\net6.0-windows10.0.18362.0\\Microsoft.Windows.PushNotifications.Projection.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.windows.sdk.net.ref\\10.0.19041.57\\lib\\net8.0\\Microsoft.Windows.SDK.NET.dll", "ItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.windows.sdk.net.ref\\10.0.19041.57\\lib\\net8.0\\Microsoft.Windows.SDK.NET.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.windowsappsdk\\1.5.240802000\\lib\\net6.0-windows10.0.18362.0\\Microsoft.Windows.Security.AccessControl.Projection.dll", "ItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.windowsappsdk\\1.5.240802000\\lib\\net6.0-windows10.0.18362.0\\Microsoft.Windows.Security.AccessControl.Projection.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.windowsappsdk\\1.5.240802000\\lib\\net6.0-windows10.0.18362.0\\Microsoft.Windows.System.Power.Projection.dll", "ItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.windowsappsdk\\1.5.240802000\\lib\\net6.0-windows10.0.18362.0\\Microsoft.Windows.System.Power.Projection.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.windowsappsdk\\1.5.240802000\\lib\\net6.0-windows10.0.18362.0\\Microsoft.Windows.System.Projection.dll", "ItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.windowsappsdk\\1.5.240802000\\lib\\net6.0-windows10.0.18362.0\\Microsoft.Windows.System.Projection.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.windowsappsdk\\1.5.240802000\\lib\\net6.0-windows10.0.18362.0\\Microsoft.Windows.Widgets.Projection.dll", "ItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.windowsappsdk\\1.5.240802000\\lib\\net6.0-windows10.0.18362.0\\Microsoft.Windows.Widgets.Projection.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.windowsappsdk\\1.5.240802000\\lib\\net6.0-windows10.0.18362.0\\Microsoft.WindowsAppRuntime.Bootstrap.Net.dll", "ItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.windowsappsdk\\1.5.240802000\\lib\\net6.0-windows10.0.18362.0\\Microsoft.WindowsAppRuntime.Bootstrap.Net.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.windowsappsdk\\1.5.240802000\\lib\\net6.0-windows10.0.18362.0\\Microsoft.WinUI.dll", "ItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.windowsappsdk\\1.5.240802000\\lib\\net6.0-windows10.0.18362.0\\Microsoft.WinUI.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Users\\<USER>\\.nuget\\packages\\mono.texttemplating\\2.2.1\\lib\\netstandard2.0\\Mono.TextTemplating.dll", "ItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\mono.texttemplating\\2.2.1\\lib\\netstandard2.0\\Mono.TextTemplating.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\mscorlib.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\mscorlib.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\netstandard.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\netstandard.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Users\\<USER>\\.nuget\\packages\\skiasharp\\3.119.0\\ref\\net8.0-windows10.0.19041\\SkiaSharp.dll", "ItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\skiasharp\\3.119.0\\ref\\net8.0-windows10.0.19041\\SkiaSharp.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Users\\<USER>\\.nuget\\packages\\skiasharp.views.maui.controls\\2.88.9\\lib\\net6.0-windows10.0.18362.0\\SkiaSharp.Views.Maui.Controls.dll", "ItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\skiasharp.views.maui.controls\\2.88.9\\lib\\net6.0-windows10.0.18362.0\\SkiaSharp.Views.Maui.Controls.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Users\\<USER>\\.nuget\\packages\\skiasharp.views.maui.core\\2.88.9\\lib\\net6.0-windows10.0.18362.0\\SkiaSharp.Views.Maui.Core.dll", "ItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\skiasharp.views.maui.core\\2.88.9\\lib\\net6.0-windows10.0.18362.0\\SkiaSharp.Views.Maui.Core.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Users\\<USER>\\.nuget\\packages\\skiasharp.views.winui\\2.88.9\\lib\\net6.0-windows10.0.19041.0\\SkiaSharp.Views.Windows.dll", "ItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\skiasharp.views.winui\\2.88.9\\lib\\net6.0-windows10.0.19041.0\\SkiaSharp.Views.Windows.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Users\\<USER>\\.nuget\\packages\\sqlitepclraw.bundle_e_sqlite3\\2.1.6\\lib\\netstandard2.0\\SQLitePCLRaw.batteries_v2.dll", "ItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\sqlitepclraw.bundle_e_sqlite3\\2.1.6\\lib\\netstandard2.0\\SQLitePCLRaw.batteries_v2.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Users\\<USER>\\.nuget\\packages\\sqlitepclraw.core\\2.1.6\\lib\\netstandard2.0\\SQLitePCLRaw.core.dll", "ItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\sqlitepclraw.core\\2.1.6\\lib\\netstandard2.0\\SQLitePCLRaw.core.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Users\\<USER>\\.nuget\\packages\\sqlitepclraw.provider.e_sqlite3\\2.1.6\\lib\\net6.0-windows7.0\\SQLitePCLRaw.provider.e_sqlite3.dll", "ItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\sqlitepclraw.provider.e_sqlite3\\2.1.6\\lib\\net6.0-windows7.0\\SQLitePCLRaw.provider.e_sqlite3.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.AppContext.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.AppContext.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Buffers.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Buffers.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Users\\<USER>\\.nuget\\packages\\system.codedom\\4.4.0\\ref\\netstandard2.0\\System.CodeDom.dll", "ItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\system.codedom\\4.4.0\\ref\\netstandard2.0\\System.CodeDom.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Collections.Concurrent.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Collections.Concurrent.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Collections.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Collections.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Collections.Immutable.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Collections.Immutable.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Collections.NonGeneric.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Collections.NonGeneric.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Collections.Specialized.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Collections.Specialized.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.ComponentModel.Annotations.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.ComponentModel.Annotations.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.ComponentModel.DataAnnotations.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.ComponentModel.DataAnnotations.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.ComponentModel.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.ComponentModel.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.ComponentModel.EventBasedAsync.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.ComponentModel.EventBasedAsync.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.ComponentModel.Primitives.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.ComponentModel.Primitives.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.ComponentModel.TypeConverter.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.ComponentModel.TypeConverter.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Users\\<USER>\\.nuget\\packages\\system.composition.attributedmodel\\6.0.0\\lib\\net6.0\\System.Composition.AttributedModel.dll", "ItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\system.composition.attributedmodel\\6.0.0\\lib\\net6.0\\System.Composition.AttributedModel.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Users\\<USER>\\.nuget\\packages\\system.composition.convention\\6.0.0\\lib\\net6.0\\System.Composition.Convention.dll", "ItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\system.composition.convention\\6.0.0\\lib\\net6.0\\System.Composition.Convention.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Users\\<USER>\\.nuget\\packages\\system.composition.hosting\\6.0.0\\lib\\net6.0\\System.Composition.Hosting.dll", "ItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\system.composition.hosting\\6.0.0\\lib\\net6.0\\System.Composition.Hosting.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Users\\<USER>\\.nuget\\packages\\system.composition.runtime\\6.0.0\\lib\\net6.0\\System.Composition.Runtime.dll", "ItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\system.composition.runtime\\6.0.0\\lib\\net6.0\\System.Composition.Runtime.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Users\\<USER>\\.nuget\\packages\\system.composition.typedparts\\6.0.0\\lib\\net6.0\\System.Composition.TypedParts.dll", "ItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\system.composition.typedparts\\6.0.0\\lib\\net6.0\\System.Composition.TypedParts.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Configuration.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Configuration.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Console.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Console.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Core.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Core.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Data.Common.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Data.Common.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Data.DataSetExtensions.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Data.DataSetExtensions.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Data.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Data.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Diagnostics.Contracts.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Diagnostics.Contracts.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Diagnostics.Debug.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Diagnostics.Debug.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Diagnostics.DiagnosticSource.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Diagnostics.DiagnosticSource.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Diagnostics.FileVersionInfo.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Diagnostics.FileVersionInfo.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Diagnostics.Process.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Diagnostics.Process.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Diagnostics.StackTrace.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Diagnostics.StackTrace.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Diagnostics.TextWriterTraceListener.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Diagnostics.TextWriterTraceListener.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Diagnostics.Tools.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Diagnostics.Tools.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Diagnostics.TraceSource.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Diagnostics.TraceSource.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Diagnostics.Tracing.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Diagnostics.Tracing.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Drawing.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Drawing.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Drawing.Primitives.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Drawing.Primitives.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Dynamic.Runtime.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Dynamic.Runtime.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Formats.Asn1.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Formats.Asn1.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Formats.Tar.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Formats.Tar.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Globalization.Calendars.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Globalization.Calendars.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Globalization.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Globalization.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Globalization.Extensions.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Globalization.Extensions.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.IO.Compression.Brotli.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.IO.Compression.Brotli.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.IO.Compression.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.IO.Compression.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.IO.Compression.FileSystem.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.IO.Compression.FileSystem.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.IO.Compression.ZipFile.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.IO.Compression.ZipFile.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.IO.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.IO.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.IO.FileSystem.AccessControl.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.IO.FileSystem.AccessControl.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.IO.FileSystem.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.IO.FileSystem.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.IO.FileSystem.DriveInfo.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.IO.FileSystem.DriveInfo.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.IO.FileSystem.Primitives.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.IO.FileSystem.Primitives.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.IO.FileSystem.Watcher.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.IO.FileSystem.Watcher.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.IO.IsolatedStorage.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.IO.IsolatedStorage.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.IO.MemoryMappedFiles.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.IO.MemoryMappedFiles.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Users\\<USER>\\.nuget\\packages\\system.io.pipelines\\6.0.3\\lib\\net6.0\\System.IO.Pipelines.dll", "ItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\system.io.pipelines\\6.0.3\\lib\\net6.0\\System.IO.Pipelines.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.IO.Pipes.AccessControl.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.IO.Pipes.AccessControl.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.IO.Pipes.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.IO.Pipes.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.IO.UnmanagedMemoryStream.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.IO.UnmanagedMemoryStream.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Linq.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Linq.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Linq.Expressions.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Linq.Expressions.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Linq.Parallel.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Linq.Parallel.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Linq.Queryable.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Linq.Queryable.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Memory.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Memory.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Net.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Net.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Net.Http.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Net.Http.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Net.Http.Json.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Net.Http.Json.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Net.HttpListener.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Net.HttpListener.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Net.Mail.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Net.Mail.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Net.NameResolution.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Net.NameResolution.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Net.NetworkInformation.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Net.NetworkInformation.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Net.Ping.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Net.Ping.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Net.Primitives.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Net.Primitives.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Net.Quic.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Net.Quic.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Net.Requests.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Net.Requests.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Net.Security.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Net.Security.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Net.ServicePoint.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Net.ServicePoint.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Net.Sockets.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Net.Sockets.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Net.WebClient.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Net.WebClient.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Net.WebHeaderCollection.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Net.WebHeaderCollection.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Net.WebProxy.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Net.WebProxy.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Net.WebSockets.Client.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Net.WebSockets.Client.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Net.WebSockets.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Net.WebSockets.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Numerics.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Numerics.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Numerics.Vectors.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Numerics.Vectors.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.ObjectModel.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.ObjectModel.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Reflection.DispatchProxy.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Reflection.DispatchProxy.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Reflection.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Reflection.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Reflection.Emit.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Reflection.Emit.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Reflection.Emit.ILGeneration.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Reflection.Emit.ILGeneration.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Reflection.Emit.Lightweight.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Reflection.Emit.Lightweight.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Reflection.Extensions.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Reflection.Extensions.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Reflection.Metadata.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Reflection.Metadata.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Reflection.Primitives.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Reflection.Primitives.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Reflection.TypeExtensions.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Reflection.TypeExtensions.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Resources.Reader.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Resources.Reader.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Resources.ResourceManager.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Resources.ResourceManager.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Resources.Writer.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Resources.Writer.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Runtime.CompilerServices.Unsafe.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Runtime.CompilerServices.Unsafe.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Runtime.CompilerServices.VisualC.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Runtime.CompilerServices.VisualC.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Runtime.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Runtime.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Runtime.Extensions.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Runtime.Extensions.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Runtime.Handles.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Runtime.Handles.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Runtime.InteropServices.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Runtime.InteropServices.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Runtime.InteropServices.JavaScript.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Runtime.InteropServices.JavaScript.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Runtime.InteropServices.RuntimeInformation.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Runtime.InteropServices.RuntimeInformation.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Runtime.Intrinsics.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Runtime.Intrinsics.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Runtime.Loader.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Runtime.Loader.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Runtime.Numerics.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Runtime.Numerics.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Runtime.Serialization.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Runtime.Serialization.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Runtime.Serialization.Formatters.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Runtime.Serialization.Formatters.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Runtime.Serialization.Json.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Runtime.Serialization.Json.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Runtime.Serialization.Primitives.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Runtime.Serialization.Primitives.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Runtime.Serialization.Xml.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Runtime.Serialization.Xml.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Security.AccessControl.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Security.AccessControl.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Security.Claims.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Security.Claims.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Security.Cryptography.Algorithms.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Security.Cryptography.Algorithms.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Security.Cryptography.Cng.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Security.Cryptography.Cng.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Security.Cryptography.Csp.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Security.Cryptography.Csp.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Security.Cryptography.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Security.Cryptography.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Security.Cryptography.Encoding.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Security.Cryptography.Encoding.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Security.Cryptography.OpenSsl.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Security.Cryptography.OpenSsl.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.pkcs\\8.0.1\\lib\\net8.0\\System.Security.Cryptography.Pkcs.dll", "ItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.pkcs\\8.0.1\\lib\\net8.0\\System.Security.Cryptography.Pkcs.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Security.Cryptography.Primitives.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Security.Cryptography.Primitives.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Security.Cryptography.X509Certificates.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Security.Cryptography.X509Certificates.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.xml\\8.0.2\\lib\\net8.0\\System.Security.Cryptography.Xml.dll", "ItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\system.security.cryptography.xml\\8.0.2\\lib\\net8.0\\System.Security.Cryptography.Xml.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Security.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Security.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Security.Principal.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Security.Principal.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Security.Principal.Windows.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Security.Principal.Windows.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Security.SecureString.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Security.SecureString.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.ServiceModel.Web.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.ServiceModel.Web.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.ServiceProcess.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.ServiceProcess.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Users\\<USER>\\.nuget\\packages\\system.speech\\8.0.0-rc.2.23479.6\\lib\\net8.0\\System.Speech.dll", "ItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\system.speech\\8.0.0-rc.2.23479.6\\lib\\net8.0\\System.Speech.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Text.Encoding.CodePages.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Text.Encoding.CodePages.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Text.Encoding.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Text.Encoding.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Text.Encoding.Extensions.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Text.Encoding.Extensions.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Text.Encodings.Web.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Text.Encodings.Web.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Text.Json.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Text.Json.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Text.RegularExpressions.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Text.RegularExpressions.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Threading.Channels.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Threading.Channels.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Threading.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Threading.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Threading.Overlapped.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Threading.Overlapped.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Threading.Tasks.Dataflow.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Threading.Tasks.Dataflow.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Threading.Tasks.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Threading.Tasks.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Threading.Tasks.Extensions.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Threading.Tasks.Extensions.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Threading.Tasks.Parallel.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Threading.Tasks.Parallel.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Threading.Thread.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Threading.Thread.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Threading.ThreadPool.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Threading.ThreadPool.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Threading.Timer.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Threading.Timer.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Transactions.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Transactions.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Transactions.Local.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Transactions.Local.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.ValueTuple.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.ValueTuple.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Web.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Web.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Web.HttpUtility.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Web.HttpUtility.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Windows.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Windows.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Xml.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Xml.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Xml.Linq.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Xml.Linq.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Xml.ReaderWriter.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Xml.ReaderWriter.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Xml.Serialization.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Xml.Serialization.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Xml.XDocument.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Xml.XDocument.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Xml.XmlDocument.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Xml.XmlDocument.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Xml.XmlSerializer.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Xml.XmlSerializer.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Xml.XPath.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Xml.XPath.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Xml.XPath.XDocument.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\System.Xml.XPath.XDocument.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\WindowsBase.dll", "ItemSpec": "C:\\Program Files\\dotnet\\packs\\Microsoft.NETCore.App.Ref\\8.0.18\\ref\\net8.0\\WindowsBase.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}, {"DependentUpon": "", "FullPath": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.windows.sdk.net.ref\\10.0.19041.57\\lib\\net8.0\\WinRT.Runtime.dll", "ItemSpec": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.windows.sdk.net.ref\\10.0.19041.57\\lib\\net8.0\\WinRT.Runtime.dll", "IsSystemReference": false, "IsNuGetReference": true, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}], "TargetPlatformMinVersion": "10.0.17763.0", "ReferenceAssemblyPaths": [], "BuildConfiguration": null, "ForceSharedStateShutdown": false, "DisableXbfGeneration": false, "DisableXbfLineInfo": false, "EnableXBindDiagnostics": false, "ClIncludeFiles": null, "CIncludeDirectories": null, "XamlApplications": [{"DependentUpon": "", "FullPath": "c:\\Users\\<USER>\\Desktop\\CakeBistro\\CakeBistro\\Platforms\\Windows\\App.xaml", "ItemSpec": "Platforms\\Windows\\App.xaml", "IsSystemReference": false, "IsNuGetReference": false, "IsStaticLibraryReference": false, "MSBuild_Link": "", "MSBuild_TargetPath": "", "MSBuild_XamlResourceMapName": "", "MSBuild_XamlComponentResourceLocation": ""}], "XamlPages": null, "LocalAssembly": null, "SdkXamlPages": null, "ProjectName": "CakeBistro", "IsPass1": true, "RootNamespace": "CakeBistro", "OutputType": "WinExe", "PriIndexName": null, "CodeGenerationControlFlags": null, "FeatureControlFlags": "EnableXBindDiagnostics;EnableDefaultValidationContextGeneration;EnableWin32Codegen;UsingCSWinRT", "XAMLFingerprint": true, "UseVCMetaManaged": true, "FingerprintIgnorePaths": null, "VCInstallDir": null, "VCInstallPath32": null, "VCInstallPath64": null, "WindowsSdkPath": null, "CompileMode": "DesignTimeBuild", "SavedStateFile": "obj\\Debug\\net8.0-windows10.0.19041.0\\win10-x64\\\\XamlSaveStateFile.xml", "RootsLog": null, "SuppressWarnings": null, "GenXbfPath": "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.windowsappsdk\\1.5.240802000\\buildTransitive\\..\\tools\\net6.0\\..\\", "PrecompiledHeaderFile": null, "XamlResourceMapName": null, "XamlComponentResourceLocation": null, "XamlPlatform": null, "TargetFileName": null, "IgnoreSpecifiedTargetPlatformMinVersion": false}