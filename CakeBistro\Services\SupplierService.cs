// SupplierService.cs
using Microsoft.EntityFrameworkCore;
using CakeBistro.Core.Models;
using CakeBistro.Core.Interfaces;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace CakeBistro.Services
{
    public class SupplierService : ISupplierService
    {
        private readonly CakeBistroContext _context;

        public SupplierService(CakeBistroContext context)
        {
            _context = context;
        }

        // PRD 4.1: Manage Suppliers: Register your suppliers, allocate unique account codes, and track their invoices
        public async Task<CakeBistro.Core.Models.Supplier> CreateSupplierAsync(CakeBistro.Core.Models.Supplier CakeBistro.Core.Models.Supplier)
        {
            _context.Suppliers.Add(CakeBistro.Core.Models.Supplier);
            await _context.SaveChangesAsync();
            return CakeBistro.Core.Models.Supplier;
        }

        // PRD 4.1: Manage Suppliers: Update CakeBistro.Core.Models.Supplier information
        public async Task<CakeBistro.Core.Models.Supplier> UpdateSupplierAsync(CakeBistro.Core.Models.Supplier CakeBistro.Core.Models.Supplier)
        {
            var existingSupplier = await _context.Suppliers
                .FirstOrDefaultAsync(s => s.Id == CakeBistro.Core.Models.Supplier.Id);

            if (existingSupplier != null)
            {
                // Update only non-relationship properties
                _context.Entry(existingSupplier).CurrentValues.SetValues(CakeBistro.Core.Models.Supplier);
                
                await _context.SaveChangesAsync();
                return existingSupplier;
            }
            
            return null;
        }

        // PRD 4.1: Manage Suppliers: Track CakeBistro.Core.Models.Supplier invoices
        public async Task<CakeBistro.Core.Models.PurchaseOrder> CreatePurchaseOrderAsync(CakeBistro.Core.Models.PurchaseOrder order)
        {
            _context.PurchaseOrders.Add(order);
            
            // Update raw material stock when creating purchase order
            foreach (var item in order.Items)
            {
                var material = await _context.RawMaterials
                    .FirstOrDefaultAsync(m => m.Id == item.RawMaterialId);

                if (material != null)
                {
                    material.CurrentStock += item.Quantity;
                }
            }
            
            await _context.SaveChangesAsync();
            return order;
        }

        // PRD 4.1: Manage Suppliers: Update purchase order information
        public async Task<CakeBistro.Core.Models.PurchaseOrder> UpdatePurchaseOrderAsync(CakeBistro.Core.Models.PurchaseOrder order)
        {
            var existingOrder = await _context.PurchaseOrders
                .Include(o => o.Items)
                .FirstOrDefaultAsync(o => o.Id == order.Id);

            if (existingOrder != null)
            {
                // Handle removed items
                var itemsToRemove = existingOrder.Items
                    .Where(existingItem => !order.Items.Any(newItem => newItem.Id == existingItem.Id))
                    .ToList();

                foreach (var item in itemsToRemove)
                {
                    var material = await _context.RawMaterials
                        .FirstOrDefaultAsync(m => m.Id == item.RawMaterialId);

                    if (material != null)
                    {
                        material.CurrentStock -= item.Quantity;
                    }
                    
                    _context.PurchaseOrderItems.Remove(item);
                }

                // Handle added/updated items and update total amount
                decimal totalAmount = 0;
                foreach (var newItem in order.Items)
                {
                    var existingItem = existingOrder.Items
                        .FirstOrDefault(i => i.Id == newItem.Id);

                    if (existingItem == null)
                    {   // New item
                        var material = await _context.RawMaterials
                            .FirstOrDefaultAsync(m => m.Id == newItem.RawMaterialId);

                        if (material != null)
                        {
                            material.CurrentStock += newItem.Quantity;
                        }
                        
                        _context.PurchaseOrderItems.Add(newItem);
                    }
                    else
                    {   // Existing item
                        var material = await _context.RawMaterials
                            .FirstOrDefaultAsync(m => m.Id == newItem.RawMaterialId);

                        if (material != null)
                        {
                            material.CurrentStock += (newItem.Quantity - existingItem.Quantity);
                        }
                        
                        _context.Entry(existingItem).CurrentValues.SetValues(newItem);
                    }
                    
                    totalAmount += newItem.UnitPrice * newItem.Quantity;
                }
                
                // Update order details
                existingOrder.SupplierId = order.SupplierId;
                existingOrder.OrderDate = order.OrderDate;
                existingOrder.TotalAmount = totalAmount;
                existingOrder.Notes = order.Notes;
                
                await _context.SaveChangesAsync();
                return existingOrder;
            }
            
            return null;
        }

        // PRD 4.1: Generate detailed stock movement reports and raw material stock statements
        public async Task<List<CakeBistro.Core.Models.Supplier>> GetAllSuppliersAsync()
        {
            return await _context.Suppliers.ToListAsync();
        }

        public async Task<List<CakeBistro.Core.Models.PurchaseOrder>> GetPurchaseOrdersAsync()
        {
            return await _context.PurchaseOrders.ToListAsync();
        }

        public async Task<List<CakeBistro.Core.Models.PurchaseOrder>> GetSupplierPurchaseOrdersAsync(int supplierId)
        {
            return await _context.PurchaseOrders
                .Where(o => o.SupplierId == supplierId)
                .ToListAsync();
        }

        // Update AddSupplierAsync method in SupplierService.cs
        public async Task<CakeBistro.Core.Models.Supplier> AddSupplierAsync(CakeBistro.Core.Models.Supplier CakeBistro.Core.Models.Supplier)
        {
            // Validate input
            if (CakeBistro.Core.Models.Supplier == null)
                throw new ArgumentNullException(nameof(CakeBistro.Core.Models.Supplier), "CakeBistro.Core.Models.Supplier cannot be null");

            if (string.IsNullOrWhiteSpace(CakeBistro.Core.Models.Supplier.Name))
                throw new ArgumentException("CakeBistro.Core.Models.Supplier name is required", nameof(CakeBistro.Core.Models.Supplier.Name));

            if (string.IsNullOrWhiteSpace(CakeBistro.Core.Models.Supplier.ContactPerson))
                throw new ArgumentException("Contact person is required", nameof(CakeBistro.Core.Models.Supplier.ContactPerson));

            if (string.IsNullOrWhiteSpace(CakeBistro.Core.Models.Supplier.Email))
                throw new ArgumentException("Email address is required", nameof(CakeBistro.Core.Models.Supplier.Email));

            // Simple email format validation
            if (!IsValidEmail(CakeBistro.Core.Models.Supplier.Email))
                throw new ArgumentException("Invalid email address format", nameof(CakeBistro.Core.Models.Supplier.Email));

            if (string.IsNullOrWhiteSpace(CakeBistro.Core.Models.Supplier.Phone))
                throw new ArgumentException("Phone number is required", nameof(CakeBistro.Core.Models.Supplier.Phone));

            if (string.IsNullOrWhiteSpace(CakeBistro.Core.Models.Supplier.Address))
                throw new ArgumentException("Address is required", nameof(CakeBistro.Core.Models.Supplier.Address));

            try
            {
                // Set default values if not provided
                CakeBistro.Core.Models.Supplier.CreatedDate ??= DateTime.Now;
                CakeBistro.Core.Models.Supplier.Status ??= "Active";
        
                // Validate that this CakeBistro.Core.Models.Supplier doesn't already exist
                var existingSuppliers = await _supplierRepository.GetAllAsync();
                if (existingSuppliers.Any(s => s.Name.Equals(CakeBistro.Core.Models.Supplier.Name, StringComparison.OrdinalIgnoreCase)))
                {
                    throw new ArgumentException($"A CakeBistro.Core.Models.Supplier with the name '{CakeBistro.Core.Models.Supplier.Name}' already exists", nameof(CakeBistro.Core.Models.Supplier.Name));
                }
        
                // Save the CakeBistro.Core.Models.Supplier
                return await _supplierRepository.AddAsync(CakeBistro.Core.Models.Supplier);
            }
            catch (Exception ex)
            {
                // Log the error (in a real application)
                // For now, just rethrow with a more descriptive message
                throw new ApplicationException($"Error adding CakeBistro.Core.Models.Supplier: {ex.Message}", ex);
            }
        }

        // Supplier Payments
        public async Task<SupplierPayment> RecordSupplierPaymentAsync(SupplierPayment payment)
        {
            _context.Set<SupplierPayment>().Add(payment);
            await _context.SaveChangesAsync();
            return payment;
        }

        public async Task<List<SupplierPayment>> GetSupplierPaymentsAsync(int supplierId)
        {
            return await _context.Set<SupplierPayment>()
                .Where(p => p.SupplierId == supplierId)
                .OrderByDescending(p => p.PaymentDate)
                .ToListAsync();
        }

        public async Task<List<SupplierPayment>> GetAllSupplierPaymentsAsync()
        {
            return await _context.Set<SupplierPayment>()
                .OrderByDescending(p => p.PaymentDate)
                .ToListAsync();
        }

        /// <summary>
        /// Validates basic email format
        /// </summary>
        private bool IsValidEmail(string email)
        {
            try
            {
                var addr = new System.Net.Mail.MailAddress(email);
                return addr.Address == email;
            }
            catch
            {
                return false;
            }
        }
    }
}
