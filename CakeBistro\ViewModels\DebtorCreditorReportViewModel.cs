using CakeBistro.Models;
using CakeBistro.Services;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.Collections.ObjectModel;
using System.Threading.Tasks;
using System;
using Microsoft.UI.Xaml.Media;

namespace CakeBistro.ViewModels
{
    public partial class DebtorCreditorReportViewModel : ObservableObject
    {
        private readonly IFinanceService _financeService;

        [ObservableProperty] private ObservableCollection<Debtor> debtors = new();
        [ObservableProperty] private ObservableCollection<Creditor> creditors = new();
        [ObservableProperty] private DateTime reportDate = DateTime.Today;
        [ObservableProperty] private string? exportResultMessage;
        private Color _statusColor = Colors.Transparent;

        public Color StatusColor
        {
            get => _statusColor;
            set => SetProperty(ref _statusColor, value);
        }

        public DebtorCreditorReportViewModel(IFinanceService financeService)
        {
            _financeService = financeService;
        }

        [RelayCommand]
        public async Task LoadDebtorsAsync()
        {
            Debtors = new ObservableCollection<Debtor>(await _financeService.GetDebtorsListAsync());
        }

        [RelayCommand]
        public async Task LoadCreditorsAsync()
        {
            Creditors = new ObservableCollection<Creditor>(await _financeService.GetCreditorsListAsync());
        }

        [RelayCommand]
        public async Task ExportDebtorCreditorReportAsync(string format)
        {
            try
            {
                var filePath = await _financeService.ExportDebtorCreditorReportAsync(ReportDate, format);
                ExportResultMessage = $"Report exported: {filePath}";
                StatusColor = Color.FromArgb("#388E3C"); // Success color
            }
            catch (Exception ex)
            {
                ExportResultMessage = $"Error: {ex.Message}";
                StatusColor = Color.FromArgb("#D32F2F"); // Error color
            }
        }
    }
}
