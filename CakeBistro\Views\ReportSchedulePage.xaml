<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:viewModels="clr-namespace:CakeBistro.ViewModels"
             x:Class="CakeBistro.Views.ReportSchedulePage"
             Title="Report Scheduling">
    <ContentPage.BindingContext>
        <viewModels:ReportScheduleViewModel />
    </ContentPage.BindingContext>
    <ScrollView>
        <VerticalStackLayout Spacing="16" Padding="16">
            <Label Text="Schedule a Report" FontSize="24" FontAttributes="Bold" />
            <Picker Title="Report Type" ItemsSource="{Binding ReportTypes}" SelectedItem="{Binding SelectedReportType}" />
            <DatePicker Date="{Binding ScheduleDate, Mode=TwoWay}" />
            <Picker Title="Frequency" ItemsSource="{Binding Frequencies}" SelectedItem="{Binding SelectedFrequency}" />
            <Button Text="Schedule Report" Command="{Binding ScheduleReportCommand}" />
            <Label Text="{Binding StatusMessage}" TextColor="{Binding StatusColor}" />
            <Label Text="Scheduled Reports" FontAttributes="Bold" />
            <CollectionView ItemsSource="{Binding ScheduledReports}">
                <CollectionView.ItemTemplate>
                    <DataTemplate>
                        <Frame>
                            <VerticalStackLayout>
                                <Label Text="{Binding ReportType}" />
                                <Label Text="{Binding ScheduleDate, StringFormat='Date: {0:yyyy-MM-dd}'}" />
                                <Label Text="{Binding Frequency}" />
                            </VerticalStackLayout>
                        </Frame>
                    </DataTemplate>
                </CollectionView.ItemTemplate>
            </CollectionView>
        </VerticalStackLayout>
    </ScrollView>
</ContentPage>
