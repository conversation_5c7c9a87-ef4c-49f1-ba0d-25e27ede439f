using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using CakeBistro.Core.Models;

namespace CakeBistro.Services
{
    public class PackingService
    {
        // Simulated in-memory storage (replace with EF Core/DB in production)
        private readonly List<PackingSection> _sections = new();

        public Task<PackingSection> CreatePackingSectionAsync(PackingSection section)
        {
            section.Id = _sections.Count > 0 ? _sections.Max(s => s.Id) + 1 : 1;
            _sections.Add(section);
            return Task.FromResult(section);
        }

        public Task<List<PackingSection>> GetAllPackingSectionsAsync()
        {
            return Task.FromResult(_sections.ToList());
        }

        public Task<bool> AddItemToPackingSectionAsync(int sectionId, PackingItem item)
        {
            var section = _sections.FirstOrDefault(s => s.Id == sectionId);
            if (section == null) return Task.FromResult(false);
            if (section.Items == null) section.Items = new List<PackingItem>();
            item.Id = section.Items.Count > 0 ? section.Items.Max(i => i.Id) + 1 : 1;
            section.Items.Add(item);
            return Task.FromResult(true);
        }

        public Task<bool> RemoveItemFromPackingSectionAsync(int sectionId, int itemId)
        {
            var section = _sections.FirstOrDefault(s => s.Id == sectionId);
            if (section == null || section.Items == null) return Task.FromResult(false);
            var item = section.Items.FirstOrDefault(i => i.Id == itemId);
            if (item == null) return Task.FromResult(false);
            section.Items.Remove(item);
            return Task.FromResult(true);
        }

        public Task<bool> AdjustPackingItemQuantityAsync(int sectionId, int itemId, int newQuantity)
        {
            var section = _sections.FirstOrDefault(s => s.Id == sectionId);
            if (section == null || section.Items == null) return Task.FromResult(false);
            var item = section.Items.FirstOrDefault(i => i.Id == itemId);
            if (item == null) return Task.FromResult(false);
            item.Quantity = newQuantity;
            return Task.FromResult(true);
        }
    }
}
