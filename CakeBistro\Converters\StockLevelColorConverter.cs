

// Create StockLevelColorConverter.cs
using System;
using System.Globalization;
using Microsoft.Maui.Controls;

namespace CakeBistro.Converters;

public class StockLevelColorConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is not CakeBistro.Core.Models.RawMaterial material)
            return Color.Default;
        
        if (material.CurrentStock < material.MinimumStock)
            return Colors.Red; // Below minimum stock
        
        if (material.CurrentStock < material.MinimumStock * 1.1m)
            return Colors.Orange; // Within 10% of minimum
        
        return Colors.Green; // Adequate stock
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        // Since this converter is likely used for one-way binding,
        // we can safely return an empty string as a default value.
        return "";
    }