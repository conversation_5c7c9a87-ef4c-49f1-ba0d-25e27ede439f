// Create ReportExportService.cs
using CakeBistro.Core.Models;
using iText.Kernel.Pdf;
using iText.Layout;
using iText.Layout.Element;
using iText.Kernel.Colors;
using System;
using System.IO;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using iText.Layout.Properties;
using System.Collections.Generic;
using Microsoft.Extensions.DependencyInjection;
using System.Text;
using OfficeOpenXml;
using iTextSharp.text;
using iTextSharp.text.pdf;

namespace CakeBistro.Services;

/// <summary>
/// Service for exporting reports to various formats
/// </summary>
public class ReportExportService : IReportExportService
{
    private readonly ILogger<ReportExportService> _logger;
    
    public ReportExportService(ILogger<ReportExportService> logger)
    {
        _logger = logger;
    }
    
    /// <summary>
    /// Exports a report to the specified format
    /// </summary>
    public async Task<byte[]> ExportAsync<T>(T report, string format) where T : class
    {
        if (report == null) throw new ArgumentNullException(nameof(report));
        
        switch (format.ToLower())
        {
            case "pdf":
                return await ExportToPdfAsync(report);
            case "csv":
                return await ExportToCsvAsync(report);
            case "excel":
                return await ExportToExcelAsync(report);
            default:
                throw new ArgumentException($"Unsupported format: {format}");
        }
    }

    /// <summary>
    /// Exports a report to PDF format
    /// </summary>
    private async Task<byte[]> ExportToPdfAsync<T>(T report) where T : class
    {
        // In a real implementation, this would use a PDF generation library
        // This is a placeholder implementation that just serializes the object
        var pdfBytes = await SerializeObjectAsync(report);
        return pdfBytes;
    }

    /// <summary>
    /// Exports a report to CSV format
    /// </summary>
    private async Task<byte[]> ExportToCsvAsync<T>(T report) where T : class
    {
        // In a real implementation, this would use a CSV generation library
        // This is a placeholder implementation that just serializes the object
        var csvBytes = await SerializeObjectAsync(report);
        return csvBytes;
    }

    /// <summary>
    /// Exports a report to Excel format
    /// </summary>
    private async Task<byte[]> ExportToExcelAsync<T>(T report) where T : class
    {
        // In a real implementation, this would use an Excel generation library
        // This is a placeholder implementation that just serializes the object
        var excelBytes = await SerializeObjectAsync(report);
        return excelBytes;
    }

    /// <summary>
    /// Serializes an object to bytes (for placeholder implementations)
    /// </summary>
    private async Task<byte[]> SerializeObjectAsync<T>(T report) where T : class
    {
        var json = JsonSerializer.Serialize(report);
        return Encoding.UTF8.GetBytes(json);
    }
    
    public async Task<string> ExportInventoryValuationReportToPdfAsync(InventoryValuationReport report, string filePath)
    {
        try
        {
            // Ensure the directory exists
            var directory = Path.GetDirectoryName(filePath);
            if (!Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }
            
            using (var writer = new PdfWriter(filePath))
            using (var pdf = new PdfDocument(writer))
            using (var document = new Document(pdf))
            {
                // Add title
                document.Add(new Paragraph("Inventory Valuation Report")
                    .SetTextAlignment(TextAlignment.CENTER)
                    .SetFontSize(20));
                
                // Add report date
                document.Add(new Paragraph($"Report Date: {report.ReportDate:g}")
                    .SetTextAlignment(TextAlignment.RIGHT)
                    .SetMarginBottom(20));
                
                // Summary section
                var summaryTable = new Table(UnitValue.CreatePercentArray(2)).UseAllAvailableWidth();
                summaryTable.AddCell(CreateReportSummaryCell("Total Inventory Value", $"{report.TotalInventoryValue:C}", ColorConstants.LIGHT_GRAY));
                summaryTable.AddCell(CreateReportSummaryCell("Total Items", $"{report.TotalItemCount} units", ColorConstants.LIGHT_GRAY));
                summaryTable.AddCell(CreateReportSummaryCell("Low Stock Items", $"{report.LowStockItemCount} items", ColorConstants.ORANGE));
                summaryTable.AddCell(CreateReportSummaryCell("Out of Stock Items", $"{report.OutOfStockItemCount} items", ColorConstants.RED));
                
                document.Add(summaryTable);
                
                // Detailed valuation section
                document.Add(new Paragraph("\nItemized Valuation")
                    .SetTextAlignment(TextAlignment.LEFT)
                    .SetFontSize(16)
                    .SetMarginTop(25));
                
                var valuationTable = new Table(UnitValue.CreatePercentArray(5)).UseAllAvailableWidth();
                valuationTable.SetMarginTop(10);
                
                // Header row
                valuationTable.AddHeaderCell(CreateReportHeaderCell("Material Name"));
                valuationTable.AddHeaderCell(CreateReportHeaderCell("Quantity"));
                valuationTable.AddHeaderCell(CreateReportHeaderCell("Unit Price"));
                valuationTable.AddHeaderCell(CreateReportHeaderCell("Total Value"));
                valuationTable.AddHeaderCell(CreateReportHeaderCell("Stock Status"));
                
                // Data rows
                foreach (var item in report.ItemValuations)
                {
                    valuationTable.AddCell(CreateReportDetailCell(item.RawMaterialName));
                    valuationTable.AddCell(CreateReportDetailCell(item.Quantity.ToString("F0")));
                    valuationTable.AddCell(CreateReportDetailCell(item.UnitPrice.ToString("C")));
                    valuationTable.AddCell(CreateReportDetailCell(item.TotalValue.ToString("C")));
                    
                    var stockStatusCell = CreateReportDetailCell(item.IsLowStock ? "Low Stock" : "OK");
                    if (item.IsLowStock)
                    {
                        stockStatusCell.SetBackgroundColor(ColorConstants.ORANGE);
                    }
                    else if (item.IsOutOfStock)
                    {
                        stockStatusCell.SetBackgroundColor(ColorConstants.RED);
                        stockStatusCell.Add(new Text(" (Out of Stock)").SetFontColor(ColorConstants.WHITE));
                    }
                    valuationTable.AddCell(stockStatusCell);
                }
                
                document.Add(valuationTable);
                
                // Recommendations section
                if (report.Recommendations != null && report.Recommendations.Count > 0)
                {
                    document.Add(new Paragraph("\nRecommendations")
                        .SetTextAlignment(TextAlignment.LEFT)
                        .SetFontSize(16)
                        .SetMarginTop(25));
                    
                    foreach (var recommendation in report.Recommendations)
                    {
                        document.Add(new Paragraph($"• {recommendation}")
                            .SetMarginLeft(15));
                    }
                }
                
                // Notes section
                if (!string.IsNullOrWhiteSpace(report.Notes))
                {
                    document.Add(new Paragraph("\nNotes:")
                        .SetFontSize(14)
                        .SetBold());
                    document.Add(new Paragraph(report.Notes)
                        .SetMarginLeft(15)
                        .SetItalic());
                }
            }
            
            return filePath;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error exporting inventory valuation report to PDF");
            throw new ApplicationException($"Error exporting report: {ex.Message}", ex);
        }
    }
    
    public async Task<string> ExportExpiringBatchReportToPdfAsync(ExpiringBatchReport report, string filePath)
    {
        try
        {
            // Ensure the directory exists
            var directory = Path.GetDirectoryName(filePath);
            if (!Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }
            
            using (var writer = new PdfWriter(filePath))
            using (var pdf = new PdfDocument(writer))
            using (var document = new Document(pdf))
            {
                // Add title
                document.Add(new Paragraph("Batch Expiration Report")
                    .SetTextAlignment(TextAlignment.CENTER)
                    .SetFontSize(20));
                
                // Add report date
                document.Add(new Paragraph($"Report Date: {report.ReportDate:g}")
                    .SetTextAlignment(TextAlignment.RIGHT)
                    .SetMarginBottom(20));
                
                // Summary section
                var summaryTable = new Table(UnitValue.CreatePercentArray(2)).UseAllAvailableWidth();
                summaryTable.AddCell(CreateReportSummaryCell("Total Expiring Batches", $"{report.TotalExpiringBatches} batches", ColorConstants.LIGHT_GRAY));
                summaryTable.AddCell(CreateReportSummaryCell("Within 30 Days", $"{report.BatchesExpiringWithin30Days} batches", ColorConstants.LIGHT_GRAY));
                summaryTable.AddCell(CreateReportSummaryCell("Urgent (7 Days)", $"{report.BatchesExpiringWithin7Days} batches", ColorConstants.ORANGE));
                summaryTable.AddCell(CreateReportSummaryCell("Expired Batches", $"{report.ExpiredBatches} batches", ColorConstants.RED));
                
                document.Add(summaryTable);
                
                // Detailed expiration section
                document.Add(new Paragraph("\nExpiring Batch Details")
                    .SetTextAlignment(TextAlignment.LEFT)
                    .SetFontSize(16)
                    .SetMarginTop(25));
                
                var expirationTable = new Table(UnitValue.CreatePercentArray(5)).UseAllAvailableWidth();
                expirationTable.SetMarginTop(10);
                
                // Header row
                expirationTable.AddHeaderCell(CreateReportHeaderCell("Material Name"));
                expirationTable.AddHeaderCell(CreateReportHeaderCell("Batch Number"));
                expirationTable.AddHeaderCell(CreateReportHeaderCell("Expiry Date"));
                expirationTable.AddHeaderCell(CreateReportHeaderCell("Quantity"));
                expirationTable.AddHeaderCell(CreateReportHeaderCell("Storage Location"));
                
                // Data rows
                foreach (var detail in report.ExpiringBatchDetails)
                {
                    expirationTable.AddCell(CreateReportDetailCell(detail.RawMaterialName));
                    expirationTable.AddCell(CreateReportDetailCell(detail.BatchNumber));
                    expirationTable.AddCell(CreateReportDetailCell(detail.ExpiryDate.ToString("d")));
                    expirationTable.AddCell(CreateReportDetailCell(detail.Quantity.ToString("F0")));
                    expirationTable.AddCell(CreateReportDetailCell(detail.StorageLocation));
                }
                
                document.Add(expirationTable);
                
                // Recommendations section
                if (report.Recommendations != null && report.Recommendations.Count > 0)
                {
                    document.Add(new Paragraph("\nExpiration Recommendations")
                        .SetTextAlignment(TextAlignment.LEFT)
                        .SetFontSize(16)
                        .SetMarginTop(25));
                    
                    foreach (var recommendation in report.Recommendations)
                    {
                        document.Add(new Paragraph($"• {recommendation}")
                            .SetMarginLeft(15)
                            .SetBold());
                    }
                }
                
                // Notes section
                if (!string.IsNullOrWhiteSpace(report.Notes))
                {
                    document.Add(new Paragraph("\nNotes:")
                        .SetFontSize(14)
                        .SetBold());
                    document.Add(new Paragraph(report.Notes)
                        .SetMarginLeft(15)
                        .SetItalic());
                }
            }
            
            return filePath;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error exporting expiring batch report to PDF");
            throw new ApplicationException($"Error exporting report: {ex.Message}", ex);
        }
    }
    
    public async Task<string> ExportDeliveryManifestsToCsvAsync(IEnumerable<DeliveryManifest> manifests, string filePath)
    {
        var csv = new StringBuilder();
        csv.AppendLine("DeliveryDate,Vehicle,Driver,Status,Notes,TotalDistance,EstimatedDuration");
        foreach (var m in manifests)
        {
            csv.AppendLine($"{m.DeliveryDate:yyyy-MM-dd},{m.Vehicle?.RegistrationNumber},{m.Driver?.FirstName} {m.Driver?.LastName},{m.Status},{m.Notes},{m.TotalDistance},{m.EstimatedDuration}");
        }
        await File.WriteAllTextAsync(filePath, csv.ToString());
        return filePath;
    }

    public async Task<string> ExportDeliveryManifestsToPdfAsync(IEnumerable<DeliveryManifest> manifests, string filePath)
    {
        using (var fs = new FileStream(filePath, FileMode.Create, FileAccess.Write, FileShare.None))
        {
            var doc = new Document();
            var writer = PdfWriter.GetInstance(doc, fs);
            doc.Open();
            doc.Add(new Paragraph("Delivery Manifest Export"));
            var table = new PdfPTable(7);
            table.AddCell("DeliveryDate");
            table.AddCell("Vehicle");
            table.AddCell("Driver");
            table.AddCell("Status");
            table.AddCell("Notes");
            table.AddCell("TotalDistance");
            table.AddCell("EstimatedDuration");
            foreach (var m in manifests)
            {
                table.AddCell(m.DeliveryDate.ToString("yyyy-MM-dd"));
                table.AddCell(m.Vehicle?.RegistrationNumber ?? "");
                table.AddCell($"{m.Driver?.FirstName} {m.Driver?.LastName}");
                table.AddCell(m.Status.ToString());
                table.AddCell(m.Notes ?? "");
                table.AddCell(m.TotalDistance.ToString());
                table.AddCell(m.EstimatedDuration.ToString());
            }
            doc.Add(table);
            doc.Close();
        }
        return filePath;
    }

    public async Task<string> ExportDeliveryManifestsToExcelAsync(IEnumerable<DeliveryManifest> manifests, string filePath)
    {
        using (var package = new ExcelPackage())
        {
            var ws = package.Workbook.Worksheets.Add("Manifests");
            ws.Cells[1, 1].Value = "DeliveryDate";
            ws.Cells[1, 2].Value = "Vehicle";
            ws.Cells[1, 3].Value = "Driver";
            ws.Cells[1, 4].Value = "Status";
            ws.Cells[1, 5].Value = "Notes";
            ws.Cells[1, 6].Value = "TotalDistance";
            ws.Cells[1, 7].Value = "EstimatedDuration";
            int i = 0;
            foreach (var m in manifests)
            {
                ws.Cells[i + 2, 1].Value = m.DeliveryDate.ToString("yyyy-MM-dd");
                ws.Cells[i + 2, 2].Value = m.Vehicle?.RegistrationNumber;
                ws.Cells[i + 2, 3].Value = $"{m.Driver?.FirstName} {m.Driver?.LastName}";
                ws.Cells[i + 2, 4].Value = m.Status.ToString();
                ws.Cells[i + 2, 5].Value = m.Notes;
                ws.Cells[i + 2, 6].Value = m.TotalDistance;
                ws.Cells[i + 2, 7].Value = m.EstimatedDuration.ToString();
                i++;
            }
            package.SaveAs(new FileInfo(filePath));
        }
        return filePath;
    }

    public async Task<string> ExportVehicleListToCsvAsync(IEnumerable<Vehicle> vehicles, string filePath)
    {
        var csv = new System.Text.StringBuilder();
        csv.AppendLine("RegistrationNumber,Make,Model,Year,Capacity,FuelType,Status");
        foreach (var v in vehicles)
        {
            csv.AppendLine($"{v.RegistrationNumber},{v.Make},{v.Model},{v.Year},{v.Capacity},{v.FuelType},{v.Status}");
        }
        await File.WriteAllTextAsync(filePath, csv.ToString());
        return filePath;
    }

    public async Task<string> ExportVehicleListToPdfAsync(IEnumerable<Vehicle> vehicles, string filePath)
    {
        using (var fs = new FileStream(filePath, FileMode.Create, FileAccess.Write, FileShare.None))
        {
            var doc = new iTextSharp.text.Document();
            var writer = iTextSharp.text.pdf.PdfWriter.GetInstance(doc, fs);
            doc.Open();
            doc.Add(new iTextSharp.text.Paragraph("Vehicle List Export"));
            var table = new iTextSharp.text.pdf.PdfPTable(6);
            table.AddCell("RegistrationNumber");
            table.AddCell("Make");
            table.AddCell("Model");
            table.AddCell("Year");
            table.AddCell("Capacity");
            table.AddCell("Status");
            foreach (var v in vehicles)
            {
                table.AddCell(v.RegistrationNumber);
                table.AddCell(v.Make);
                table.AddCell(v.Model);
                table.AddCell(v.Year.ToString());
                table.AddCell(v.Capacity.ToString());
                table.AddCell(v.Status.ToString());
            }
            doc.Add(table);
            doc.Close();
        }
        return filePath;
    }

    public async Task<string> ExportVehicleListToExcelAsync(IEnumerable<Vehicle> vehicles, string filePath)
    {
        using (var package = new OfficeOpenXml.ExcelPackage())
        {
            var ws = package.Workbook.Worksheets.Add("Vehicles");
            ws.Cells[1, 1].Value = "RegistrationNumber";
            ws.Cells[1, 2].Value = "Make";
            ws.Cells[1, 3].Value = "Model";
            ws.Cells[1, 4].Value = "Year";
            ws.Cells[1, 5].Value = "Capacity";
            ws.Cells[1, 6].Value = "Status";
            int i = 0;
            foreach (var v in vehicles)
            {
                ws.Cells[i + 2, 1].Value = v.RegistrationNumber;
                ws.Cells[i + 2, 2].Value = v.Make;
                ws.Cells[i + 2, 3].Value = v.Model;
                ws.Cells[i + 2, 4].Value = v.Year;
                ws.Cells[i + 2, 5].Value = v.Capacity;
                ws.Cells[i + 2, 6].Value = v.Status.ToString();
                i++;
            }
            package.SaveAs(new FileInfo(filePath));
        }
        return filePath;
    }

    public async Task<string> ExportDriverListToCsvAsync(IEnumerable<Driver> drivers, string filePath)
    {
        var csv = new System.Text.StringBuilder();
        csv.AppendLine("FirstName,LastName,LicenseNumber,LicenseExpiryDate,PhoneNumber,Address,Status");
        foreach (var d in drivers)
        {
            csv.AppendLine($"{d.FirstName},{d.LastName},{d.LicenseNumber},{d.LicenseExpiryDate:yyyy-MM-dd},{d.PhoneNumber},{d.Address},{d.Status}");
        }
        await File.WriteAllTextAsync(filePath, csv.ToString());
        return filePath;
    }

    public async Task<string> ExportDriverListToPdfAsync(IEnumerable<Driver> drivers, string filePath)
    {
        using (var fs = new FileStream(filePath, FileMode.Create, FileAccess.Write, FileShare.None))
        {
            var doc = new iTextSharp.text.Document();
            var writer = iTextSharp.text.pdf.PdfWriter.GetInstance(doc, fs);
            doc.Open();
            doc.Add(new iTextSharp.text.Paragraph("Driver List Export"));
            var table = new iTextSharp.text.pdf.PdfPTable(6);
            table.AddCell("FirstName");
            table.AddCell("LastName");
            table.AddCell("LicenseNumber");
            table.AddCell("LicenseExpiryDate");
            table.AddCell("PhoneNumber");
            table.AddCell("Status");
            foreach (var d in drivers)
            {
                table.AddCell(d.FirstName);
                table.AddCell(d.LastName);
                table.AddCell(d.LicenseNumber);
                table.AddCell(d.LicenseExpiryDate.ToString("yyyy-MM-dd"));
                table.AddCell(d.PhoneNumber);
                table.AddCell(d.Status.ToString());
            }
            doc.Add(table);
            doc.Close();
        }
        return filePath;
    }

    public async Task<string> ExportDriverListToExcelAsync(IEnumerable<Driver> drivers, string filePath)
    {
        using (var package = new OfficeOpenXml.ExcelPackage())
        {
            var ws = package.Workbook.Worksheets.Add("Drivers");
            ws.Cells[1, 1].Value = "FirstName";
            ws.Cells[1, 2].Value = "LastName";
            ws.Cells[1, 3].Value = "LicenseNumber";
            ws.Cells[1, 4].Value = "LicenseExpiryDate";
            ws.Cells[1, 5].Value = "PhoneNumber";
            ws.Cells[1, 6].Value = "Status";
            int i = 0;
            foreach (var d in drivers)
            {
                ws.Cells[i + 2, 1].Value = d.FirstName;
                ws.Cells[i + 2, 2].Value = d.LastName;
                ws.Cells[i + 2, 3].Value = d.LicenseNumber;
                ws.Cells[i + 2, 4].Value = d.LicenseExpiryDate.ToString("yyyy-MM-dd");
                ws.Cells[i + 2, 5].Value = d.PhoneNumber;
                ws.Cells[i + 2, 6].Value = d.Status.ToString();
                i++;
            }
            package.SaveAs(new FileInfo(filePath));
        }
        return filePath;
    }
    
    public async Task<string> ExportProductionBatchAnalyticsReportToPdfAsync(object batchAnalyticsReport, string filePath)
    {
        try
        {
            // Ensure the directory exists
            var directory = Path.GetDirectoryName(filePath);
            if (!Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            using (var writer = new PdfWriter(filePath))
            using (var pdf = new PdfDocument(writer))
            using (var document = new Document(pdf))
            {
                // Add title
                document.Add(new Paragraph("Production Batch Analytics Report")
                    .SetTextAlignment(TextAlignment.CENTER)
                    .SetFontSize(20));

                // Add report date
                document.Add(new Paragraph($"Report Date: {DateTime.Now:g}")
                    .SetTextAlignment(TextAlignment.RIGHT)
                    .SetMarginBottom(20));

                // Use dynamic for now (should be strongly typed in future)
                dynamic report = batchAnalyticsReport;

                // Summary section
                var summaryTable = new Table(UnitValue.CreatePercentArray(2)).UseAllAvailableWidth();
                summaryTable.AddCell(CreateReportSummaryCell("Batch ID", $"{report.BatchId}", ColorConstants.LIGHT_GRAY));
                summaryTable.AddCell(CreateReportSummaryCell("Recipe", $"{report.RecipeName}", ColorConstants.LIGHT_GRAY));
                summaryTable.AddCell(CreateReportSummaryCell("Planned Qty", $"{report.PlannedQuantity}", ColorConstants.LIGHT_GRAY));
                summaryTable.AddCell(CreateReportSummaryCell("Actual Qty", $"{report.ActualQuantity}", ColorConstants.LIGHT_GRAY));
                summaryTable.AddCell(CreateReportSummaryCell("Planned Cost", $"{report.PlannedCost:C}", ColorConstants.LIGHT_GRAY));
                summaryTable.AddCell(CreateReportSummaryCell("Total Cost", $"{report.TotalCost:C}", ColorConstants.LIGHT_GRAY));
                summaryTable.AddCell(CreateReportSummaryCell("Expected Revenue", $"{report.ExpectedRevenue:C}", ColorConstants.LIGHT_GRAY));
                summaryTable.AddCell(CreateReportSummaryCell("Actual Revenue", $"{report.ActualRevenue:C}", ColorConstants.LIGHT_GRAY));
                summaryTable.AddCell(CreateReportSummaryCell("Expected Profit", $"{report.ExpectedProfit:C}", ColorConstants.LIGHT_GRAY));
                summaryTable.AddCell(CreateReportSummaryCell("Actual Profit", $"{report.ActualProfit:C}", ColorConstants.LIGHT_GRAY));
                summaryTable.AddCell(CreateReportSummaryCell("Profit Margin", $"{report.ProfitMargin:F2}%", ColorConstants.LIGHT_GRAY));
                summaryTable.AddCell(CreateReportSummaryCell("Actual Profit Margin", $"{report.ActualProfitMargin:F2}%", ColorConstants.LIGHT_GRAY));
                document.Add(summaryTable);

                // Cost breakdown section
                document.Add(new Paragraph("\nCost Breakdown")
                    .SetTextAlignment(TextAlignment.LEFT)
                    .SetFontSize(16)
                    .SetMarginTop(25));

                var breakdownTable = new Table(UnitValue.CreatePercentArray(2)).UseAllAvailableWidth();
                breakdownTable.AddHeaderCell(CreateReportHeaderCell("Cost Item"));
                breakdownTable.AddHeaderCell(CreateReportHeaderCell("Cost"));
                foreach (var item in report.CostBreakdown)
                {
                    breakdownTable.AddCell(CreateReportDetailCell(item.Name));
                    breakdownTable.AddCell(CreateReportDetailCell(((decimal)item.Cost).ToString("C")));
                }
                document.Add(breakdownTable);
            }
            return filePath;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error exporting production batch analytics report to PDF");
            throw new ApplicationException($"Error exporting report: {ex.Message}", ex);
        }
    }
    
    public async Task<string> ExportProductionBatchAnalyticsReportToExcelAsync(object batchAnalyticsReport, string filePath)
    {
        try
        {
            var report = batchAnalyticsReport as dynamic;
            using (var package = new OfficeOpenXml.ExcelPackage())
            {
                var ws = package.Workbook.Worksheets.Add("Batch Analytics");
                ws.Cells[1, 1].Value = "Batch ID";
                ws.Cells[1, 2].Value = report.BatchId;
                ws.Cells[2, 1].Value = "Recipe";
                ws.Cells[2, 2].Value = report.RecipeName;
                ws.Cells[3, 1].Value = "Planned Quantity";
                ws.Cells[3, 2].Value = report.PlannedQuantity;
                ws.Cells[4, 1].Value = "Actual Quantity";
                ws.Cells[4, 2].Value = report.ActualQuantity;
                ws.Cells[5, 1].Value = "Planned Cost";
                ws.Cells[5, 2].Value = report.PlannedCost;
                ws.Cells[6, 1].Value = "Total Cost";
                ws.Cells[6, 2].Value = report.TotalCost;
                ws.Cells[7, 1].Value = "Expected Revenue";
                ws.Cells[7, 2].Value = report.ExpectedRevenue;
                ws.Cells[8, 1].Value = "Actual Revenue";
                ws.Cells[8, 2].Value = report.ActualRevenue;
                ws.Cells[9, 1].Value = "Expected Profit";
                ws.Cells[9, 2].Value = report.ExpectedProfit;
                ws.Cells[10, 1].Value = "Actual Profit";
                ws.Cells[10, 2].Value = report.ActualProfit;
                ws.Cells[11, 1].Value = "Profit Margin";
                ws.Cells[11, 2].Value = report.ProfitMargin;
                ws.Cells[12, 1].Value = "Actual Profit Margin";
                ws.Cells[12, 2].Value = report.ActualProfitMargin;
                // Cost Breakdown
                ws.Cells[14, 1].Value = "Cost Breakdown";
                ws.Cells[15, 1].Value = "Item";
                ws.Cells[15, 2].Value = "Cost";
                int row = 16;
                foreach (var item in report.CostBreakdown)
                {
                    ws.Cells[row, 1].Value = item.Name;
                    ws.Cells[row, 2].Value = item.Cost;
                    row++;
                }
                package.SaveAs(new FileInfo(filePath));
            }
            return filePath;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error exporting production batch analytics report to Excel");
            throw new ApplicationException($"Error exporting report: {ex.Message}", ex);
        }
    }

    public async Task<string> ExportProductionBatchAnalyticsReportToCsvAsync(object batchAnalyticsReport, string filePath)
    {
        try
        {
            var report = batchAnalyticsReport as dynamic;
            var sb = new StringBuilder();
            sb.AppendLine("Field,Value");
            sb.AppendLine($"Batch ID,{report.BatchId}");
            sb.AppendLine($"Recipe,{report.RecipeName}");
            sb.AppendLine($"Planned Quantity,{report.PlannedQuantity}");
            sb.AppendLine($"Actual Quantity,{report.ActualQuantity}");
            sb.AppendLine($"Planned Cost,{report.PlannedCost}");
            sb.AppendLine($"Total Cost,{report.TotalCost}");
            sb.AppendLine($"Expected Revenue,{report.ExpectedRevenue}");
            sb.AppendLine($"Actual Revenue,{report.ActualRevenue}");
            sb.AppendLine($"Expected Profit,{report.ExpectedProfit}");
            sb.AppendLine($"Actual Profit,{report.ActualProfit}");
            sb.AppendLine($"Profit Margin,{report.ProfitMargin}");
            sb.AppendLine($"Actual Profit Margin,{report.ActualProfitMargin}");
            sb.AppendLine();
            sb.AppendLine("Cost Breakdown");
            sb.AppendLine("Item,Cost");
            foreach (var item in report.CostBreakdown)
            {
                sb.AppendLine($"{item.Name},{item.Cost}");
            }
            File.WriteAllText(filePath, sb.ToString());
            return filePath;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error exporting production batch analytics report to CSV");
            throw new ApplicationException($"Error exporting report: {ex.Message}", ex);
        }
    }
    
    private Cell CreateReportHeaderCell(string text)
    {
        var cell = new Cell().Add(new Paragraph(text).SetBold());
        cell.SetBackgroundColor(ColorConstants.LIGHT_GRAY);
        return cell;
    }
    
    private Cell CreateReportSummaryCell(string label, string value, Color backgroundColor)
    {
        var cell = new Cell();
        cell.Add(new Paragraph(label).SetBold());
        cell.Add(new Paragraph(value));
        cell.SetBackgroundColor(backgroundColor);
        return cell;
    }
    
    private Cell CreateReportDetailCell(string text)
    {
        return new Cell().Add(new Paragraph(text));
    }
}

public interface IReportExportService
{
    Task<string> ExportInventoryValuationReportToPdfAsync(InventoryValuationReport report, string filePath);
    Task<string> ExportExpiringBatchReportToPdfAsync(ExpiringBatchReport report, string filePath);
    Task<string> ExportProductionBatchAnalyticsReportToPdfAsync(object batchAnalyticsReport, string filePath);
    Task<string> ExportProductionBatchAnalyticsReportToExcelAsync(object batchAnalyticsReport, string filePath);
    Task<string> ExportProductionBatchAnalyticsReportToCsvAsync(object batchAnalyticsReport, string filePath);
}
