using CakeBistro.Core.Models;

namespace CakeBistro.Repositories
{
    // Interface for customer repository operations
    public interface ICustomerRepository : IRepository<Customer>
    {
        // Get all customers
        Task<IEnumerable<Customer>> GetAllAsync();
        
        // Get customer by ID
        Task<Customer> GetByIdAsync(Guid id);
        
        // Add a new customer
        Task AddAsync(Customer customer);
        
        // Update an existing customer
        Task UpdateAsync(Customer customer);
        
        // Delete a customer
        Task DeleteAsync(Guid id);
        
        // Search customers by name or contact information
        Task<IEnumerable<Customer>> SearchCustomersAsync(string searchTerm);
        
        // Get active customers
        Task<IEnumerable<Customer>> GetActiveCustomersAsync();
    }
}
