using Microsoft.Maui.Controls;
using Microsoft.Extensions.DependencyInjection;
using CakeBistro.Core.Models;
using System.Collections.ObjectModel;
using System.Linq;

namespace CakeBistro.Views
{
    public partial class StockTakePage : ContentPage
    {
        public ObservableCollection<StockTake> StockTakes { get; set; } = new();
        private readonly CakeBistroContext _db;

        public StockTakePage()
        {
            InitializeComponent();
            _db = Application.Current.Services.GetService<CakeBistroContext>();
            LoadStockTakes();
            StockTakeListView.ItemsSource = StockTakes;
        }

        private void LoadStockTakes()
        {
            StockTakes.Clear();
            foreach (var item in _db.StockTakes.OrderByDescending(st => st.Date))
                StockTakes.Add(item);
        }

        private async void OnAddStockTakeClicked(object sender, EventArgs e)
        {
            await Shell.Current.GoToAsync("stocktakeform");
        }

        private async void OnStockTakeSelected(object sender, SelectionChangedEventArgs e)
        {
            if (e.CurrentSelection.FirstOrDefault() is StockTake selected)
            {
                // Pass the selected StockTake Id as a query parameter for editing
                await Shell.Current.GoToAsync($"stocktakeform?id={selected.Id}");
                StockTakeListView.SelectedItem = null;
            }
        }

        private async void OnDeleteStockTake(object sender, EventArgs e)
        {
            if (sender is SwipeItem swipeItem && swipeItem.CommandParameter is StockTake stockTake)
            {
                bool confirm = await Application.Current.MainPage.DisplayAlert("Delete", "Are you sure you want to delete this record?", "Yes", "No");
                if (confirm)
                {
                    _db.StockTakes.Remove(stockTake);
                    await _db.SaveChangesAsync();
                    LoadStockTakes();
                }
            }
        }

        protected override void OnAppearing()
        {
            base.OnAppearing();
            LoadStockTakes();
        }
    }
}
