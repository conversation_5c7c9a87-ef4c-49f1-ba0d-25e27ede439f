<?xml version="1.0" encoding="utf-8" ?>
<ContentView xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="CakeBistro.Views.SalesOrderFormFields"
             xmlns:models="clr-namespace:CakeBistro.Models">
    <StackLayout Spacing="16" Padding="10">
        <Picker Title="Customer"
                ItemsSource="{Binding Customers}"
                SelectedItem="{Binding SelectedCustomer}"
                ItemDisplayBinding="{Binding Name}" />
        <DatePicker Date="{Binding OrderDate}" />
        <Entry Placeholder="Location" Text="{Binding Location}" />
        <Switch IsToggled="{Binding IsDelivery}" />
        <Editor Placeholder="Notes" Text="{Binding Notes}" HeightRequest="100" />
    </StackLayout>
</ContentView>
