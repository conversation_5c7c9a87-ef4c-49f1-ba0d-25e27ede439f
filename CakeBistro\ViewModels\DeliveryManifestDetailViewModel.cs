using System.Collections.ObjectModel;
using System.Windows.Input;
using CakeBistro.Models;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;

namespace CakeBistro.ViewModels
{
    public partial class DeliveryManifestDetailViewModel : ObservableObject
    {
        [ObservableProperty]
        private DeliveryManifest manifest;

        [ObservableProperty]
        private ObservableCollection<Vehicle> vehicleOptions = new();

        [ObservableProperty]
        private ObservableCollection<Driver> driverOptions = new();

        [ObservableProperty]
        private ObservableCollection<string> statusOptions = new()
        {
            "Pending",
            "In Transit",
            "Delivered",
        };

        public ICommand SaveManifestCommand { get; }

        public DeliveryManifestDetailViewModel()
        {
            Manifest = new DeliveryManifest();
            SaveManifestCommand = new RelayCommand(SaveManifest);
            // TODO: Load options from service
        }

        private void SaveManifest()
        {
            // TODO: Implement save logic
        }
    }
}
