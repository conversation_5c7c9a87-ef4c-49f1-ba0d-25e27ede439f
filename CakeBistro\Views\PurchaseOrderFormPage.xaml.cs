using Microsoft.Maui.Controls;
using Microsoft.Extensions.DependencyInjection;
using CakeBistro.ViewModels;
using CakeBistro.Services;
using CakeBistro.Core.Interfaces;

namespace CakeBistro.Views
{
    public partial class PurchaseOrderFormPage : ContentPage
    {
        public PurchaseOrderFormPage()
        {
            InitializeComponent();
            var orderService = MauiProgram.ServiceProvider.GetService<IPurchaseOrderService>();
            var supplierService = MauiProgram.ServiceProvider.GetService<ISupplierService>();
            var rawService = MauiProgram.ServiceProvider.GetService<IRawMaterialService>();
            BindingContext = new PurchaseOrderFormViewModel(orderService, supplierService, rawService);
        }

        private void InitializeComponent() { /* generated by XAML compiler */ }
    }
}
