<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="CakeBistro.Views.Admin.RoleManagementPage"
             Title="Role Management">
    <ScrollView>
        <StackLayout Padding="20">
            <Label Text="Role Management"
                   FontSize="24"
                   FontAttributes="Bold"
                   HorizontalOptions="Center"
                   Margin="0,0,0,20" />

            <Button Text="Add New Role"
                    Command="{Binding AddRoleCommand}"
                    BackgroundColor="Blue"
                    TextColor="White"
                    Margin="0,0,0,20" />

            <CollectionView ItemsSource="{Binding Roles}">
                <CollectionView.ItemTemplate>
                    <DataTemplate>
                        <Grid Padding="10" RowDefinitions="Auto,Auto" ColumnDefinitions="*,Auto,Auto">
                            <Label Grid.Row="0" Grid.Column="0"
                                   Text="{Binding Name}"
                                   FontSize="18"
                                   FontAttributes="Bold" />
                            <Label Grid.Row="1" Grid.Column="0"
                                   Text="{Binding Description}"
                                   FontSize="14"
                                   TextColor="Gray" />
                            <Button Grid.Row="0" Grid.Column="1"
                                    Text="Edit"
                                    Command="{Binding Source={RelativeSource AncestorType={x:Type ContentPage}}, Path=BindingContext.EditRoleCommand}"
                                    CommandParameter="{Binding .}"
                                    BackgroundColor="Orange"
                                    TextColor="White"
                                    Margin="5,0" />
                            <Button Grid.Row="0" Grid.Column="2"
                                    Text="Delete"
                                    Command="{Binding Source={RelativeSource AncestorType={x:Type ContentPage}}, Path=BindingContext.DeleteRoleCommand}"
                                    CommandParameter="{Binding .}"
                                    BackgroundColor="Red"
                                    TextColor="White"
                                    Margin="5,0" />
                        </Grid>
                    </DataTemplate>
                </CollectionView.ItemTemplate>
            </CollectionView>
        </StackLayout>
    </ScrollView>
</ContentPage>
