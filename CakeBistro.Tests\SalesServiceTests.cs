// Create SalesServiceTests.cs
using CakeBistro.Services;
using CakeBistro.Core.Models;
using CakeBistro.Repositories;
using Microsoft.EntityFrameworkCore;
using Xunit;

namespace CakeBistro.Tests;

public class SalesServiceTests
{
    private readonly CakeBistroContext _context;
    private readonly ISalesService _salesService;
    private readonly IRepository<Customer> _customerRepository;
    private readonly IRepository<SalesOrder> _salesRepository;
    private readonly IInventoryRepository _inventoryRepository;
    private readonly IRepository<DeliveryVehicle> _vehicleRepository;

    public SalesServiceTests()
    {
        // Set up in-memory database
        var options = new DbContextOptionsBuilder<CakeBistroContext>()
            .UseInMemoryDatabase(databaseName: "TestDatabase")
            .Options;

        _context = new CakeBistroContext(options);
        
        // Initialize repositories
        _customerRepository = new CustomerRepository(_context);
        _salesRepository = new SalesRepository(_context);
        _inventoryRepository = new InventoryRepository(_context);
        _vehicleRepository = new BaseRepository<DeliveryVehicle>(_context);
        
        // Initialize service with test repositories
        _salesService = new SalesService(
            _customerRepository,
            _salesRepository,
            _inventoryRepository,
            _vehicleRepository);
    }

    [Fact]
    public async Task AddCustomerAsync_ShouldAddCustomer()
    {
        // Arrange
        var customer = new Customer
        {
            Name = "Test Customer",
            Email = "<EMAIL>",
            Phone = "************",
            Address = "123 Test Street"
        };

        // Act
        var result = await _salesService.AddCustomerAsync(customer);
        
        // Assert
        Assert.NotNull(result);
        Assert.Equal("Test Customer", result.Name);
        Assert.True(result.Id > 0);
    }

    [Fact]
    public async Task GetCustomerByIdAsync_ShouldReturnCustomer()
    {
        // Arrange
        var customer = new Customer
        {
            Name = "Test Customer",
            Email = "<EMAIL>",
            Phone = "************",
            Address = "123 Test Street"
        };
        
        var addedCustomer = await _salesService.AddCustomerAsync(customer);
        
        // Act
        var result = await _salesService.GetCustomerByIdAsync(addedCustomer.Id);
        
        // Assert
        Assert.NotNull(result);
        Assert.Equal(addedCustomer.Id, result.Id);
        Assert.Equal("Test Customer", result.Name);
    }

    [Fact]
    public async Task UpdateCustomerAsync_ShouldUpdateCustomer()
    {
        // Arrange
        var customer = new Customer
        {
            Name = "Test Customer",
            Email = "<EMAIL>",
            Phone = "************",
            Address = "123 Test Street"
        };
        
        var addedCustomer = await _salesService.AddCustomerAsync(customer);
        
        // Modify customer
        addedCustomer.Name = "Updated Customer";
        addedCustomer.Phone = "************";
        
        // Act
        await _salesService.UpdateCustomerAsync(addedCustomer);
        
        // Get updated customer
        var result = await _salesService.GetCustomerByIdAsync(addedCustomer.Id);
        
        // Assert
        Assert.NotNull(result);
        Assert.Equal("Updated Customer", result.Name);
        Assert.Equal("************", result.Phone);
    }

    [Fact]
    public async Task DeleteCustomerAsync_ShouldRemoveCustomer()
    {
        // Arrange
        var customer = new Customer
        {
            Name = "Test Customer",
            Email = "<EMAIL>",
            Phone = "************",
            Address = "123 Test Street"
        };
        
        var addedCustomer = await _salesService.AddCustomerAsync(customer);
        
        // Act
        await _salesService.DeleteCustomerAsync(addedCustomer.Id);
        
        // Try to get deleted customer
        var result = await _salesService.GetCustomerByIdAsync(addedCustomer.Id);
        
        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task CreateSalesOrderAsync_ShouldCreateOrder()
    {
        // Arrange
        var order = new SalesOrder
        {
            CustomerId = 1,
            OrderDate = DateTime.Now,
            Status = "New",
            TaxRate = 0.1m,
            Discount = 0.05m,
            FuelCost = 5.0m,
            Items = new List<OrderItem>
            {
                new OrderItem { ProductId = 1, Quantity = 2, UnitPrice = 10.0m },
                new OrderItem { ProductId = 2, Quantity = 1, UnitPrice = 15.0m }
            }
        };

        // Act
        var result = await _salesService.CreateSalesOrderAsync(order);
        
        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Items.Count);
        Assert.Equal(39.0m, result.TotalAmount); // (2*10 + 1*15) * 1.1 (tax) - 5% discount + 5 fuel cost = 39.0
        Assert.Equal("New", result.Status);
    }

    [Fact]
    public async Task GetSalesOrdersByDateRangeAsync_ShouldReturnOrdersInDateRange()
    {
        // Arrange
        var date1 = new DateTime(2023, 1, 1);
        var date2 = new DateTime(2023, 1, 15);
        var date3 = new DateTime(2023, 2, 1);
        
        var order1 = new SalesOrder { OrderDate = date1, CustomerId = 1, Status = "New" };
        var order2 = new SalesOrder { OrderDate = date2, CustomerId = 1, Status = "Completed" };
        var order3 = new SalesOrder { OrderDate = date3, CustomerId = 1, Status = "Shipped" };
        
        await _salesService.CreateSalesOrderAsync(order1);
        await _salesService.CreateSalesOrderAsync(order2);
        await _salesService.CreateSalesOrderAsync(order3);
        
        // Act
        var result = await _salesService.GetSalesOrdersByDateRangeAsync(new DateTime(2023, 1, 1), new DateTime(2023, 1, 31));
        
        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count());
        Assert.Contains(result, o => o.OrderDate == date1);
        Assert.Contains(result, o => o.OrderDate == date2);
        Assert.DoesNotContain(result, o => o.OrderDate == date3);
    }

    [Fact]
    public async Task GetCustomerSalesHistoryAsync_ShouldReturnCustomerOrders()
    {
        // Arrange
        const int customerId = 1;
        
        var order1 = new SalesOrder { CustomerId = customerId, OrderDate = new DateTime(2023, 1, 1), Status = "New" };
        var order2 = new SalesOrder { CustomerId = customerId, OrderDate = new DateTime(2023, 1, 15), Status = "Completed" };
        var order3 = new SalesOrder { CustomerId = 2, OrderDate = new DateTime(2023, 2, 1), Status = "Shipped" };
        
        await _salesService.CreateSalesOrderAsync(order1);
        await _salesService.CreateSalesOrderAsync(order2);
        await _salesService.CreateSalesOrderAsync(order3);
        
        // Act
        var result = await _salesService.GetCustomerSalesHistoryAsync(customerId);
        
        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Count());
        Assert.All(result, o => Assert.Equal(customerId, o.CustomerId));
    }
}