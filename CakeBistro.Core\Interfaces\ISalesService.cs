using System;
using System.Collections.Generic;
using CakeBistro.Core.Models;

namespace CakeBistro.Core.Interfaces
{
    public interface ISalesService
    {
        void ManageLoadingBay(SalesOrder salesOrder);
        void RegisterVehicleDriver(User user);
        SalesOrder CreateSalesOrder(IEnumerable<Product> products);
        void ProcessTransaction(SalesOrder salesOrder);
        void ReconcileCashier(decimal cashBalance);
        IEnumerable<SalesOrder> GetSalesOrders();
    }
}
