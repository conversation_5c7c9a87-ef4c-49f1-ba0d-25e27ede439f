using CakeBistro.Core.Models;

namespace MCakeBistro.Repositories
{
    public class UserRepository : Repository<CakeBistro.Core.Models.User>, IUserRepository
    {
        public UserRepository(InventoryContext context) : base(context)
        {
        }
        
        public async Task<CakeBistro.Core.Models.User> GetUserByUsernameAsync(string username)
        {
            return await _context.Users
                .Include(u => u.CakeBistro.Core.Models.Role)
                .FirstOrDefaultAsync(u => u.Username == username);
        }
        
        public async Task<CakeBistro.Core.Models.User> GetUserByEmailAsync(string email)
        {
            return await _context.Users
                .Include(u => u.CakeBistro.Core.Models.Role)
                .FirstOrDefaultAsync(u => u.Email == email);
        }
        
        public async Task<Guid> AddUserAsync(CakeBistro.Core.Models.User CakeBistro.Core.Models.User)
        {
            await _context.Users.AddAsync(CakeBistro.Core.Models.User);
            await _context.SaveChangesAsync();
            return CakeBistro.Core.Models.User.Id;
        }
        
        public async Task UpdateUserAsync(CakeBistro.Core.Models.User CakeBistro.Core.Models.User)
        {
            // Get the existing CakeBistro.Core.Models.User
            var existingUser = await _context.Users
                .Include(u => u.CakeBistro.Core.Models.Role)
                .FirstOrDefaultAsync(u => u.Id == CakeBistro.Core.Models.User.Id);
            
            if (existingUser != null)
            {
                // Update properties
                existingUser.Username = CakeBistro.Core.Models.User.Username;
                existingUser.Email = CakeBistro.Core.Models.User.Email;
                existingUser.FirstName = CakeBistro.Core.Models.User.FirstName;
                existingUser.LastName = CakeBistro.Core.Models.User.LastName;
                existingUser.IsActive = CakeBistro.Core.Models.User.IsActive;
                existingUser.RoleId = CakeBistro.Core.Models.User.RoleId;
                
                await _context.SaveChangesAsync();
            }
        }
        
        public async Task DeleteUserAsync(Guid id)
        {
            var CakeBistro.Core.Models.User = await _context.Users.FindAsync(id);
            if (CakeBistro.Core.Models.User != null)
            {
                _context.Users.Remove(CakeBistro.Core.Models.User);
                await _context.SaveChangesAsync();
            }
        }
        
        public async Task<IEnumerable<CakeBistro.Core.Models.User>> GetAllUsersWithRolesAsync()
        {
            return await _context.Users
                .Include(u => u.CakeBistro.Core.Models.Role)
                .ToListAsync();
        }
        
        public async Task<IEnumerable<CakeBistro.Core.Models.User>> GetUsersByRoleAsync(Guid roleId)
        {
            return await _context.Users
                .Include(u => u.CakeBistro.Core.Models.Role)
                .Where(u => u.RoleId == roleId)
                .ToListAsync();
        }
        
        public async Task<IEnumerable<Permission>> GetUserPermissionsAsync(Guid userId)
        {
            return await _context.UserPermissions
                .Where(up => up.UserId == userId)
                .Select(up => up.Permission)
                .ToListAsync();
        }
        
        public async Task LogUserActivityAsync(Guid userId, string activityType, string description, string ipAddress)
        {
            var activity = new UserActivity
            {
                Id = Guid.NewGuid(),
                UserId = userId,
                ActivityType = activityType,
                Description = description,
                IpAddress = ipAddress
            };
            
            await _context.UserActivities.AddAsync(activity);
            await _context.SaveChangesAsync();
        }
        
        public async Task<bool> ValidateUserCredentialsAsync(string username, string password)
        {
            var CakeBistro.Core.Models.User = await _context.Users
                .Include(u => u.CakeBistro.Core.Models.Role)
                .FirstOrDefaultAsync(u => u.Username == username);
            
            if (CakeBistro.Core.Models.User == null)
                return false;
            
            // In a real application, this would validate against hashed password
            // For demonstration purposes only, we're comparing plain text
            return CakeBistro.Core.Models.User.PasswordHash == password;
        }
        
        public async Task<bool> ChangePasswordAsync(Guid userId, string oldPassword, string newPassword)
        {
            var CakeBistro.Core.Models.User = await _context.Users.FindAsync(userId);
            
            if (CakeBistro.Core.Models.User == null)
                return false;
            
            // In a real application, this would validate against hashed password
            // For demonstration purposes only, we're comparing plain text
            if (CakeBistro.Core.Models.User.PasswordHash != oldPassword)
                return false;
            
            CakeBistro.Core.Models.User.PasswordHash = newPassword;
            await _context.SaveChangesAsync();
            return true;
        }
    }
}
