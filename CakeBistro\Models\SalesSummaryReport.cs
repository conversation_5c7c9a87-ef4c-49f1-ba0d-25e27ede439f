namespace CakeBistro.Models;

public class SalesSummaryReport
{
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public decimal TotalSales { get; set; }
    public int OrderCount { get; set; }
    public decimal AverageOrderValue { get; set; }
    public string? TopSellingProduct { get; set; }
    public decimal TotalProfit { get; set; }
    public List<ProductSalesSummary> ProductSummaries { get; set; } = new();
}

public class ProductSalesSummary
{
    public int ProductId { get; set; }
    public string? ProductName { get; set; }
    public decimal TotalSales { get; set; }
    public decimal TotalProfit { get; set; }
}
