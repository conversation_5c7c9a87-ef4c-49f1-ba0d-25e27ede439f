
using CakeBistro.Services;

namespace CakeBistro.Views;

/// <summary>
/// Implementation of ThemeSettingsPage that also implements IThemable interface
/// </summary>
public partial class ThemeSettingsPage : ContentPage, IThemable
{
    private readonly IThemeService _themeService;
    private List<ThemeInfo> _availableThemes;
    private ThemeConfiguration _currentTheme;
    private bool _isDarkModeEnabled;
    private string _accentColor;
    private string _backgroundColor;
    private string _textColor;
    private string _primaryFont;
    private string _secondaryFont;

    public ThemeSettingsPage(IThemeService themeService)
    {
        InitializeComponent();
        _themeService = themeService ?? throw new ArgumentNullException(nameof(themeService));
        
        // Initialize the page
        InitializeAsync();
    }

    /// <inheritdoc />
    public void ApplyTheme(ThemeConfiguration theme)
    {
        if (theme != null)
        {
            // Update UI with new theme
            CurrentTheme = theme;
            IsDarkModeEnabled = theme.IsDarkModeEnabled;
            AccentColor = theme.AccentColor;
            BackgroundColor = theme.BackgroundColor;
            TextColor = theme.TextColor;
            PrimaryFont = theme.PrimaryFontFamily;
            SecondaryFont = theme.SecondaryFontFamily;
            
            // Update preview
            UpdateThemePreview();
        }
    }

    /// <summary>
    /// Updates the theme preview section
    /// </summary>
    private void UpdateThemePreview()
    {
        // Update preview elements based on current theme
        if (previewGrid != null)
        {
            try
            {
                // Parse colors
                var backgroundColor = Color.Parse(BackgroundColor);
                var textColor = Color.Parse(TextColor);
                var accentColor = Color.Parse(AccentColor);
                
                // Update grid background
                previewGrid.BackgroundColor = backgroundColor;
                
                // Update text element colors
                if (previewLabel != null)
                {
                    previewLabel.TextColor = textColor;
                    previewLabel.BackgroundColor = accentColor;
                }
                
                if (previewButton != null)
                {
                    previewButton.TextColor = textColor;
                    previewButton.BackgroundColor = accentColor;
                }
                
                // Update other preview elements as needed
                if (previewCard != null)
                {
                    previewCard.BackgroundColor = accentColor;
                }
                
                if (previewIcon != null)
                {
                    previewIcon.Color = textColor;
                }
            }
            catch (Exception ex)
            {
                // Ignore parsing errors for now
            }
        }
    }

}