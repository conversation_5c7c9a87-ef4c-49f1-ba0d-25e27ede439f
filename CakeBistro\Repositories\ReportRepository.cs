using CakeBistro.Core.Models;

namespace MCakeBistro.Repositories
{
    /// <summary>
    /// Implementation of IReportRepository interface
    /// </summary>
    public class ReportRepository : IReportRepository
    {
        private readonly CakeBistroContext _context;
        
        public ReportRepository(CakeBistroContext context)
        {
            _context = context;
        }
        
        /// <inheritdoc />
        public async Task<List<FinancialReport>> GetAllAsync()
        {
            return await _context.FinancialReports.ToListAsync();
        }
        
        /// <inheritdoc />
        public async Task<FinancialReport> GetByIdAsync(int id)
        {
            return await _context.FinancialReports.FindAsync(id);
        }
        
        /// <inheritdoc />
        public async Task AddAsync(FinancialReport report)
        {
            await _context.FinancialReports.AddAsync(report);
            await _context.SaveChangesAsync();
        }
        
        /// <inheritdoc />
        public async Task UpdateAsync(FinancialReport report)
        {
            _context.FinancialReports.Update(report);
            await _context.SaveChangesAsync();
        }
        
        /// <inheritdoc />
        public async Task DeleteAsync(int id)
        {
            var report = await _context.FinancialReports.FindAsync(id);
            if (report != null)
            {
                _context.FinancialReports.Remove(report);
                await _context.SaveChangesAsync();
            }
        }
        
        /// <inheritdoc />
        public async Task<List<FinancialReport>> GetByDateRangeAsync(DateTime startDate, DateTime endDate)
        {
            return await _context.FinancialReports
                .Where(r => r.ReportDate >= startDate && r.ReportDate <= endDate)
                .ToListAsync();
        }
        
        /// <inheritdoc />
        public async Task<List<FinancialReport>> GetByTypeAsync(string reportType)
        {
            return await _context.FinancialReports
                .Where(r => r.ReportType.ToString() == reportType)
                .ToListAsync();
        }
    }
}
