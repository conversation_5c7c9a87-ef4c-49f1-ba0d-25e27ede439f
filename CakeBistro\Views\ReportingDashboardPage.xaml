<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:viewModels="clr-namespace:CakeBistro.ViewModels"
             xmlns:microcharts="clr-namespace:Microcharts.Maui;assembly=Microcharts.Maui"
             x:Class="CakeBistro.Views.ReportingDashboardPage"
             Title="Reporting Dashboard">
    <ContentPage.BindingContext>
        <viewModels:ReportingDashboardViewModel />
    </ContentPage.BindingContext>
    <ScrollView>
        <VerticalStackLayout Spacing="16" Padding="16">
            <Label Text="Reporting Dashboard" FontSize="24" FontAttributes="Bold" />
            <Frame>
                <VerticalStackLayout Spacing="8">
                    <Label Text="Total Assets: {Binding TotalAssets, StringFormat='{0:C}'}" />
                    <Label Text="Net Income: {Binding NetIncome, StringFormat='{0:C}'}" />
                    <Label Text="Inventory Value: {Binding InventoryValue, StringFormat='{0:C}'}" />
                    <Label Text="Low Stock Items: {Binding LowStockCount}" />
                </VerticalStackLayout>
            </Frame>
            <Frame>
                <VerticalStackLayout>
                    <Label Text="KPI Overview" FontAttributes="Bold" />
                    <microcharts:ChartView HeightRequest="200" Chart="{Binding KpiChart}" />
                </VerticalStackLayout>
            </Frame>
            <Frame>
                <VerticalStackLayout>
                    <Label Text="Net Income Trend" FontAttributes="Bold" />
                    <HorizontalStackLayout Spacing="8" VerticalOptions="Center">
                        <Label Text="From:" VerticalOptions="Center" />
                        <DatePicker Date="{Binding StartDate, Mode=TwoWay}" />
                        <Label Text="To:" VerticalOptions="Center" />
                        <DatePicker Date="{Binding EndDate, Mode=TwoWay}" />
                        <Button Text="Refresh Trends" Command="{Binding RefreshTrendsCommand}" />
                        <Button Text="Export Trend Chart" Command="{Binding ExportTrendChartCommand}" />
                    </HorizontalStackLayout>
                    <microcharts:ChartView HeightRequest="200" Chart="{Binding TrendChart}" />
                </VerticalStackLayout>
            </Frame>
            <Frame>
                <VerticalStackLayout>
                    <Label Text="Top Products (by Sales)" FontAttributes="Bold" />
                    <microcharts:ChartView HeightRequest="200" Chart="{Binding TopProductsChart}" />
                    <Button Text="Export Top Products Chart" Command="{Binding ExportTopProductsChartCommand}" Margin="0,8,0,0" />
                </VerticalStackLayout>
            </Frame>
            <Frame>
                <VerticalStackLayout>
                    <Label Text="Sales by Category" FontAttributes="Bold" />
                    <microcharts:ChartView HeightRequest="200" Chart="{Binding SalesByCategoryChart}" />
                    <Button Text="Export Sales by Category Chart" Command="{Binding ExportSalesByCategoryChartCommand}" Margin="0,8,0,0" />
                </VerticalStackLayout>
            </Frame>
            <HorizontalStackLayout Spacing="10" HorizontalOptions="Center">
                <Button Text="Financial Reports" Command="{Binding GoToFinancialReportsCommand}" />
                <Button Text="Inventory Reports" Command="{Binding GoToInventoryReportsCommand}" />
            </HorizontalStackLayout>
        </VerticalStackLayout>
    </ScrollView>
</ContentPage>
