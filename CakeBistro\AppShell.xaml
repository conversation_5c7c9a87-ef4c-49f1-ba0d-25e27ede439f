<?xml version="1.0" encoding="utf-8" ?>
<Shell
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:local="clr-namespace:CakeBistro.Views"
    x:Class="CakeBistro.AppShell"
    Shell.FlyoutBehavior="Flyout"
    Title="CakeBistro Management System">

    <Shell.FlyoutHeader>
        <Grid BackgroundColor="#512BD4" HeightRequest="100">
            <Label Text="CakeBistro" 
                   FontSize="24" 
                   TextColor="White" 
                   HorizontalOptions="Center" 
                   VerticalOptions="Center" 
                   FontAttributes="Bold"/>
        </Grid>
    </Shell.FlyoutHeader>

    <!-- Dashboard -->
    <FlyoutItem Title="Dashboard" Icon="home.png">
        <ShellContent Title="Dashboard" ContentTemplate="{DataTemplate local:MainPage}" Route="main"/>
    </FlyoutItem>

    <!-- Store Management -->
    <FlyoutItem Title="Store Management" Icon="store.png">
        <Tab Title="Inventory">
            <ShellContent Title="Raw Materials" ContentTemplate="{DataTemplate local:RawMaterialsPage}" Route="rawmaterials"/>
            <ShellContent Title="Register Raw Material" ContentTemplate="{DataTemplate local:RawMaterialRegistrationPage}" Route="rawmaterialregistration"/>
            <ShellContent Title="Inventory" ContentTemplate="{DataTemplate local:InventoryPage}" Route="inventory"/>
            <ShellContent Title="Stock Take" ContentTemplate="{DataTemplate local:StockTakePage}" Route="stocktake"/>
            <ShellContent Title="Add/Edit Stock Take" ContentTemplate="{DataTemplate local:StockTakeFormPage}" Route="stocktakeform"/>
            <ShellContent Title="Stock Adjustment" ContentTemplate="{DataTemplate local:StockAdjustmentPage}" Route="stockadjustments"/>
            <ShellContent Title="Add/Edit Stock Adjustment" ContentTemplate="{DataTemplate local:StockAdjustmentFormPage}" Route="stockadjustmentform"/>
            <ShellContent Title="Add/Edit Inventory Batch" ContentTemplate="{DataTemplate local:InventoryFormPage}" Route="inventoryform"/>
            <ShellContent Title="Stock Movement Tracking" ContentTemplate="{DataTemplate local:StockMovementTrackingPage}" Route="stockmovementtracking"/>
            <ShellContent Title="Inter-branch Transfers" ContentTemplate="{DataTemplate local:InterBranchTransferPage}" Route="interbranchtransfers"/>
            <ShellContent Title="Add/Edit Transfer" ContentTemplate="{DataTemplate local:InterBranchTransferFormPage}" Route="interbranchtransferform"/>
        </Tab>
        <Tab Title="Suppliers">
            <ShellContent Title="Suppliers" ContentTemplate="{DataTemplate local:SupplierPage}" Route="suppliers"/>
            <ShellContent Title="Supplier Management" ContentTemplate="{DataTemplate local:SupplierManagementPage}" Route="suppliermanagement"/>
        </Tab>
        <Tab Title="Purchase Orders">
            <ShellContent Title="Purchase Orders" ContentTemplate="{DataTemplate local:PurchaseOrderPage}" Route="purchaseorders"/>
            <ShellContent Title="Add/Edit Order" ContentTemplate="{DataTemplate local:PurchaseOrderFormPage}" Route="purchaseorderform"/>
        </Tab>
    </FlyoutItem>

    <!-- Production Control -->
    <FlyoutItem Title="Production" Icon="production.png">
        <Tab Title="Production">
            <ShellContent Title="Recipes" ContentTemplate="{DataTemplate local:RecipesPage}" Route="recipes"/>
            <ShellContent Title="Recipe Management" ContentTemplate="{DataTemplate local:RecipeManagementPage}" Route="recipemanagement"/>
            <ShellContent Title="Production Batches" ContentTemplate="{DataTemplate local:ProductionBatchPage}" Route="productionbatches"/>
            <ShellContent Title="Production Batch Details" ContentTemplate="{DataTemplate local:ProductionBatchDetailPage}" Route="productionbatchdetail"/>
            <ShellContent Title="Profitability Analysis" ContentTemplate="{DataTemplate local:ProfitabilityAnalysisReport}" Route="profitabilityanalysis"/>
            <ShellContent Title="Cost Calculation" ContentTemplate="{DataTemplate local:CostCalculationDashboard}" Route="costcalculation"/>
            <ShellContent Title="Damage Tracking" ContentTemplate="{DataTemplate local:DamageTrackingPage}" Route="damagetracking"/>
            <ShellContent Title="Inter-Departmental Transfer" ContentTemplate="{DataTemplate local:InterDepartmentTransferPage}" Route="interdepartmenttransfer"/>
            <ShellContent Title="Quality Control" ContentTemplate="{DataTemplate local:QualityControlPage}" Route="qualitycontrol"/>
            <ShellContent Title="Add/Edit Quality Check" ContentTemplate="{DataTemplate local:QualityCheckFormPage}" Route="qualitycheckform"/>
        </Tab>
        <Tab Title="Quality Control">
            <ShellContent Title="Quality Checks" ContentTemplate="{DataTemplate local:QualityControlPage}" Route="qualitycontrol"/>
            <ShellContent Title="Quality Check Details" ContentTemplate="{DataTemplate local:QualityCheckDetailPage}" Route="qualitycheckdetail"/>
        </Tab>
        <Tab Title="Recipes">
            <ShellContent Title="Recipes" ContentTemplate="{DataTemplate local:RecipesPage}" Route="recipes"/>
            <ShellContent Title="Recipe Details" ContentTemplate="{DataTemplate local:RecipeDetailPage}" Route="recipedetail"/>
        </Tab>
    </FlyoutItem>

    <!-- Sales & Distribution -->
    <FlyoutItem Title="Sales" Icon="sales.png">
        <ShellContent Title="Sales Orders" ContentTemplate="{DataTemplate local:SalesOrderPage}" Route="salesorders"/>
        <ShellContent Title="Add/Edit Sales Order" ContentTemplate="{DataTemplate local:SalesOrderFormPage}" Route="salesorderform"/>
        <ShellContent Title="Sales Transactions" ContentTemplate="{DataTemplate local:SalesTransactionListPage}" Route="salestransactions"/>
    </FlyoutItem>

    <!-- Financial Management -->
    <FlyoutItem Title="Finance" Icon="finance.png">
        <ShellContent Title="Finance" ContentTemplate="{DataTemplate local:FinancePage}" Route="finance"/>
    </FlyoutItem>

    <!-- Asset Management -->
    <FlyoutItem Title="Assets" Icon="assets.png">
        <ShellContent Title="Asset Register" ContentTemplate="{DataTemplate local:AssetListPage}" Route="assetregister"/>
        <ShellContent Title="Assets" ContentTemplate="{DataTemplate local:AssetPage}" Route="assets"/>
    </FlyoutItem>

    <!-- Reporting -->
    <FlyoutItem Title="Reports" Icon="reports.png">
        <ShellContent Title="Reports" ContentTemplate="{DataTemplate local:ReportingPage}" Route="reports"/>
        <ShellContent Title="Schedule Report" ContentTemplate="{DataTemplate local:ReportSchedulePage}" Route="reportschedule"/>
    </FlyoutItem>

    <!-- User Management -->
    <FlyoutItem Title="Administration" Icon="admin.png">
        <ShellContent Title="User Management" ContentTemplate="{DataTemplate local:UserManagementPage}" Route="users"/>
    </FlyoutItem>

    <!-- Logistics Management -->
    <FlyoutItem Title="Logistics" Icon="truck.png">
        <Tab Title="Delivery Manifests">
            <ShellContent Title="Manifests" ContentTemplate="{DataTemplate local:DeliveryManifestListPage}" Route="deliverymanifests"/>
        </Tab>
        <Tab Title="Packing">
            <ShellContent Title="Packing Section" ContentTemplate="{DataTemplate local:PackingPage}" Route="packing"/>
        </Tab>
        <Tab Title="Loading">
            <ShellContent Title="Loading Section" ContentTemplate="{DataTemplate local:LoadingPage}" Route="loading"/>
        </Tab>
        <Tab Title="Vehicles">
            <ShellContent Title="Vehicles" ContentTemplate="{DataTemplate local:VehicleListPage}" Route="vehicles"/>
            <ShellContent Title="Add/Edit Vehicle" ContentTemplate="{DataTemplate local:VehicleDetailPage}" Route="vehicledetail"/>
        </Tab>
        <Tab Title="Drivers">
            <ShellContent Title="Drivers" ContentTemplate="{DataTemplate local:DriverListPage}" Route="drivers"/>
            <ShellContent Title="Add/Edit Driver" ContentTemplate="{DataTemplate local:DriverDetailPage}" Route="driverdetail"/>
        </Tab>
    </FlyoutItem>

    <!-- Reporting &amp; Reconciliation -->
    <FlyoutItem Title="Reporting &amp; Reconciliation" Icon="report.png">
        <ShellContent Title="Reporting Dashboard" ContentTemplate="{DataTemplate local:ReportingDashboardPage}" Route="reportingdashboard" />
        <ShellContent Title="Reporting" ContentTemplate="{DataTemplate local:ReportingPage}" Route="reporting" />
        <ShellContent Title="Reconciliation" ContentTemplate="{DataTemplate local:ReconciliationPage}" Route="reconciliation" />
        <ShellContent Title="Financial Reports" ContentTemplate="{DataTemplate local:FinancialReportsPage}" Route="financialreports" />
        <ShellContent Title="Report Scheduling" ContentTemplate="{DataTemplate local:ReportSchedulePage}" Route="reportschedule" />
        <ShellContent Title="Banking Operations" ContentTemplate="{DataTemplate local:BankingPage}" Route="banking" />
    </FlyoutItem>

    <!-- Global Loading Indicator Overlay -->
    <Grid x:Name="GlobalLoadingOverlay"
          BackgroundColor="#********"
          IsVisible="{Binding IsBusy, Source={x:Reference AppShell}}"
          VerticalOptions="FillAndExpand"
          HorizontalOptions="FillAndExpand"
          ZIndex="1000">
        <ActivityIndicator IsRunning="{Binding IsBusy, Source={x:Reference AppShell}}"
                           IsVisible="{Binding IsBusy, Source={x:Reference AppShell}}"
                           Color="{StaticResource Primary}" 
                           VerticalOptions="Center" 
                           HorizontalOptions="Center"
                           WidthRequest="60" HeightRequest="60"/>
    </Grid>

</Shell>
