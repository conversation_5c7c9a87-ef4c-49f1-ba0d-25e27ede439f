/// <summary>
/// Represents a scheduled report
/// </summary>
public class ReportSchedule
{
    /// <summary>
    /// Gets or sets the schedule ID
    /// </summary>
    public int Id { get; set; }
    
    /// <summary>
    /// Gets or sets the type of report to generate
    /// </summary>
    public string ReportType { get; set; }
    
    /// <summary>
    /// Gets or sets the date and time of the schedule
    /// </summary>
    public DateTime ScheduleDate { get; set; }
    
    /// <summary>
    /// Gets or sets how often the report should be generated
    /// </summary>
    public string Frequency { get; set; } // Once, Daily, Weekly, Monthly
    
    /// <summary>
    /// Gets or sets the recipients of the report
    /// </summary>
    public List<string> Recipients { get; set; } = new();
    
    /// <summary>
    /// Gets or sets the format in which to deliver the report
    /// </summary>
    public string DeliveryFormat { get; set; } // Email, Print, FileShare
    
    /// <summary>
    /// Gets or sets additional parameters for the report
    /// </summary>
    public Dictionary<string, string> Parameters { get; set; } = new();
    
    /// <summary>
    /// Gets or sets whether the report is currently active
    /// </summary>
    public bool IsActive { get; set; } = true;
    
    /// <summary>
    /// Gets or sets when the report was last generated
    /// </summary>
    public DateTime? LastGenerated { get; set; }
}