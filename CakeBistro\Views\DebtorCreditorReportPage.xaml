<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2022/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="CakeBistro.Views.DebtorCreditorReportPage"
             Title="Debtor &amp; Creditor Reports">
    <ContentPage.Content>
        <VerticalStackLayout Spacing="20" Padding="20">
            <Label Text="Debtor &amp; Creditor Reports" FontSize="24"/>
            <DatePicker Date="{Binding ReportDate, Mode=TwoWay}"/>
            <Button Text="Load Debtors" Command="{Binding LoadDebtorsCommand}"/>
            <Button Text="Load Creditors" Command="{Binding LoadCreditorsCommand}"/>
            <Button Text="Export Report (PDF)" Command="{Binding ExportDebtorCreditorReportCommand}" CommandParameter="pdf"/>
            <Button Text="Export Report (Excel)" Command="{Binding ExportDebtorCreditorReportCommand}" CommandParameter="excel"/>
            <Label Text="{Binding ExportResultMessage}" TextColor="Green"/>
            <Label Text="Debtors" FontSize="18"/>
            <CollectionView ItemsSource="{Binding Debtors}">
                <CollectionView.ItemTemplate>
                    <DataTemplate>
                        <Frame Padding="10" Margin="5">
                            <VerticalStackLayout>
                                <Label Text="{Binding CustomerId, StringFormat='Customer ID: {0}'}"/>
                                <Label Text="{Binding AmountOwing, StringFormat='Owing: {0:C2}'}"/>
                            </VerticalStackLayout>
                        </Frame>
                    </DataTemplate>
                </CollectionView.ItemTemplate>
            </CollectionView>
            <Label Text="Creditors" FontSize="18"/>
            <CollectionView ItemsSource="{Binding Creditors}">
                <CollectionView.ItemTemplate>
                    <DataTemplate>
                        <Frame Padding="10" Margin="5">
                            <VerticalStackLayout>
                                <Label Text="{Binding SupplierId, StringFormat='Supplier ID: {0}'}"/>
                                <Label Text="{Binding SupplierName}"/>
                                <Label Text="{Binding OutstandingAmount, StringFormat='Outstanding: {0:C2}'}"/>
                            </VerticalStackLayout>
                        </Frame>
                    </DataTemplate>
                </CollectionView.ItemTemplate>
            </CollectionView>
            <Label Text="{Binding StatusMessage}"
                   IsVisible="{Binding StatusMessage, Converter={StaticResource EmptyToFalseConverter}}"
                   TextColor="{Binding StatusColor}" />
        </VerticalStackLayout>
    </ContentPage.Content>
</ContentPage>
