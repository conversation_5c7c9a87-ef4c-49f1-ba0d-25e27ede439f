<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="CakeBistro.MainPage"
             Title="Dashboard">

    <ScrollView>
        <Grid Padding="20">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="Auto" />
                <RowDefinition Height="*" />
            </Grid.RowDefinitions>

            <!-- Header -->
            <StackLayout Grid.Row="0" Spacing="10" Margin="0,0,0,20">
                <Label Text="CakeBistro Dashboard" 
                       FontSize="28" 
                       FontAttributes="Bold" 
                       HorizontalOptions="Center" 
                       TextColor="#512BD4" />
                <Label Text="Welcome to your bakery management system" 
                       FontSize="16" 
                       HorizontalOptions="Center" 
                       TextColor="Gray" />
            </StackLayout>

            <!-- Key Metrics -->
            <Grid Grid.Row="1" Margin="0,0,0,20">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="Auto" /> <!-- Added for extra metrics -->
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="*" />
                </Grid.ColumnDefinitions>

                <!-- Total Raw Materials -->
                <Frame Grid.Row="0" Grid.Column="0" BackgroundColor="#E3F2FD" Margin="5" Padding="15">
                    <StackLayout>
                        <Label Text="Raw Materials" FontSize="14" TextColor="#1976D2" FontAttributes="Bold"/>
                        <Label x:Name="TotalRawMaterialsLabel" Text="Loading..." FontSize="24" TextColor="#1976D2" FontAttributes="Bold"/>
                    </StackLayout>
                </Frame>

                <!-- Total Suppliers -->
                <Frame Grid.Row="0" Grid.Column="1" BackgroundColor="#E8F5E8" Margin="5" Padding="15">
                    <StackLayout>
                        <Label Text="Suppliers" FontSize="14" TextColor="#388E3C" FontAttributes="Bold"/>
                        <Label x:Name="TotalSuppliersLabel" Text="Loading..." FontSize="24" TextColor="#388E3C" FontAttributes="Bold"/>
                    </StackLayout>
                </Frame>

                <!-- Low Stock Items -->
                <Frame Grid.Row="1" Grid.Column="0" BackgroundColor="#FFF3E0" Margin="5" Padding="15">
                    <StackLayout>
                        <Label Text="Low Stock Items" FontSize="14" TextColor="#F57C00" FontAttributes="Bold"/>
                        <Label x:Name="LowStockItemsLabel" Text="Loading..." FontSize="24" TextColor="#F57C00" FontAttributes="Bold"/>
                    </StackLayout>
                </Frame>

                <!-- Recent Orders -->
                <Frame Grid.Row="1" Grid.Column="1" BackgroundColor="#FCE4EC" Margin="5" Padding="15">
                    <StackLayout>
                        <Label Text="Recent Orders" FontSize="14" TextColor="#C2185B" FontAttributes="Bold"/>
                        <Label x:Name="RecentOrdersLabel" Text="Loading..." FontSize="24" TextColor="#C2185B" FontAttributes="Bold"/>
                    </StackLayout>
                </Frame>

                <!-- Stock Movements -->
                <Frame Grid.Row="2" Grid.Column="0" BackgroundColor="#E1F5FE" Margin="5" Padding="15">
                    <StackLayout>
                        <Label Text="Stock Movements" FontSize="14" TextColor="#0288D1" FontAttributes="Bold"/>
                        <Label x:Name="StockMovementsLabel" Text="Loading..." FontSize="24" TextColor="#0288D1" FontAttributes="Bold"/>
                    </StackLayout>
                </Frame>

                <!-- Inter-branch Transfers -->
                <Frame Grid.Row="2" Grid.Column="1" BackgroundColor="#F3E5F5" Margin="5" Padding="15">
                    <StackLayout>
                        <Label Text="Inter-branch Transfers" FontSize="14" TextColor="#7B1FA2" FontAttributes="Bold"/>
                        <Label x:Name="InterBranchTransfersLabel" Text="Loading..." FontSize="24" TextColor="#7B1FA2" FontAttributes="Bold"/>
                    </StackLayout>
                </Frame>

                <!-- Monthly Stock Take -->
                <Frame Grid.Row="3" Grid.Column="0" BackgroundColor="#FFFDE7" Margin="5" Padding="15">
                    <StackLayout>
                        <Label Text="Monthly Stock Take" FontSize="14" TextColor="#FBC02D" FontAttributes="Bold"/>
                        <Label x:Name="MonthlyStockTakeLabel" Text="Loading..." FontSize="24" TextColor="#FBC02D" FontAttributes="Bold"/>
                    </StackLayout>
                </Frame>

                <!-- Stock Adjustments -->
                <Frame Grid.Row="3" Grid.Column="1" BackgroundColor="#E0F2F1" Margin="5" Padding="15">
                    <StackLayout>
                        <Label Text="Stock Adjustments" FontSize="14" TextColor="#00796B" FontAttributes="Bold"/>
                        <Label x:Name="StockAdjustmentsLabel" Text="Loading..." FontSize="24" TextColor="#00796B" FontAttributes="Bold"/>
                    </StackLayout>
                </Frame>
            </Grid>

            <!-- Quality Control Section -->
            <Frame Grid.Row="1" Grid.Column="0" Grid.ColumnSpan="2" 
                   BackgroundColor="#F3E5F5" 
                   Margin="5,20,5,5" 
                   Padding="15">
                <Grid RowDefinitions="Auto,Auto" ColumnDefinitions="*,*,*,*">
                    <Label Text="Quality Control Metrics (30 Days)" 
                           Grid.Row="0" 
                           Grid.ColumnSpan="4" 
                           FontSize="16" 
                           TextColor="#6A1B9A" 
                           FontAttributes="Bold"
                           Margin="0,0,0,10"/>

                    <!-- Total Quality Checks -->
                    <VerticalStackLayout Grid.Row="1" Grid.Column="0">
                        <Label Text="{Binding TotalQualityChecks}" 
                               FontSize="24" 
                               TextColor="#6A1B9A" 
                               FontAttributes="Bold"
                               HorizontalOptions="Center"/>
                        <Label Text="Total Checks" 
                               FontSize="12" 
                               TextColor="#6A1B9A"
                               HorizontalOptions="Center"/>
                    </VerticalStackLayout>

                    <!-- Quality Pass Rate -->
                    <VerticalStackLayout Grid.Row="1" Grid.Column="1">
                        <Label Text="{Binding QualityPassRate, StringFormat='{0:F1}%'}" 
                               FontSize="24" 
                               TextColor="#4CAF50" 
                               FontAttributes="Bold"
                               HorizontalOptions="Center"/>
                        <Label Text="Pass Rate" 
                               FontSize="12" 
                               TextColor="#4CAF50"
                               HorizontalOptions="Center"/>
                    </VerticalStackLayout>

                    <!-- Failed Checks -->
                    <VerticalStackLayout Grid.Row="1" Grid.Column="2">
                        <Label Text="{Binding FailedQualityChecks}" 
                               FontSize="24" 
                               TextColor="#F44336" 
                               FontAttributes="Bold"
                               HorizontalOptions="Center"/>
                        <Label Text="Failed" 
                               FontSize="12" 
                               TextColor="#F44336"
                               HorizontalOptions="Center"/>
                    </VerticalStackLayout>

                    <!-- Pending Reviews -->
                    <VerticalStackLayout Grid.Row="1" Grid.Column="3">
                        <Label Text="{Binding PendingQualityChecks}" 
                               FontSize="24" 
                               TextColor="#FF9800" 
                               FontAttributes="Bold"
                               HorizontalOptions="Center"/>
                        <Label Text="Pending" 
                               FontSize="12" 
                               TextColor="#FF9800"
                               HorizontalOptions="Center"/>
                    </VerticalStackLayout>
                </Grid>
            </Frame>

            <!-- Quick Actions -->
            <StackLayout Grid.Row="2" Spacing="15">
                <Label Text="Quick Actions" FontSize="20" FontAttributes="Bold" Margin="0,10,0,10"/>
                
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" /> <!-- Added for extra actions -->
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="*" />
                    </Grid.ColumnDefinitions>

                    <Button Grid.Row="0" Grid.Column="0" 
                            Text="Add Raw Material" 
                            BackgroundColor="#512BD4" 
                            TextColor="White" 
                            Margin="5"
                            Clicked="OnAddRawMaterialClicked"/>

                    <Button Grid.Row="0" Grid.Column="1" 
                            Text="Create Purchase Order" 
                            BackgroundColor="#512BD4" 
                            TextColor="White" 
                            Margin="5"
                            Clicked="OnCreatePurchaseOrderClicked"/>

                    <Button Grid.Row="1" Grid.Column="0" 
                            Text="View Inventory" 
                            BackgroundColor="#512BD4" 
                            TextColor="White" 
                            Margin="5"
                            Clicked="OnViewInventoryClicked"/>

                    <Button Grid.Row="1" Grid.Column="1" 
                            Text="Generate Reports" 
                            BackgroundColor="#512BD4" 
                            TextColor="White" 
                            Margin="5"
                            Clicked="OnGenerateReportsClicked"/>

                    <Button Grid.Row="2" Grid.Column="0" 
                            Text="Record Stock Movement" 
                            BackgroundColor="#0288D1" 
                            TextColor="White" 
                            Margin="5"
                            Clicked="OnRecordStockMovementClicked"/>

                    <Button Grid.Row="2" Grid.Column="1" 
                            Text="Inter-branch Transfer" 
                            BackgroundColor="#7B1FA2" 
                            TextColor="White" 
                            Margin="5"
                            Clicked="OnInterBranchTransferClicked"/>

                    <Button Grid.Row="3" Grid.Column="0" 
                            Text="Monthly Stock Take" 
                            BackgroundColor="#FBC02D" 
                            TextColor="White" 
                            Margin="5"
                            Clicked="OnMonthlyStockTakeClicked"/>

                    <Button Grid.Row="3" Grid.Column="1" 
                            Text="Stock Adjustment" 
                            BackgroundColor="#00796B" 
                            TextColor="White" 
                            Margin="5"
                            Clicked="OnStockAdjustmentClicked"/>
                </Grid>
            </StackLayout>
        </Grid>
    </ScrollView>

</ContentPage>
