using CakeBistro.Core.Models;
using CakeBistro.data;
using Microsoft.EntityFrameworkCore;

namespace CakeBistro.Repositories
{
    public class SalesOrderItemRepository : BaseRepository<SalesOrderItem>, ISalesOrderItemRepository
    {
        public SalesOrderItemRepository(CakeBistroContext context) : base(context)
        {
        }

        public override async Task DeleteAsync(Guid id)
        {
            var item = await _context.SalesOrderItems.FindAsync(id);
            if (item != null)
            {
                _context.SalesOrderItems.Remove(item);
                await _context.SaveChangesAsync();
            }
        }
        
        public async Task<IEnumerable<SalesOrderItem>> GetItemsByOrderAsync(Guid orderId)
        {
            return await _context.SalesOrderItems
                .Where(i => i.SalesOrderId == orderId)
                .ToListAsync();
        }
    }
}
