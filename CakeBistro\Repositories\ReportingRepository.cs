using CakeBistro.Core.Models;
using CakeBistro.Core.Interfaces;
using Microsoft.EntityFrameworkCore;
using System.Linq.Expressions;

namespace CakeBistro.Repositories
{
    public class ReportingRepository : BaseRepository<Report>, IReportingRepository
    {
        public ReportingRepository(CakeBistroContext context) : base(context)
        {
        }

        public async Task<IEnumerable<SalesSummary>> GetDailySalesSummaryAsync(DateTime date)
        {
            return await _context.SalesSummaries
                .Where(s => s.Date.Date == date.Date)
                .ToListAsync();
        }

        public async Task<IEnumerable<ProductDistribution>> GetProductDistributionByRegionAsync(DateTime startDate, DateTime endDate)
        {
            return await _context.ProductDistributions
                .Where(p => p.Date >= startDate && p.Date <= endDate && p.Region != null)
                .ToListAsync();
        }
    }
}
