using System;
using System.Collections.Generic;
using CakeBistro.Core.Models;

namespace CakeBistro.Repositories
{
    // Interface defining operations specific to CakeBistro.Core.Models.Supplier repository
    public interface ISupplierRepository : IRepository<CakeBistro.Core.Models.Supplier>
    {
        Task<CakeBistro.Core.Models.Supplier> GetByAccountCodeAsync(string accountCode);
        Task<IEnumerable<CakeBistro.Core.Models.Supplier>> GetByProductAsync(int productId);
    }
}
