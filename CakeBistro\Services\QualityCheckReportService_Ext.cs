using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using CakeBistro.Core.Models;
using CakeBistro.Core.Interfaces;

namespace CakeBistro.Services
{
    public partial class QualityCheckReportService : IQualityCheckReportService
    {
        private readonly IQualityControlService _qualityControlService;

        public QualityCheckReportService(IQualityControlService qualityControlService)
        {
            _qualityControlService = qualityControlService;
        }

        public async Task<string> GenerateReportAsync(DateTime startDate, DateTime endDate, string checkType, ProductionBatch batch, string exportFormat)
        {
            var checks = await _qualityControlService.GetChecksByDateRangeAsync(startDate, endDate);
            if (!string.IsNullOrEmpty(checkType))
                checks = checks.Where(c => c.Type == checkType);
            if (batch != null)
                checks = checks.Where(c => c.ProductionBatchId == batch.Id);

            var fileName = $"QualityCheckReport_{DateTime.Now:yyyyMMdd_HHmmss}.{exportFormat.ToLower()}";
            var filePath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments), fileName);

            switch (exportFormat?.ToUpperInvariant())
            {
                case "PDF":
                    return await GeneratePdfReportAsync(checks, filePath);
                case "CSV":
                    return await GenerateCsvReportAsync(checks, filePath);
                case "EXCEL":
                    // TODO: Implement Excel export
                    throw new NotImplementedException("Excel export not implemented yet.");
                default:
                    throw new ArgumentException("Unsupported export format");
            }
        }
    }
}
