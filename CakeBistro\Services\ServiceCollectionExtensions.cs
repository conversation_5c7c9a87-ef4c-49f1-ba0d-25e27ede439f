using System;
// using Microsoft.Extensions.Diagnostics.HealthChecks; // Uncomment if you have health checks
// using Microsoft.EntityFrameworkCore; // Uncomment if you use EF Core
// using Microsoft.AspNetCore.Identity; // Uncomment if you use Identity
// using Microsoft.Extensions.Http; // Uncomment if you use IHttpClientFactory
// using StackExchange.Redis; // Uncomment if you use Redis

using CakeBistro.Services;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace CakeBistro.Services
{
    public static class ServiceCollectionExtensions
    {
        public static IServiceCollection AddApplicationServices(
            this IServiceCollection services,
            IConfiguration configuration
        )
        {
            // Configure cache settings
            var cacheSettings = new CacheSettings();
            configuration.GetSection("CacheSettings").Bind(cacheSettings);
            services.AddSingleton(cacheSettings);

            // Configure and register memory cache
            services.AddMemoryCache(options =>
            {
                options.SizeLimit = cacheSettings.SizeLimit ?? 1024 * 1024 * 10; // Default 10 MB
                // options.CompactOnMemoryPressure = cacheSettings.CompactOnMemoryPressure ?? true; // Obsolete
                options.ExpirationScanFrequency = TimeSpan.FromMinutes(
                    cacheSettings.ExpirationScanFrequencyMinutes
                );
            });

            // Configure and register cache services
            if (cacheSettings.UseRedis)
            {
                // Uncomment and implement if you have Redis and related services
                // services.AddStackExchangeRedisCache(...);
                // services.AddSingleton<IDistributedCacheService, RedisCacheService>();
            }
            else
            {
                // Uncomment and implement if you have these services
                // services.AddSingleton<ICacheService, MemoryCacheService>();
            }

            // Register your other services here as needed

            return services;
        }
    }
}
