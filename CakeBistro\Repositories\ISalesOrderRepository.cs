using CakeBistro.Core.Models;

namespace CakeBistro.Repositories
{
    // Interface for sales order repository operations
    public interface ISalesOrderRepository : IRepository<SalesOrder>
    {
        // Get all sales orders
        Task<IEnumerable<SalesOrder>> GetAllAsync();
        
        // Get sales order by ID
        Task<SalesOrder> GetByIdAsync(Guid id);
        
        // Add a new sales order
        Task AddAsync(SalesOrder order);
        
        // Update an existing sales order
        Task UpdateAsync(SalesOrder order);
        
        // Delete a sales order
        Task DeleteAsync(Guid id);
        
        // Get sales orders by customer
        Task<IEnumerable<SalesOrder>> GetOrdersByCustomerAsync(Guid customerId);
        
        // Get sales orders by status
        Task<IEnumerable<SalesOrder>> GetOrdersByStatusAsync(SalesOrderStatus status);
        
        // Get sales orders within a date range
        Task<IEnumerable<SalesOrder>> GetOrdersByDateRangeAsync(DateTime startDate, DateTime endDate);
    }
}
