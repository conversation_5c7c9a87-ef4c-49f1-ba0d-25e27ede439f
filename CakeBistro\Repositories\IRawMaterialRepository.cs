using System;
using System.Collections.Generic;
using CakeBistro.Core.Models;

namespace CakeBistro.Repositories
{
    // Interface defining operations specific to raw material repository
    public interface IRawMaterialRepository : IRepository<CakeBistro.Core.Models.RawMaterial>
    {
        // Get all raw materials with low stock alerts
        Task<IEnumerable<CakeBistro.Core.Models.RawMaterial>> GetLowStockMaterialsAsync();
        
        // Search raw materials by name or category
        Task<IEnumerable<CakeBistro.Core.Models.RawMaterial>> SearchMaterialsAsync(string searchTerm);
        
        // Get total stock quantity for a specific raw material
        Task<decimal> GetTotalStockQuantityAsync(Guid materialId);
        
        // Get current inventory batches for a raw material
        Task<IEnumerable<InventoryBatch>> GetCurrentBatchesAsync(Guid materialId);
        
        // Get active low stock alerts
        Task<IEnumerable<LowStockAlert>> GetActiveLowStockAlertsAsync();
        
        // Get stock movements for a specific time period
        Task<IEnumerable<StockMovement>> GetStockMovementsByPeriodAsync(DateTime startDate, DateTime endDate);
        
        // Get inter-branch transfers for a specific time period
        Task<IEnumerable<InterBranchTransfer>> GetInterBranchTransfersByPeriodAsync(DateTime startDate, DateTime endDate);
        
        // Generate stock statement for a specific material and time period
        Task<RawMaterialStockStatement> GenerateStockStatementAsync(Guid materialId, DateTime startDate, DateTime endDate);

        // Get raw materials by category
        Task<IEnumerable<CakeBistro.Core.Models.RawMaterial>> GetByCategoryAsync(string category);

        // Get raw material by name
        Task<CakeBistro.Core.Models.RawMaterial> GetByNameAsync(string name);
    }
}
