<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:vm="clr-namespace:CakeBistro.ViewModels"
             xmlns:model="clr-namespace:CakeBistro.Models"
             x:Class="CakeBistro.Views.RecipeDetailPage"
             x:DataType="vm:RecipeDetailViewModel"
             Title="{Binding IsNewRecipe, Converter={StaticResource BoolToStringConverter}, ConverterParameter='New Recipe|Edit Recipe'}">

    <ScrollView>
        <VerticalStackLayout Spacing="10" Padding="20">
            <!-- Product Selection -->
            <Label Text="Product" />
            <Picker ItemsSource="{Binding AvailableProducts}"
                    ItemDisplayBinding="{Binding Name}"
                    SelectedItem="{Binding SelectedProduct}"
                    Title="Select Product"/>

            <!-- Recipe Name -->
            <Label Text="Recipe Name" />
            <Entry Text="{Binding Name}"
                   Placeholder="Enter recipe name"/>

            <!-- Description -->
            <Label Text="Description" />
            <Editor Text="{Binding Description}"
                    Placeholder="Enter recipe description"
                    AutoSize="TextChanges"
                    MaxLength="500"/>

            <!-- Batch Size -->
            <Label Text="Batch Size" />
            <Entry Text="{Binding BatchSize}"
                   Keyboard="Numeric"
                   Placeholder="Enter batch size"/>

            <!-- Preparation Time -->
            <Label Text="Preparation Time (minutes)" />
            <Entry Text="{Binding PreparationTimeMinutes}"
                   Keyboard="Numeric"
                   Placeholder="Enter preparation time in minutes"/>

            <!-- Estimated Cost -->
            <Label Text="Estimated Cost per Batch" />
            <Entry Text="{Binding EstimatedCostPerBatch}"
                   Keyboard="Numeric"
                   Placeholder="Enter estimated cost"/>

            <!-- Instructions -->
            <Label Text="Instructions" />
            <Editor Text="{Binding Instructions}"
                    Placeholder="Enter cooking instructions"
                    AutoSize="TextChanges"
                    MaxLength="2000"/>

            <!-- Recipe Items Section -->
            <Label Text="Ingredients"
                   FontSize="18"
                   FontAttributes="Bold"
                   Margin="0,20,0,10"/>

            <Button Text="Add Ingredient"
                    Command="{Binding AddRecipeItemCommand}"
                    HorizontalOptions="Start"/>

            <CollectionView ItemsSource="{Binding Items}">
                <CollectionView.ItemTemplate>
                    <DataTemplate x:DataType="model:RecipeItem">
                        <SwipeView>
                            <SwipeView.RightItems>
                                <SwipeItems>
                                    <SwipeItem Text="Delete"
                                             BackgroundColor="Red"
                                             Command="{Binding Source={RelativeSource AncestorType={x:Type vm:RecipeDetailViewModel}}, Path=RemoveRecipeItemCommand}"
                                             CommandParameter="{Binding .}"/>
                                </SwipeItems>
                            </SwipeView.RightItems>

                            <Grid Padding="5">
                                <Frame>
                                    <HorizontalStackLayout Spacing="10">
                                        <Label Text="{Binding RawMaterial.Name}"
                                               VerticalOptions="Center"/>
                                        <Label Text="{Binding Quantity, StringFormat='{0:N2}'}"
                                               VerticalOptions="Center"/>
                                        <Label Text="{Binding Unit}"
                                               VerticalOptions="Center"/>
                                    </HorizontalStackLayout>
                                </Frame>
                            </Grid>
                        </SwipeView>
                    </DataTemplate>
                </CollectionView.ItemTemplate>
            </CollectionView>

            <!-- Action Buttons -->
            <HorizontalStackLayout Spacing="10" 
                                 Margin="0,20,0,0"
                                 HorizontalOptions="Center">
                <Button Text="Save"
                        Command="{Binding SaveRecipeCommand}"/>
                <Button Text="Cancel"
                        Command="{Binding CancelCommand}"/>
            </HorizontalStackLayout>
        </VerticalStackLayout>
    </ScrollView>
</ContentPage>
