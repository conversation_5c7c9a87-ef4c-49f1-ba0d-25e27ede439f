using System;
using System.Globalization;
using Microsoft.Maui.Controls;

namespace CakeBistro.Converters
{
    public class StringNotNullOrEmptyConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            return !string.IsNullOrEmpty(value as string);
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            // Since this converter is likely used for one-way binding,
            // we can safely return an empty string as a default value.
            return "";
        }
    }
}
