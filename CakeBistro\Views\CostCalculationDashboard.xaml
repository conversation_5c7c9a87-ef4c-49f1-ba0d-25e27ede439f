<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:controls="clr-namespace:CakeBistro.Controls"
             xmlns:viewModels="clr-namespace:CakeBistro.ViewModels"
             x:Class="CakeBistro.Views.CostCalculationDashboard"
             Title="Cost Calculation Dashboard">
    <ContentPage.BindingContext>
        <viewModels:CostCalculationDashboardViewModel />
    </ContentPage.BindingContext>
    <VerticalStackLayout Padding="20" Spacing="16">
        <controls:CardView>
            <StackLayout>
                <Label Text="Select Recipe" FontAttributes="Bold"/>
                <Picker ItemsSource="{Binding Recipes}"
                        ItemDisplayBinding="{Binding Name}"
                        SelectedItem="{Binding SelectedRecipe, Mode=TwoWay}" />
                <Button Text="Calculate Cost"
                        Command="{Binding CalculateCostCommand}"
                        StyleClass="PrimaryButton AnimatedButton" />
                <Label Text="{Binding CostResult, StringFormat='Total Cost: {0:C2}'}"
                       FontAttributes="Bold" FontSize="18" TextColor="{StaticResource Primary}" />
            </StackLayout>
        </controls:CardView>
    </VerticalStackLayout>
</ContentPage>
