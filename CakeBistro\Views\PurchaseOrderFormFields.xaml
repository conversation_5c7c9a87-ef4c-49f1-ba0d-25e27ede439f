<?xml version="1.0" encoding="utf-8" ?>
<ContentView xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="CakeBistro.Views.PurchaseOrderFormFields"
             xmlns:models="clr-namespace:CakeBistro.Models">
    <StackLayout Spacing="16">
        <Picker Title="Supplier"
                ItemsSource="{Binding Suppliers}"
                SelectedItem="{Binding Supplier}"
                ItemDisplayBinding="{Binding Name}"/>
        <DatePicker Date="{Binding OrderDate}" />
        <!-- TODO: Add CollectionView here for line items -->
    </StackLayout>
</ContentView>
