using System.Collections.ObjectModel;
using System.Windows.Input;
using CakeBistro.Core.Models;
using CakeBistro.Services;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;

namespace CakeBistro.ViewModels
{
    public partial class CostCalculationDashboardViewModel : ObservableObject
    {
        private readonly IProductionService _productionService;
        public ObservableCollection<Recipe> Recipes { get; } = new();
        [ObservableProperty]
        private Recipe selectedRecipe;
        [ObservableProperty]
        private decimal costResult;

        public CostCalculationDashboardViewModel() {}

        public CostCalculationDashboardViewModel(IProductionService productionService)
        {
            _productionService = productionService;
            LoadRecipes();
        }

        private async void LoadRecipes()
        {
            var recipes = await _productionService.GetAllRecipesAsync();
            Recipes.Clear();
            foreach (var r in recipes)
                Recipes.Add(r);
        }

        [RelayCommand]
        private async Task CalculateCost()
        {
            if (SelectedRecipe != null)
                CostResult = await _productionService.CalculateProductionCostAsync(SelectedRecipe.Id);
        }
    }
}
