using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace CakeBistro.Core.Models
{
    public class ProductionBatch : BaseEntity
    {
        [Required]
        public int RecipeId { get; set; }

        [ForeignKey("RecipeId")]
        public Recipe Recipe { get; set; }

        [Required]
        public int QuantityProduced { get; set; }

        [Required]
        public DateTime ProductionDate { get; set; } = DateTime.UtcNow;

        public DateTime? ExpiryDate { get; set; }

        [Required]
        [Column(TypeName = "decimal(18, 2)")]
        public decimal TotalCost { get; set; }

        [Column(TypeName = "decimal(18, 2)")]
        public decimal CostPerUnit { get; set; }

        public string Notes { get; set; }
    }
}
