namespace CakeBistro.Services
{
    public class CacheSettings
    {
        public TimeSpan DefaultCacheDuration { get; set; } = TimeSpan.FromMinutes(30);
        public TimeSpan ShortTermCacheDuration { get; set; } = TimeSpan.FromMinutes(5);
        public TimeSpan LongTermCacheDuration { get; set; } = TimeSpan.FromHours(24);
        public bool UseRedis { get; set; } = false;
        public int? SizeLimit { get; set; }
        public int ExpirationScanFrequencyMinutes { get; set; } = 5;

        public int DefaultCacheDurationSeconds
        {
            get => (int)DefaultCacheDuration.TotalSeconds;
            set => DefaultCacheDuration = TimeSpan.FromSeconds(value);
        }
        public int ShortTermCacheDurationSeconds
        {
            get => (int)ShortTermCacheDuration.TotalSeconds;
            set => ShortTermCacheDuration = TimeSpan.FromSeconds(value);
        }
        public int LongTermCacheDurationSeconds
        {
            get => (int)LongTermCacheDuration.TotalSeconds;
            set => LongTermCacheDuration = TimeSpan.FromSeconds(value);
        }
    }
}
