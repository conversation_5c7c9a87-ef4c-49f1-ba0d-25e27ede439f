using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using CakeBistro.Core.Interfaces;
using CakeBistro.Core.Models;
using CakeBistro.Core.Services;
using Moq;
using Xunit;

namespace CakeBistro.Core.Tests
{
    public class ProductionControlServiceTests
    {
        private readonly Mock<IInventoryBatchRepository> _batchRepoMock;
        private readonly ProductionControlService _service;

        public ProductionControlServiceTests()
        {
            _batchRepoMock = new Mock<IInventoryBatchRepository>();
            _service = new ProductionControlService(_batchRepoMock.Object);
        }

        [Fact]
        public async Task CreateBatchAsync_CreatesBatchWithCorrectFields()
        {
            // Arrange
            var batchNumber = "BATCH-001";
            var traceCode = "TRACE-001";
            _batchRepoMock
                .Setup(r => r.AddAsync(It.IsAny<InventoryBatch>()))
                .Returns(Task.CompletedTask);

            // Act
            var batch = await _service.CreateBatchAsync(
                1,
                batchNumber,
                DateTime.UtcNow,
                DateTime.UtcNow.AddDays(10),
                100,
                50.0m,
                "A1",
                traceCode
            );

            // Assert
            Assert.Equal(batchNumber, batch.BatchNumber);
            Assert.Equal(traceCode, batch.TraceabilityCode);
            Assert.Equal(BatchStatus.InProgress, batch.Status);
        }

        [Fact]
        public async Task UpdateBatchStatusAsync_UpdatesStatus()
        {
            // Arrange
            var batchId = Guid.NewGuid();
            var batch = new InventoryBatch { Id = 1, Status = BatchStatus.InProgress };
            _batchRepoMock.Setup(r => r.GetByIdAsync(batchId)).ReturnsAsync(batch);
            _batchRepoMock.Setup(r => r.UpdateAsync(batch)).Returns(Task.CompletedTask);

            // Act
            await _service.UpdateBatchStatusAsync(batchId, BatchStatus.Completed);

            // Assert
            Assert.Equal(BatchStatus.Completed, batch.Status);
        }
    }
}
