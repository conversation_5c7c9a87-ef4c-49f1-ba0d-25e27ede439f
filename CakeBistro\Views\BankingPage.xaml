<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="CakeBistro.Views.BankingPage"
             xmlns:viewModels="clr-namespace:CakeBistro.ViewModels"
             Title="Banking Operations">
    <ContentPage.BindingContext>
        <viewModels:BankingViewModel />
    </ContentPage.BindingContext>
    <ScrollView>
        <VerticalStackLayout Spacing="16" Padding="16">
            <Label Text="Banking Operations" FontSize="24" HorizontalOptions="Start"/>
            <Label Text="{Binding StatusMessage}"
                   IsVisible="{Binding StatusMessage, Converter={StaticResource EmptyToFalseConverter}}"
                   TextColor="{Binding StatusColor}" />
            <Button Text="Load Accounts" Command="{Binding LoadAccountsCommand}"/>
            <CollectionView ItemsSource="{Binding Accounts}" SelectionMode="Single" SelectedItem="{Binding SelectedAccount, Mode=TwoWay}">
                <CollectionView.ItemTemplate>
                    <DataTemplate>
                        <Frame BorderColor="Gray" Margin="4">
                            <VerticalStackLayout>
                                <Label Text="{Binding AccountName}" FontAttributes="Bold"/>
                                <Label Text="{Binding AccountNumber}"/>
                                <Label Text="{Binding BankName}"/>
                                <Label Text="{Binding Balance, StringFormat='Balance: {0:C}'}"/>
                                <Label Text="{Binding LastReconciled, StringFormat='Last Reconciled: {0:d}'}"/>
                            </VerticalStackLayout>
                        </Frame>
                    </DataTemplate>
                </CollectionView.ItemTemplate>
            </CollectionView>
            <Label Text="Account Details" FontSize="18"/>
            <Entry Placeholder="Account Name" Text="{Binding SelectedAccount.AccountName, Mode=TwoWay}"/>
            <Entry Placeholder="Account Number" Text="{Binding SelectedAccount.AccountNumber, Mode=TwoWay}"/>
            <Entry Placeholder="Bank Name" Text="{Binding SelectedAccount.BankName, Mode=TwoWay}"/>
            <Entry Placeholder="Balance" Text="{Binding SelectedAccount.Balance, Mode=TwoWay}" Keyboard="Numeric"/>
            <HorizontalStackLayout Spacing="10" HorizontalOptions="Center">
                <Button Text="Add" Command="{Binding AddAccountCommand}"/>
                <Button Text="Update" Command="{Binding UpdateAccountCommand}"/>
                <Button Text="Delete" Command="{Binding DeleteAccountCommand}"/>
            </HorizontalStackLayout>
        </VerticalStackLayout>
    </ScrollView>
</ContentPage>
