using CakeBistro.ViewModels;
using CakeBistro.Services;

// Create the view
namespace CakeBistro.Views;

public partial class AssetPage : ContentPage
{
    public AssetPage()
    {
        InitializeComponent();
        BindingContext = CakeBistro.App.Services.GetService<AssetListViewModel>();
    }

    // Event handlers for UI buttons
    private async void OnAddAssetClicked(object sender, EventArgs e)
    {
        if (BindingContext is AssetListViewModel vm)
            await vm.AddAssetAsync();
    }

    private async void OnSaveAssetChanges(object sender, EventArgs e)
    {
        if (BindingContext is AssetListViewModel vm && vm.SelectedAsset != null)
            await vm.UpdateAssetAsync();
    }

    private async void OnDeleteAsset(object sender, EventArgs e)
    {
        if (BindingContext is AssetListViewModel vm)
            await vm.RemoveAssetAsync();
    }

    private async void OnCalculateDepreciation(object sender, EventArgs e)
    {
        if (BindingContext is AssetListViewModel vm)
            await vm.CalculateDepreciationAsync();
    }

    private async void OnViewDepreciationSchedule(object sender, EventArgs e)
    {
        if (BindingContext is AssetListViewModel vm)
            await vm.CalculateDepreciationAsync();
    }

    private async void OnScheduleMaintenance(object sender, EventArgs e)
    {
        if (BindingContext is AssetListViewModel vm)
            await vm.ScheduleMaintenanceAsync();
    }

    private async void OnViewMaintenanceHistory(object sender, EventArgs e)
    {
        if (BindingContext is AssetListViewModel vm)
            await vm.LoadMaintenanceHistoryAsync();
    }

    private async void OnGenerateAssetRegister(object sender, EventArgs e)
    {
        if (BindingContext is AssetListViewModel vm)
            await vm.ExportAssetRegisterAsync("pdf");
    }

    private async void OnExportToPDF(object sender, EventArgs e)
    {
        if (BindingContext is AssetListViewModel vm)
            await vm.ExportAssetRegisterAsync("pdf");
    }

    private async void OnExportToExcel(object sender, EventArgs e)
    {
        if (BindingContext is AssetListViewModel vm)
            await vm.ExportAssetRegisterAsync("excel");
    }

    private async void OnGenerateLocationReport(object sender, EventArgs e)
    {
        if (BindingContext is AssetListViewModel vm)
            await vm.LoadLocationHistoryAsync();
    }

    // Add stubs for other buttons as needed (depreciation, export, etc.)
}
