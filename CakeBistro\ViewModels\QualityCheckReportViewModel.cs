using System;
using System.Collections.ObjectModel;
using System.Threading.Tasks;
using System.Windows.Input;
using CakeBistro.Models;
using CakeBistro.Services;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Maui.Controls;

namespace CakeBistro.ViewModels
{
    public partial class QualityCheckReportViewModel : ObservableObject
    {
        private readonly IQualityCheckReportService _reportService;
        private readonly IQualityControlService _qualityControlService;

        [ObservableProperty]
        private DateTime _startDate = DateTime.Today.AddDays(-7);
        [ObservableProperty]
        private DateTime _endDate = DateTime.Today;
        [ObservableProperty]
        private ObservableCollection<string> _checkTypes = new();
        [ObservableProperty]
        private string _selectedCheckType;
        [ObservableProperty]
        private ObservableCollection<ProductionBatch> _batches = new();
        [ObservableProperty]
        private ProductionBatch _selectedBatch;
        [ObservableProperty]
        private ObservableCollection<string> _exportFormats = new() { "PDF", "Excel", "CSV" };
        [ObservableProperty]
        private string _selectedExportFormat = "PDF";
        [ObservableProperty]
        private bool _isBusy;
        [ObservableProperty]
        private bool _isNotBusy = true;

        private Color _statusColor = Colors.Transparent;
        public Color StatusColor
        {
            get => _statusColor;
            set => SetProperty(ref _statusColor, value);
        }

        public ICommand GenerateReportCommand { get; }

        public QualityCheckReportViewModel(
            IQualityCheckReportService reportService,
            IQualityControlService qualityControlService)
        {
            _reportService = reportService;
            _qualityControlService = qualityControlService;
            GenerateReportCommand = new AsyncRelayCommand(GenerateReportAsync, () => IsNotBusy);
            LoadFilters();
        }

        private async void LoadFilters()
        {
            CheckTypes = new ObservableCollection<string>(await _qualityControlService.GetCheckTypesAsync());
            Batches = new ObservableCollection<ProductionBatch>(await _qualityControlService.GetBatchesAsync());
        }

        private async Task GenerateReportAsync()
        {
            IsBusy = true;
            IsNotBusy = false;
            StatusColor = Color.FromArgb("#1976D2"); // Info color
            try
            {
                var filePath = await _reportService.GenerateReportAsync(
                    StartDate,
                    EndDate,
                    SelectedCheckType,
                    SelectedBatch,
                    SelectedExportFormat
                );
                StatusColor = Color.FromArgb("#388E3C"); // Success color
                await Shell.Current.DisplayAlert("Success", $"Report generated: {filePath}", "OK");
            }
            catch (Exception ex)
            {
                StatusColor = Color.FromArgb("#D32F2F"); // Error color
                await Shell.Current.DisplayAlert("Error", $"Failed to generate report: {ex.Message}", "OK");
            }
            finally
            {
                IsBusy = false;
                IsNotBusy = true;
            }
        }
    }
}
