using CakeBistro.Core.Models;
using CakeBistro.Core.Interfaces;
using Microsoft.EntityFrameworkCore;

namespace CakeBistro.Repositories
{

using Microsoft.EntityFrameworkCore;
using System.Linq.Expressions;
using CakeBistro.Core.Models;

public class SalesRepository : BaseRepository<SalesOrder>, ISalesRepository
{
    public SalesRepository(CakeBistroContext context) : base(context)
    {
    }

    public async Task<IEnumerable<SalesOrder>> GetByCustomerAsync(int customerId)
    {
        return await _context.SalesOrders
            .Where(s => s.CustomerId == customerId)
            .ToListAsync();
    }

    public async Task<IEnumerable<SalesOrder>> GetByDateRangeAsync(DateTime startDate, DateTime endDate)
    {
        return await _context.SalesOrders
            .Where(s => s.OrderDate >= startDate && s.OrderDate <= endDate)
            .ToListAsync();
    }
}
}
