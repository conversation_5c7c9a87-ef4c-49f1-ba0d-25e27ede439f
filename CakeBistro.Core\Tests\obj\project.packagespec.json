﻿"restore":{"projectUniqueName":"C:\\Users\\<USER>\\Desktop\\CakeBistro\\CakeBistro.Core\\Tests\\CakeBistro.Core.Tests.csproj","projectName":"CakeBistro.Core.Tests","projectPath":"C:\\Users\\<USER>\\Desktop\\CakeBistro\\CakeBistro.Core\\Tests\\CakeBistro.Core.Tests.csproj","outputPath":"C:\\Users\\<USER>\\Desktop\\CakeBistro\\CakeBistro.Core\\Tests\\obj\\","projectStyle":"PackageReference","UsingMicrosoftNETSdk":false,"fallbackFolders":["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"],"originalTargetFrameworks":["net8.0"],"sources":{"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\":{},"C:\\Program Files\\dotnet\\library-packs":{},"https://api.nuget.org/v3/index.json":{}},"frameworks":{"net8.0":{"targetAlias":"net8.0","projectReferences":{"C:\\Users\\<USER>\\Desktop\\CakeBistro\\CakeBistro.Core\\CakeBistro.Core.csproj":{"projectPath":"C:\\Users\\<USER>\\Desktop\\CakeBistro\\CakeBistro.Core\\CakeBistro.Core.csproj"}}}},"warningProperties":{"warnAsError":["NU1605"]},"restoreAuditProperties":{"enableAudit":"true","auditLevel":"low","auditMode":"direct"}}"frameworks":{"net8.0":{"targetAlias":"net8.0","dependencies":{"Microsoft.NET.Test.Sdk":{"target":"Package","version":"[17.10.0, )"},"Moq":{"target":"Package","version":"[4.20.72, )"},"xunit":{"target":"Package","version":"[2.9.3, )"},"xunit.runner.visualstudio":{"include":"Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive","suppressParent":"All","target":"Package","version":"[3.1.2, )"}},"imports":["net461","net462","net47","net471","net472","net48","net481"],"assetTargetFallback":true,"warn":true,"frameworkReferences":{"Microsoft.NETCore.App":{"privateAssets":"all"}},"runtimeIdentifierGraphPath":"C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}