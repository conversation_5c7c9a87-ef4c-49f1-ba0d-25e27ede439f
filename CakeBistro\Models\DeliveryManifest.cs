namespace CakeBistro.Models
{
    public class DeliveryManifest
    {
        public DateTime DeliveryDate { get; set; } = DateTime.Today;
        public string? Notes { get; set; }
        public Vehicle? Vehicle { get; set; }
        public Driver? Driver { get; set; }
        public string? Status { get; set; }
    }

    public class Vehicle
    {
        public string RegistrationNumber { get; set; } = string.Empty;
    }

    public class Driver
    {
        public string FirstName { get; set; } = string.Empty;
        public string LastName { get; set; } = string.Empty;
    }
}
