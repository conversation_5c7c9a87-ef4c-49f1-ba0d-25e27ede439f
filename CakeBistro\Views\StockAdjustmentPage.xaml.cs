using Microsoft.Maui.Controls;
using Microsoft.Extensions.DependencyInjection;
using CakeBistro.Core.Models;
using System.Collections.ObjectModel;
using System.Linq;

namespace CakeBistro.Views
{
    public partial class StockAdjustmentPage : ContentPage
    {
        public ObservableCollection<StockAdjustment> StockAdjustments { get; set; } = new();
        private readonly CakeBistroContext _db;

        public StockAdjustmentPage()
        {
            InitializeComponent();
            _db = Application.Current.Services.GetService<CakeBistroContext>();
            LoadStockAdjustments();
            StockAdjustmentListView.ItemsSource = StockAdjustments;
        }

        private void LoadStockAdjustments()
        {
            StockAdjustments.Clear();
            foreach (var item in _db.StockAdjustments.OrderByDescending(sa => sa.Date))
                StockAdjustments.Add(item);
        }

        private async void OnAddStockAdjustmentClicked(object sender, EventArgs e)
        {
            await Shell.Current.GoToAsync("stockadjustmentform");
        }

        private async void OnStockAdjustmentSelected(object sender, SelectionChangedEventArgs e)
        {
            if (e.CurrentSelection.FirstOrDefault() is StockAdjustment selected)
            {
                // Pass the selected StockAdjustment Id as a query parameter for editing
                await Shell.Current.GoToAsync($"stockadjustmentform?id={selected.Id}");
                StockAdjustmentListView.SelectedItem = null;
            }
        }

        private async void OnDeleteStockAdjustment(object sender, EventArgs e)
        {
            if (sender is SwipeItem swipeItem && swipeItem.CommandParameter is StockAdjustment stockAdjustment)
            {
                bool confirm = await Application.Current.MainPage.DisplayAlert("Delete", "Are you sure you want to delete this record?", "Yes", "No");
                if (confirm)
                {
                    _db.StockAdjustments.Remove(stockAdjustment);
                    await _db.SaveChangesAsync();
                    LoadStockAdjustments();
                }
            }
        }

        protected override void OnAppearing()
        {
            base.OnAppearing();
            LoadStockAdjustments();
        }
    }
}
