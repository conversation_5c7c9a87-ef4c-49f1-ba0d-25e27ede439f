using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using CakeBistro.Core.Models;

namespace CakeBistro.Repositories
{
    public class ProductionBatchRepository : BaseRepository<ProductionBatch>, IProductionBatchRepository
    {
        private readonly CakeBistroContext _context;
        private readonly IInventoryRepository _inventoryRepository;

        public ProductionBatchRepository(
            CakeBistroContext context,
            IInventoryRepository inventoryRepository) : base(context)
        {
            _context = context;
            _inventoryRepository = inventoryRepository;
        }

        public async Task<IEnumerable<ProductionBatch>> GetBatchesByDateRangeAsync(DateTime startDate, DateTime endDate)
        {
            return await _context.ProductionBatches
                .Include(b => b.Recipe)
                    .ThenInclude(r => r.Product)
                .Where(b => b.PlannedStartDate >= startDate && b.PlannedStartDate <= endDate)
                .OrderByDescending(b => b.PlannedStartDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<ProductionBatch>> GetBatchesByStatusAsync(string status)
        {
            return await _context.ProductionBatches
                .Include(b => b.Recipe)
                    .ThenInclude(r => r.Product)
                .Where(b => b.Status == status)
                .OrderByDescending(b => b.PlannedStartDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<ProductionBatch>> GetBatchesByRecipeAsync(int recipeId)
        {
            return await _context.ProductionBatches
                .Include(b => b.Recipe)
                    .ThenInclude(r => r.Product)
                .Where(b => b.RecipeId == recipeId)
                .OrderByDescending(b => b.PlannedStartDate)
                .ToListAsync();
        }

        public async Task<decimal> CalculateActualCostAsync(int batchId)
        {
            var batch = await _context.ProductionBatches
                .Include(b => b.Recipe)
                    .ThenInclude(r => r.RecipeItems)
                .FirstOrDefaultAsync(b => b.Id == batchId);

            if (batch == null)
                throw new ArgumentException("Batch not found", nameof(batchId));

            decimal totalCost = 0;
            foreach (var item in batch.Recipe.RecipeItems)
            {
                var latestBatch = await _inventoryRepository.GetLatestBatchAsync(item.RawMaterialId);
                if (latestBatch != null)
                {
                    // Adjust quantity based on actual production quantity vs planned
                    var actualQuantity = (decimal)batch.ActualQuantity / batch.PlannedQuantity * item.Quantity;
                    totalCost += latestBatch.UnitCost * actualQuantity;
                }
            }

            return totalCost;
        }
    }
}
