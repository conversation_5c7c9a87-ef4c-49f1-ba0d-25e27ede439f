using System;
using System.Collections.Generic;

namespace CakeBistro.Core.Models
{
    public class PurchaseOrder
    {
        public int Id { get; set; }
        public DateTime OrderDate { get; set; }
        public int SupplierId { get; set; }
        public decimal TotalAmount { get; set; }
        
        // Navigation properties
        public ICollection<PurchaseOrderItem> Items { get; set; } = new List<PurchaseOrderItem>();
    }
}
