using CakeBistro.ViewModels;
using Microsoft.Maui.Controls;

namespace CakeBistro.Views;

public partial class PurchaseOrderPage : ContentPage
{
    public PurchaseOrderPage(PurchaseOrderViewModel viewModel)
    {
        InitializeComponent();
        BindingContext = viewModel;
    }
    
    protected override async void OnAppearing()
    {
        base.OnAppearing();
        
        if (BindingContext is PurchaseOrderViewModel viewModel)
        {
            await viewModel.LoadDataAsync();
        }
    }
}
