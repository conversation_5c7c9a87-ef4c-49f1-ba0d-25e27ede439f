using CakeBistro.Models;
using CakeBistro.Services;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.Collections.ObjectModel;
using System.Threading.Tasks;
using System;
using Microsoft.Maui.Graphics;

namespace CakeBistro.ViewModels
{
    public partial class SupplierPaymentViewModel : ObservableObject
    {
        private readonly SupplierService _supplierService;

        [ObservableProperty] private ObservableCollection<SupplierPayment> payments = new();
        [ObservableProperty] private int supplierId;
        [ObservableProperty] private decimal amount;
        [ObservableProperty] private DateTime paymentDate = DateTime.Today;
        [ObservableProperty] private string? paymentMethod;
        [ObservableProperty] private string? referenceNumber;
        [ObservableProperty] private string? notes;
        [ObservableProperty] private string? resultMessage;

        private Color _statusColor = Colors.Transparent;
        public Color StatusColor
        {
            get => _statusColor;
            set => SetProperty(ref _statusColor, value);
        }

        public SupplierPaymentViewModel(SupplierService supplierService)
        {
            _supplierService = supplierService;
        }

        [RelayCommand]
        public async Task LoadPaymentsAsync()
        {
            Payments = new ObservableCollection<SupplierPayment>(
                await _supplierService.GetAllSupplierPaymentsAsync());
        }

        [RelayCommand]
        public async Task RecordPaymentAsync()
        {
            if (SupplierId > 0 && Amount > 0)
            {
                var payment = new SupplierPayment
                {
                    SupplierId = SupplierId,
                    Amount = Amount,
                    PaymentDate = PaymentDate,
                    PaymentMethod = PaymentMethod,
                    ReferenceNumber = ReferenceNumber,
                    Notes = Notes
                };
                await _supplierService.RecordSupplierPaymentAsync(payment);
                ResultMessage = "Payment recorded.";
                StatusColor = Color.FromArgb("#388E3C"); // Success color
                await LoadPaymentsAsync();
                Amount = 0;
                PaymentMethod = null;
                ReferenceNumber = null;
                Notes = null;
            }
            else
            {
                ResultMessage = "Supplier and amount are required.";
                StatusColor = Color.FromArgb("#D32F2F"); // Error color
            }
        }
    }
}
