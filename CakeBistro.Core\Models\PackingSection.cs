using System.Collections.Generic;

namespace CakeBistro.Core.Models
{
    public class PackingSection
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public ICollection<PackingItem> Items { get; set; } = new List<PackingItem>();
    }

    public class PackingItem
    {
        public int Id { get; set; }
        public int PackingSectionId { get; set; }
        public int ProductId { get; set; }
        public int Quantity { get; set; }
        public Product Product { get; set; }
    }
}
