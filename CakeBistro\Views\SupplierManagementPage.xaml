<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:controls="clr-namespace:CakeBistro.Controls"
             x:Class="CakeBistro.Views.SupplierManagementPage"
             Title="Manage Suppliers">
    <VerticalStackLayout Padding="20" Spacing="16">
        <Label Text="Supplier Name" FontAttributes="Bold"/>
        <Entry x:Name="SupplierNameEntry" Placeholder="Enter supplier name" />
        <Label Text="Account Code" FontAttributes="Bold"/>
        <Entry x:Name="AccountCodeEntry" Placeholder="Enter account code" />
        <Button Text="Add Supplier" Clicked="OnAddSupplierClicked" StyleClass="PrimaryButton" />
        <CollectionView x:Name="SuppliersListView">
            <CollectionView.ItemTemplate>
                <DataTemplate>
                    <controls:CardView>
                        <StackLayout Orientation="Horizontal" Spacing="10">
                            <Label Text="{Binding Name}" FontAttributes="Bold" />
                            <Label Text="{Binding AccountCode}" TextColor="Gray" />
                        </StackLayout>
                    </controls:CardView>
                </DataTemplate>
            </CollectionView.ItemTemplate>
        </CollectionView>
        <Label Text="{Binding StatusMessage}"
               IsVisible="{Binding StatusMessage, Converter={StaticResource EmptyToFalseConverter}}"
               TextColor="{Binding StatusColor}" />
    </VerticalStackLayout>
</ContentPage>
