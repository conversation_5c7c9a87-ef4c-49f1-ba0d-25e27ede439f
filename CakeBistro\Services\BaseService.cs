using System;
using CakeBistro.Core.Models;

namespace CakeBistro.Services
{
    /// <summary>
    /// Abstract base service class implementing core CRUD operations
    /// </summary>
    /// <typeparam name="T">The entity type</typeparam>
    public abstract class BaseService<T> : IReadRepository<T>, IWriteRepository<T> where T : BaseModel
    {
        protected readonly IRepository<T> _repository;

        protected BaseService(IRepository<T> repository)
        {
            _repository = repository ?? throw new ArgumentNullException(nameof(repository));
        }

        // Implementing IReadRepository<T>
        public virtual async Task<IEnumerable<T>> GetAllAsync()
        {
            return await _repository.GetAllAsync();
        }

        public virtual async Task<T> GetByIdAsync(Guid id)
        {
            if (id == Guid.Empty)
                throw new ArgumentException("Invalid GUID", nameof(id));
                
            return await _repository.GetByIdAsync(id);
        }

        // Implementing IWriteRepository<T>
        public virtual async Task<Guid> AddAsync(T entity)
        {
            if (entity == null)
                throw new ArgumentNullException(nameof(entity));
                
            await _repository.AddAsync(entity);
            return entity.Id;
        }

        public virtual async Task UpdateAsync(T entity)
        {
            if (entity == null)
                throw new ArgumentNullException(nameof(entity));
                
            await _repository.UpdateAsync(entity);
        }

        public virtual async Task DeleteAsync(Guid id)
        {
            if (id == Guid.Empty)
                throw new ArgumentException("Invalid GUID", nameof(id));
                
            await _repository.DeleteAsync(id);
        }

        public virtual async Task<bool> ExistsAsync(Guid id)
        {
            if (id == Guid.Empty)
                throw new ArgumentException("Invalid GUID", nameof(id));
                
            var entity = await _repository.GetByIdAsync(id);
            return entity != null;
        }

        /// <summary>
        /// Validates that an entity exists or throws a business rule validation exception.
        /// </summary>
        /// <typeparam name="T">The type of the entity</typeparam>
        /// <param name="entity">The entity to check for null</param>
        /// <param name="ruleName">The name of the business rule that failed</param>
        /// <param name="message">The error message to use if the entity is null</param>
        protected void CheckEntityExists<T>(T? entity, string ruleName, string message) where T : class
        {
            if (entity == null)
                throw new BusinessRuleValidationException(ruleName, message);
        }

        /// <summary>
        /// Validates that an ID is valid or throws a business rule validation exception.
        /// </summary>
        /// <param name="id">The GUID to validate</param>
        /// <param name="message">The error message to use if the GUID is empty</param>
        protected void ValidateId(Guid id, string message)
        {
            if (id == Guid.Empty)
                throw new BusinessRuleValidationException("InvalidGUID", message);
        }

        /// <summary>
        /// Validates that a string is not null or whitespace.
        /// </summary>
        /// <param name="value">The string value to validate</param>
        /// <param name="ruleName">The name of the business rule that failed</param>
        /// <param name="message">The error message to use if validation fails</param>
        protected void ValidateString(string? value, string ruleName, string message)
        {
            if (string.IsNullOrWhiteSpace(value))
                throw new BusinessRuleValidationException(ruleName, message);
        }
    }
}
