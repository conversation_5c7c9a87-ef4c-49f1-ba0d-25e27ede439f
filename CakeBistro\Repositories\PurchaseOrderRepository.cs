using CakeBistro.Core.Models;
using CakeBistro.Repositories;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace CakeBistro.Repositories;

public class PurchaseOrderRepository : BaseRepository<CakeBistro.Core.Models.PurchaseOrder>, IPurchaseOrderRepository
{
    public PurchaseOrderRepository(CakeBistroContext context) : base(context)
    {
    }
    
    public override async Task<CakeBistro.Core.Models.PurchaseOrder> GetByIdAsync(int id)
    {
        return await _context.Set<CakeBistro.Core.Models.PurchaseOrder>()
            .Include(o => o.CakeBistro.Core.Models.Supplier)
            .Include(o => o.Items)
                .ThenInclude(i => i.CakeBistro.Core.Models.RawMaterial)
            .FirstOrDefaultAsync(o => o.Id == id);
    }
    
    public async Task<IEnumerable<CakeBistro.Core.Models.PurchaseOrder>> GetBySupplierIdAsync(int supplierId)
    {
        return await _context.Set<CakeBistro.Core.Models.PurchaseOrder>()
            .Where(o => o.SupplierId == supplierId)
            .Include(o => o.CakeBistro.Core.Models.Supplier)
            .Include(o => o.Items)
                .ThenInclude(i => i.CakeBistro.Core.Models.RawMaterial)
            .ToListAsync();
    }
    
    public async Task<IEnumerable<CakeBistro.Core.Models.PurchaseOrder>> GetByStatusAsync(string status)
    {
        return await _context.Set<CakeBistro.Core.Models.PurchaseOrder>()
            .Where(o => o.Status == status)
            .Include(o => o.CakeBistro.Core.Models.Supplier)
            .Include(o => o.Items)
                .ThenInclude(i => i.CakeBistro.Core.Models.RawMaterial)
            .ToListAsync();
    }
    
    public async Task<CakeBistro.Core.Models.PurchaseOrder> AddAsync(CakeBistro.Core.Models.PurchaseOrder order)
    {
        await _context.Set<CakeBistro.Core.Models.PurchaseOrder>().AddAsync(order);
        await _context.SaveChangesAsync();
        return order;
    }
    
    public async Task<CakeBistro.Core.Models.PurchaseOrder> UpdateAsync(CakeBistro.Core.Models.PurchaseOrder order)
    {
        var existingOrder = await _context.Set<CakeBistro.Core.Models.PurchaseOrder>()
            .Include(o => o.Items)
            .FirstOrDefaultAsync(o => o.Id == order.Id);
        
        if (existingOrder == null)
            return null;
        
        // Update simple properties
        _context.Entry(existingOrder).CurrentValues.SetValues(order);
        
        // Handle items
        foreach (var item in order.Items)
        {
            var existingItem = existingOrder.Items.FirstOrDefault(i => i.Id == item.Id);
            if (existingItem == null)
            {
                // New item
                item.PurchaseOrderId = existingOrder.Id;
                existingOrder.Items.Add(item);
            }
            else
            {
                // Update existing item
                _context.Entry(existingItem).CurrentValues.SetValues(item);
            }
        }
        
        // Remove deleted items
        var itemsToRemove = existingOrder.Items
            .Where(existingItem => !order.Items.Any(newItem => newItem.Id == existingItem.Id))
            .ToList();
        
        foreach (var item in itemsToRemove)
        {
            existingOrder.Items.Remove(item);
        }
        
        await _context.SaveChangesAsync();
        return existingOrder;
    }
    
    public async Task<bool> DeleteAsync(int id)
    {
        var order = await _context.Set<CakeBistro.Core.Models.PurchaseOrder>().FindAsync(id);
        if (order == null)
            return false;
        
        if (order.Status != "Pending")
            return false; // Can't delete processed orders
        
        _context.Set<CakeBistro.Core.Models.PurchaseOrder>().Remove(order);
        await _context.SaveChangesAsync();
        return true;
    }
    
    public async Task<bool> SubmitPurchaseOrderAsync(int id)
    {
        var order = await _context.Set<CakeBistro.Core.Models.PurchaseOrder>().FindAsync(id);
        if (order == null || order.Status != "Pending")
            return false;
        
        order.Status = "Submitted";
        order.SubmittedDate = DateTime.Now;
        await _context.SaveChangesAsync();
        return true;
    }
    
    public async Task<bool> ReceivePurchaseOrderAsync(int id)
    {
        var order = await _context.Set<CakeBistro.Core.Models.PurchaseOrder>().FindAsync(id);
        if (order == null || order.Status != "Submitted")
            return false;
        
        order.Status = "Received";
        order.ReceivedDate = DateTime.Now;
        
        // Update inventory with received items
        foreach (var item in order.Items)
        {
            var CakeBistro.Core.Models.RawMaterial = await _context.Set<CakeBistro.Core.Models.RawMaterial>().FindAsync(item.RawMaterialId);
            if (CakeBistro.Core.Models.RawMaterial != null)
            {
                CakeBistro.Core.Models.RawMaterial.CurrentStock += item.Quantity;
                CakeBistro.Core.Models.RawMaterial.LastStockTake = DateTime.Now;
            }
        }
        
        await _context.SaveChangesAsync();
        return true;
    }
}
