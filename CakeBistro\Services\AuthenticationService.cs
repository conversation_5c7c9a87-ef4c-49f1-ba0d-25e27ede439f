using CakeBistro.Core.Models;
using CakeBistro.Core.Interfaces;
using MCakeBistro.Repositories;
using CakeBistro.Services;
using Microsoft.EntityFrameworkCore;

namespace CakeBistro.Services
{
    public class AuthenticationService : IAuthenticationService
    {
        private readonly CakeBistroContext _context;
        private readonly ICacheService _cacheService;
        private readonly IUserRepository _userRepository;
        private readonly IRoleRepository _roleRepository;
        
        public AuthenticationService(CakeBistroContext context, ICacheService cacheService, IUserRepository userRepository, IRoleRepository roleRepository)
        {
            _context = context;
            _cacheService = cacheService;
            _userRepository = userRepository;
            _roleRepository = roleRepository;
        }
        
        public async Task<User> AuthenticateAsync(string username, string password, string ipAddress)
        {
            // Validate credentials
            if (string.IsNullOrEmpty(username) || string.IsNullOrEmpty(password))
                return null;
            
            var user = await _userRepository.GetUserByUsernameAsync(username);
            if (user == null || !user.IsActive || !BCrypt.Net.BCrypt.Verify(password, user.PasswordHash))
                return null;
            // Update last login
            user.LastLoginDate = DateTime.UtcNow;
            await _userRepository.UpdateUserAsync(user);
            // Log activity
            await LogUserActivityAsync(user.Id, "Login", "User logged in successfully", ipAddress);
            return user;
        }
        
        public async Task<IEnumerable<User>> GetAllUsersAsync()
        {
            return await _userRepository.GetAllUsersWithRolesAsync();
        }
        
        public async Task<User> GetUserByIdAsync(Guid id)
        {
            return await _userRepository.GetUserByIdAsync(id);
        }
        
        public async Task<Guid> CreateUserAsync(User user, string password)
        {
            // Hash password
            user.PasswordHash = BCrypt.Net.BCrypt.HashPassword(password);
            user.Id = Guid.NewGuid();
            user.CreatedDate = DateTime.UtcNow;
            user.UpdatedDate = DateTime.UtcNow;
            var id = await _userRepository.AddUserAsync(user);
            _cacheService.Invalidate("user_");
            return id;
        }
        
        public async Task UpdateUserAsync(User user)
        {
            await _userRepository.UpdateUserAsync(user);
            _cacheService.Invalidate("user_");
        }
        
        public async Task DeleteUserAsync(Guid id)
        {
            await _userRepository.DeleteUserAsync(id);
            _cacheService.Invalidate("user_");
        }
        
        public async Task<IEnumerable<CakeBistro.Core.Models.Role>> GetAllRolesAsync()
        {
            return await _roleRepository.GetAllRolesWithPermissionsAsync();
        }
        
        public async Task<CakeBistro.Core.Models.Role> GetRoleByIdAsync(Guid id)
        {
            return await _roleRepository.GetByIdAsync(id);
        }
        
        public async Task<Guid> CreateRoleAsync(Role role)
        {
            var id = await _roleRepository.AddRoleAsync(role);
            _cacheService.Invalidate("role_");
            return id;
        }
        
        public async Task UpdateRoleAsync(Role role)
        {
            await _roleRepository.UpdateRoleAsync(role);
            _cacheService.Invalidate("role_");
        }
        
        public async Task DeleteRoleAsync(Guid id)
        {
            await _roleRepository.DeleteRoleAsync(id);
            _cacheService.Invalidate("role_");
        }
        
        public async Task<IEnumerable<Permission>> GetAllPermissionsAsync()
        {
            return await _roleRepository.GetAllPermissionsAsync();
        }
        
        public async Task AssignPermissionsToRoleAsync(Guid roleId, IEnumerable<Guid> permissionIds)
        {
            await _roleRepository.AssignPermissionsToRoleAsync(roleId, permissionIds);
        }
        
        public async Task RemovePermissionsFromRoleAsync(Guid roleId, IEnumerable<Guid> permissionIds)
        {
            await _roleRepository.RemovePermissionsFromRoleAsync(roleId, permissionIds);
        }
        
        public async Task LogUserActivityAsync(Guid userId, string activityType, string description, string ipAddress)
        {
            await _userRepository.LogUserActivityAsync(userId, activityType, description, ipAddress);
        }
        
        public async Task<IEnumerable<UserActivity>> GetUserActivityLogsAsync(Guid userId, DateTime startDate, DateTime endDate)
        {
            return await _userRepository.GetUserActivityLogsAsync(userId, startDate, endDate);
        }
        
        public async Task<bool> HasUserPermissionAsync(Guid userId, string permissionName)
        {
            return await _userRepository.HasUserPermissionAsync(userId, permissionName);
        }
    }
}
