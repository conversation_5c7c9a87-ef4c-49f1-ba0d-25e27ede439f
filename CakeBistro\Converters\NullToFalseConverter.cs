

// Create NullToFalseConverter.cs
using System;
using System.Globalization;
using Microsoft.Maui.Controls;

namespace CakeBistro.Converters;

public class NullToFalseConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        return value == null;
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        // Since this converter is likely used for one-way binding,
        // we can safely return an empty string as a default value.
        return "";
    }