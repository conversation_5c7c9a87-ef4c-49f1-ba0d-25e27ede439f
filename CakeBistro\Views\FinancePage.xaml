<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2022/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="CakeBistro.Views.FinancePage"
             Title="Finance Management">
    <ContentPage.Content>
        <ScrollView>
            <VerticalStackLayout Spacing="25" Padding="30">
                <!-- Bank Accounts -->
                <Label Text="Bank Accounts" FontSize="24" HorizontalOptions="Start"/>
                
                <HorizontalStackLayout>
                    <Entry Placeholder="Search accounts..." WidthRequest="200"/>
                    <Button Text="Add Account" Clicked="OnAddAccountClicked"/>
                </HorizontalStackLayout>
                
                <CollectionView ItemsSource="{Binding BankAccounts}"
                                  SelectedItem="{Binding SelectedBankAccount, Mode=TwoWay}"
                                  SelectionMode="Single">
                    <CollectionView.ItemsLayout>
                        <LinearItemsLayout Orientation="Vertical"/>
                    </CollectionView.ItemsLayout>
                    <CollectionView.ItemTemplate>
                        <DataTemplate>
                            <Frame Padding="10" Margin="5">
                                <Grid ColumnDefinitions="*,*,*" RowDefinitions="Auto,Auto">
                                    <Label Grid.Row="0" Grid.Column="0" Text="{Binding Name}"/>
                                    <Label Grid.Row="0" Grid.Column="1" Text="{Binding Type}"/>
                                    <Label Grid.Row="0" Grid.Column="2" Text="{Binding Balance, StringFormat='{0:C2}'}"/>
                                    
                                    <Label Grid.Row="1" Grid.Column="0" Text="{Binding AccountNumber}"/>
                                    <Label Grid.Row="1" Grid.Column="1" Text="{Binding Status}"/>
                                    <Label Grid.Row="1" Grid.Column="2" Text="{Binding LastReconciled, StringFormat='Last Reconciled: {0:yyyy-MM-dd}'}"/>
                                </Grid>
                            </Frame>
                        </DataTemplate>
                    </CollectionView.ItemTemplate>
                </CollectionView>
                
                <Label Text="Selected Account Details" FontSize="18" HorizontalOptions="Start"/>
                
                <Grid ColumnDefinitions="*,*" RowDefinitions="Auto,Auto,Auto,Auto,Auto,Auto">
                    <Label Grid.Row="0" Grid.Column="0" Text="Name:"/>
                    <Entry Grid.Row="0" Grid.Column="1" Text="{Binding SelectedBankAccount.Name, Mode=TwoWay}"/>
                    
                    <Label Grid.Row="1" Grid.Column="0" Text="Account Number:"/>
                    <Entry Grid.Row="1" Grid.Column="1" Text="{Binding SelectedBankAccount.AccountNumber, Mode=TwoWay}"/>
                    
                    <Label Grid.Row="2" Grid.Column="0" Text="Type:"/>
                    <Picker Grid.Row="2" Grid.Column="1" ItemsSource="{Binding AccountTypes}" SelectedItem="{Binding SelectedBankAccount.Type, Mode=TwoWay}"/>
                    
                    <Label Grid.Row="3" Grid.Column="0" Text="Balance:"/>
                    <Label Grid.Row="3" Grid.Column="1" Text="{Binding SelectedBankAccount.Balance, StringFormat='{0:C2}'}"/>
                    
                    <Label Grid.Row="4" Grid.Column="0" Text="Status:"/>
                    <Picker Grid.Row="4" Grid.Column="1" ItemsSource="{Binding AccountStatuses}" SelectedItem="{Binding SelectedBankAccount.Status, Mode=TwoWay}"/>
                    
                    <Label Grid.Row="5" Grid.Column="0" Text="Notes:"/>
                    <Editor Grid.Row="5" Grid.Column="1" Text="{Binding SelectedBankAccount.Notes, Mode=TwoWay}"/>
                </Grid>
                
                <HorizontalStackLayout Spacing="10" HorizontalOptions="Center">
                    <Button Text="Save Changes" Clicked="OnSaveAccountChanges" IsEnabled="{Binding SelectedBankAccount != null}"/>
                    <Button Text="Delete Account" Clicked="OnDeleteAccount" IsEnabled="{Binding SelectedBankAccount != null}"/>
                    <Button Text="View Transactions" Clicked="OnViewTransactions" IsEnabled="{Binding SelectedBankAccount != null}"/>
                    <Button Text="Reconcile Account" Clicked="OnReconcileAccount" IsEnabled="{Binding SelectedAccountId > 0}"/>
                </HorizontalStackLayout>
                
                <!-- Transaction Management -->
                <Label Text="Transaction Management" FontSize="24" HorizontalOptions="Start"/>
                
                <Grid ColumnDefinitions="*,*,*" RowDefinitions="Auto,Auto,Auto,Auto">
                    <Label Grid.Row="0" Grid.Column="0" Text="Date:"/>
                    <DatePicker Grid.Row="0" Grid.Column="1" Date="{Binding TransactionDate, Mode=TwoWay, StringFormat={0:yyyy-MM-dd}}"/>
                    <Button Grid.Row="0" Grid.Column="2" Text="Today" Clicked="OnSetTodayDate"/>
                    
                    <Label Grid.Row="1" Grid.Column="0" Text="Description:"/>
                    <Entry Grid.Row="1" Grid.Column="1" Grid.ColumnSpan="2" Text="{Binding TransactionDescription, Mode=TwoWay}"/>
                    
                    <Label Grid.Row="2" Grid.Column="0" Text="Amount:"/>
                    <Entry Grid.Row="2" Grid.Column="1" Text="{Binding TransactionAmount, Mode=TwoWay, StringFormat={}{0:C2}}"/>
                    <Picker Grid.Row="2" Grid.Column="2" ItemsSource="{Binding TransactionTypes}" SelectedItem="{Binding SelectedTransactionType, Mode=TwoWay}" WidthRequest="120"/>
                    
                    <Label Grid.Row="3" Grid.Column="0" Text="Category:"/>
                    <Picker Grid.Row="3" Grid.Column="1" ItemsSource="{Binding TransactionCategories}" SelectedItem="{Binding SelectedTransactionCategory, Mode=TwoWay}"/>
                    <Button Grid.Row="3" Grid.Column="2" Text="Record Transaction" Clicked="OnRecordTransaction" IsEnabled="{Binding CanRecordTransaction}"/>
                </Grid>
                
                <!-- Recent Transactions -->
                <Label Text="Recent Transactions" FontSize="24" HorizontalOptions="Start"/>
                
                <HorizontalStackLayout>
                    <DatePicker Date="{Binding StartDate, Mode=TwoWay, StringFormat={0:yyyy-MM-dd}}"/>
                    <DatePicker Date="{Binding EndDate, Mode=TwoWay, StringFormat={0:yyyy-MM-dd}}"/>
                    <Button Text="Filter" Clicked="OnFilterTransactions"/>
                </HorizontalStackLayout>
                
                <CollectionView ItemsSource="{Binding TransactionHistory}"
                                  SelectionMode="None">
                    <CollectionView.ItemsLayout>
                        <LinearItemsLayout Orientation="Vertical"/>
                    </CollectionView.ItemsLayout>
                    <CollectionView.ItemTemplate>
                        <DataTemplate>
                            <Frame Padding="10" Margin="5">
                                <Grid ColumnDefinitions="*,*,*,*" RowDefinitions="Auto,Auto">
                                    <Label Grid.Row="0" Grid.Column="0" Text="{Binding Date, StringFormat={0:yyyy-MM-dd}}"/>
                                    <Label Grid.Row="0" Grid.Column="1" Text="{Binding Description}"/>
                                    <Label Grid.Row="0" Grid.Column="2" Text="{Binding Amount, StringFormat='{0:C2}'}"/>
                                    <Label Grid.Row="0" Grid.Column="3" Text="{Binding Type}"/>
                                    
                                    <Label Grid.Row="1" Grid.Column="0" Text="{Binding Category}"/>
                                    <Label Grid.Row="1" Grid.Column="1" Text="{Binding Reference}"/>
                                    <Label Grid.Row="1" Grid.Column="2" Text="{Binding RunningBalance, StringFormat='{0:C2}'}"/>
                                    <Label Grid.Row="1" Grid.Column="3" Text="{Binding Status}"/>
                                </Grid>
                            </Frame>
                        </DataTemplate>
                    </CollectionView.ItemTemplate>
                </CollectionView>
                
                <!-- Reconciliation -->
                <Label Text="Bank Reconciliation" FontSize="24" HorizontalOptions="Start"/>
                
                <Grid ColumnDefinitions="*,*" RowDefinitions="Auto,Auto,Auto,Auto,Auto">
                    <Label Grid.Row="0" Grid.Column="0" Text="Statement Date:"/>
                    <DatePicker Grid.Row="0" Grid.Column="1" Date="{Binding ReconciliationDate, Mode=TwoWay, StringFormat={0:yyyy-MM-dd}}"/>
                    
                    <Label Grid.Row="1" Grid.Column="0" Text="Statement Balance:"/>
                    <Entry Grid.Row="1" Grid.Column="1" Text="{Binding StatementBalance, Mode=TwoWay, StringFormat={}{0:C2}}"/>
                    
                    <Label Grid.Row="2" Grid.Column="0" Text="Variance Tolerance:"/>
                    <Entry Grid.Row="2" Grid.Column="1" Text="{Binding VarianceTolerance, Mode=TwoWay, StringFormat={}{0:C2}}"/>
                    
                    <Label Grid.Row="3" Grid.Column="0" Text="Reconciliation Notes:"/>
                    <Editor Grid.Row="3" Grid.Column="1" Text="{Binding ReconciliationNotes, Mode=TwoWay}"/>
                    
                    <Label Grid.Row="4" Grid.Column="0" Text="Reconciliation Result:"/>
                    <ContentView Grid.Row="4" Grid.Column="1" Content="{Binding ReconciliationResult}"/>
                </Grid>
                
                <Button Text="Perform Reconciliation" Clicked="OnPerformReconciliation" IsEnabled="{Binding SelectedAccountId > 0}"/>
                
                <!-- Financial Reports -->
                <Label Text="Financial Reports" FontSize="24" HorizontalOptions="Start"/>
                
                <Grid ColumnDefinitions="*,*" RowDefinitions="Auto,Auto,Auto">
                    <Label Grid.Row="0" Grid.Column="0" Text="Report Date:"/>
                    <DatePicker Grid.Row="0" Grid.Column="1" Date="{Binding ReportDate, Mode=TwoWay, StringFormat={0:yyyy-MM-dd}}"/>
                    
                    <Label Grid.Row="1" Grid.Column="0" Text="Report Type:"/>
                    <Picker Grid.Row="1" Grid.Column="1" ItemsSource="{Binding ReportTypes}" SelectedItem="{Binding SelectedReportType, Mode=TwoWay}"/>
                    
                    <Label Grid.Row="2" Grid.Column="0" Text="Department Filter:"/>
                    <Picker Grid.Row="2" Grid.Column="1" ItemsSource="{Binding Departments}" SelectedItem="{Binding SelectedDepartment, Mode=TwoWay}"/>
                </Grid>
                
                <HorizontalStackLayout Spacing="10" HorizontalOptions="Center">
                    <Button Text="Generate Report" Clicked="OnGenerateFinancialReport"/>
                    <Button Text="Export to PDF" Clicked="OnExportToPDF"/>
                    <Button Text="Export to Excel" Clicked="OnExportToExcel"/>
                    <Button Text="Print Report" Clicked="OnPrintReport"/>
                </HorizontalStackLayout>
                
                <!-- Report Viewer -->
                <Label Text="Report Viewer" FontSize="24" HorizontalOptions="Start"/>
                
                <ContentView Content="{Binding CurrentReportViewer}"/>
                
                <!-- Navigation -->
                <HorizontalStackLayout Spacing="10" HorizontalOptions="Center">
                    <Button Text="Back to Dashboard" Clicked="OnBackToDashboard"/>
                    <Button Text="View All Reports" Clicked="OnViewAllReports"/>
                    <Button Text="Schedule Report" Clicked="OnScheduleReport"/>
                </HorizontalStackLayout>
            </VerticalStackLayout>
        </ScrollView>
    </ContentPage.Content>
</ContentPage>