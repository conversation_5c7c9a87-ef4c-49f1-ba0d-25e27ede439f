<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:viewmodels="clr-namespace:CakeBistro.ViewModels"
             x:Class="CakeBistro.Views.SalesTransactionListPage">
    <ContentPage.BindingContext>
        <viewmodels:SalesTransactionListViewModel />
    </ContentPage.BindingContext>
    <ScrollView>
        <VerticalStackLayout Padding="16">
            <Label Text="Sales Transactions" FontSize="24" FontAttributes="Bold" />
            <Picker Title="Select Manifest" ItemsSource="{Binding Manifests}" ItemDisplayBinding="{Binding Destination}" SelectedItem="{Binding SelectedManifest, Mode=TwoWay}" />
            <Entry Placeholder="Product Name" Text="{Binding ProductName}" />
            <Entry Placeholder="Quantity" Keyboard="Numeric" Text="{Binding Quantity}" />
            <Entry Placeholder="Unit Price" Keyboard="Numeric" Text="{Binding UnitPrice}" />
            <Entry Placeholder="Notes" Text="{Binding Notes}" />
            <Button Text="Add Transaction" Command="{Binding AddTransactionCommand}" />
            <CollectionView ItemsSource="{Binding Transactions}" SelectionMode="Single" SelectedItem="{Binding SelectedTransaction, Mode=TwoWay}">
                <CollectionView.ItemTemplate>
                    <DataTemplate>
                        <Frame Margin="10" Padding="10" BackgroundColor="White" CornerRadius="8">
                            <StackLayout>
                                <Label Text="{Binding ProductName}" FontAttributes="Bold" FontSize="18" />
                                <Label Text="{Binding Date, StringFormat='Date: {0:yyyy-MM-dd}'}" />
                                <Label Text="{Binding ManifestId, StringFormat='Manifest ID: {0}'}" />
                                <Label Text="{Binding Quantity, StringFormat='Qty: {0}'}" />
                                <Label Text="{Binding UnitPrice, StringFormat='Unit Price: {0:C}'}" />
                                <Label Text="{Binding Total, StringFormat='Total: {0:C}'}" />
                                <Label Text="{Binding Notes}" />
                                <Button Text="Remove" Command="{Binding BindingContext.RemoveTransactionCommand, Source={x:Reference Name=SalesTransactionListPage}}" />
                            </StackLayout>
                        </Frame>
                    </DataTemplate>
                </CollectionView.ItemTemplate>
            </CollectionView>
        </VerticalStackLayout>
    </ScrollView>
</ContentPage>
