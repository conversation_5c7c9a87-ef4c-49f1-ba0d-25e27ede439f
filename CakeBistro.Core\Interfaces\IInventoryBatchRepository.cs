using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using CakeBistro.Core.Models;

namespace CakeBistro.Core.Interfaces
{
    public interface IInventoryBatchRepository
    {
        Task<IEnumerable<InventoryBatch>> GetAllAsync();
        Task<InventoryBatch> GetByIdAsync(Guid id);
        Task AddAsync(InventoryBatch batch);
        Task UpdateAsync(InventoryBatch batch);
        Task DeleteAsync(Guid id);
        Task<IEnumerable<InventoryBatch>> GetBatchesByMaterialAsync(Guid materialId);
        Task<IEnumerable<InventoryBatch>> GetActiveBatchesAsync();
        Task<IEnumerable<InventoryBatch>> GetExpiringBatchesAsync(DateTime thresholdDate);
        Task<decimal> GetTotalStockQuantityAsync(Guid materialId);
    }
}
