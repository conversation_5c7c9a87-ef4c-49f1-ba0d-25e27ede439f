using CakeBistro.Core.Interfaces;
using CakeBistro.Core.Models;
using CakeBistro.data;
using Microsoft.EntityFrameworkCore;

namespace CakeBistro.Services
{
    public class RawMaterialService : IRawMaterialService
    {
        private readonly CakeBistroContext _context;

        public RawMaterialService(CakeBistroContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<CakeBistro.Core.Models.RawMaterial>> GetAllRawMaterialsAsync()
        {
            return await _context.RawMaterials.ToListAsync();
        }

        public async Task<CakeBistro.Core.Models.RawMaterial> GetRawMaterialByIdAsync(int id)
        {
            return await _context.RawMaterials.FindAsync(id);
        }

        public async Task<CakeBistro.Core.Models.RawMaterial> AddRawMaterialAsync(CakeBistro.Core.Models.RawMaterial CakeBistro.Core.Models.RawMaterial)
        {
            CakeBistro.Core.Models.RawMaterial.CreatedDate = DateTime.UtcNow;
            CakeBistro.Core.Models.RawMaterial.UpdatedDate = DateTime.UtcNow;
            
            _context.RawMaterials.Add(CakeBistro.Core.Models.RawMaterial);
            await _context.SaveChangesAsync();
            return CakeBistro.Core.Models.RawMaterial;
        }

        public async Task UpdateRawMaterialAsync(CakeBistro.Core.Models.RawMaterial CakeBistro.Core.Models.RawMaterial)
        {
            CakeBistro.Core.Models.RawMaterial.UpdatedDate = DateTime.UtcNow;
            _context.RawMaterials.Update(CakeBistro.Core.Models.RawMaterial);
            await _context.SaveChangesAsync();
        }

        public async Task DeleteRawMaterialAsync(int id)
        {
            var CakeBistro.Core.Models.RawMaterial = await _context.RawMaterials.FindAsync(id);
            if (CakeBistro.Core.Models.RawMaterial != null)
            {
                _context.RawMaterials.Remove(CakeBistro.Core.Models.RawMaterial);
                await _context.SaveChangesAsync();
            }
        }

        public async Task<IEnumerable<CakeBistro.Core.Models.RawMaterial>> SearchRawMaterialsAsync(string searchTerm)
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
                return await GetAllRawMaterialsAsync();

            return await _context.RawMaterials
                .Where(rm => rm.Name.Contains(searchTerm) || rm.Description.Contains(searchTerm))
                .ToListAsync();
        }

        public async Task<IEnumerable<CakeBistro.Core.Models.RawMaterial>> GetLowStockRawMaterialsAsync()
        {
            return await _context.RawMaterials
                .Where(rm => rm.StockQuantity <= rm.MinimumStockLevel)
                .ToListAsync();
        }

        public async Task<bool> IsRawMaterialInUseAsync(int rawMaterialId)
        {
            // Check if raw material is referenced in any stock movements, purchase orders, etc.
            var hasStockMovements = await _context.StockMovements
                .AnyAsync(sm => sm.RawMaterialId == rawMaterialId);
            
            return hasStockMovements;
        }
    }
}
