<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:viewmodels="clr-namespace:CakeBistro.ViewModels"
             x:Class="CakeBistro.Views.PackingPage"
             x:Name="PackingPage">
    <ContentPage.BindingContext>
        <viewmodels:PackingViewModel />
    </ContentPage.BindingContext>
    <ScrollView>
        <VerticalStackLayout Padding="16" Spacing="12">
            <Label Text="Packing Sections" FontSize="24" FontAttributes="Bold" />
            <DatePicker Date="{Binding NewSectionDate, Mode=TwoWay}" />
            <Entry Placeholder="Section Name" Text="{Binding NewSectionName}" />
            <Entry Placeholder="Description" Text="{Binding NewSectionDescription}" />
            <Entry Placeholder="Responsible Staff" Text="{Binding NewSectionStaff}" />
            <Button Text="Create Section" Command="{Binding CreateSectionCommand}" />
            <CollectionView ItemsSource="{Binding PackingSections}" SelectionMode="Single" SelectedItem="{Binding SelectedSection, Mode=TwoWay}">
                <CollectionView.ItemTemplate>
                    <DataTemplate>
                        <Frame Margin="10" Padding="10" BackgroundColor="White" CornerRadius="8">
                            <StackLayout>
                                <Label Text="{Binding Name}" FontAttributes="Bold" FontSize="18" />
                                <Label Text="{Binding Description}" FontSize="14" />
                                <Label Text="{Binding Date, StringFormat='Date: {0:yyyy-MM-dd}'}" />
                                <Label Text="{Binding Staff}" />
                                <CollectionView ItemsSource="{Binding Items}" SelectionMode="Single" SelectedItem="{Binding BindingContext.SelectedItem, Source={x:Reference PackingPage}, Mode=TwoWay}">
                                    <CollectionView.ItemTemplate>
                                        <DataTemplate>
                                            <StackLayout Orientation="Horizontal" Spacing="10">
                                                <Label Text="{Binding Name}" />
                                                <Label Text="{Binding Quantity, StringFormat='Qty: {0}'}" />
                                                <Button Text="Remove" Command="{Binding BindingContext.RemoveItemCommand, Source={x:Reference PackingPage}}" CommandParameter="{Binding .}" />
                                                <Button Text="Adjust" Command="{Binding BindingContext.AdjustQuantityCommand, Source={x:Reference PackingPage}}" CommandParameter="{Binding Quantity}" />
                                            </StackLayout>
                                        </DataTemplate>
                                    </CollectionView.ItemTemplate>
                                </CollectionView>
                                <Entry Placeholder="Item Name" Text="{Binding BindingContext.NewItemName, Source={x:Reference PackingPage}}" />
                                <Entry Placeholder="Quantity" Keyboard="Numeric" Text="{Binding BindingContext.NewItemQuantity, Source={x:Reference PackingPage}}" />
                                <Button Text="Add Item" Command="{Binding BindingContext.AddItemCommand, Source={x:Reference PackingPage}}" />
                                <Label Text="{Binding Items.Count, StringFormat='Total Items: {0}'}" FontAttributes="Bold" />
                            </StackLayout>
                        </Frame>
                    </DataTemplate>
                </CollectionView.ItemTemplate>
            </CollectionView>
            <Label Text="{Binding StatusMessage}"
                   IsVisible="{Binding StatusMessage, Converter={StaticResource EmptyToFalseConverter}}"
                   TextColor="{Binding StatusColor}" />
        </VerticalStackLayout>
    </ScrollView>
</ContentPage>
