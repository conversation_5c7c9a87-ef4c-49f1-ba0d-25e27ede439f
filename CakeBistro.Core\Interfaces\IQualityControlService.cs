using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using CakeBistro.Core.Models;

namespace CakeBistro.Core.Interfaces
{
    public interface IQualityControlService
    {
        Task<QualityCheck> CreateCheckAsync(QualityCheck check);
        Task<QualityCheck> UpdateCheckAsync(QualityCheck check);
        Task<QualityCheck> GetCheckByIdAsync(int id);
        Task<IEnumerable<QualityCheck>> GetChecksByBatchAsync(int batchId);
        Task<IEnumerable<QualityCheck>> GetChecksByDateRangeAsync(DateTime startDate, DateTime endDate);
        Task<IEnumerable<QualityCheck>> GetFailedChecksAsync(DateTime? since = null);
        Task<IEnumerable<QualityCheck>> GetPendingChecksAsync();
        Task<bool> DeleteCheckAsync(int id);
        Task<bool> ValidateBatchQualityAsync(int batchId);
        Task<(bool IsValid, string Message)> ValidateQualityCheckAsync(QualityCheck check);
        Task<Dictionary<string, int>> GetQualityMetricsAsync(DateTime startDate, DateTime endDate);
        Task<bool> RequiresQualityCheckAsync(int productionBatchId, string checkType);
    }
}
