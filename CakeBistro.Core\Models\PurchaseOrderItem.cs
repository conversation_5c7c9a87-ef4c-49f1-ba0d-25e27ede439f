using System;

namespace CakeBistro.Core.Models
{
    public class PurchaseOrderItem
    {
        public int Id { get; set; }
        public int? PurchaseOrderId { get; set; }
        public int ProductId { get; set; }
        public int Quantity { get; set; }
        public decimal UnitPrice { get; set; }
        public int? RawMaterialId { get; set; } // Added RawMaterialId property
        
        public virtual PurchaseOrder? PurchaseOrder { get; set; }
        public virtual RawMaterial? RawMaterial { get; set; }
    }
}
