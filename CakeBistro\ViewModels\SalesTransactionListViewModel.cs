using CakeBistro.Models;
using CakeBistro.Services;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;

namespace CakeBistro.ViewModels
{
    public partial class SalesTransactionListViewModel : ObservableObject
    {
        private readonly SalesService _salesService;
        private readonly DeliveryManifestService _manifestService;
        [ObservableProperty]
        private ObservableCollection<DeliveryManifest> manifests = new();
        [ObservableProperty]
        private DeliveryManifest? selectedManifest;
        [ObservableProperty]
        private ObservableCollection<SalesTransaction> transactions = new();
        [ObservableProperty]
        private SalesTransaction? selectedTransaction;
        [ObservableProperty]
        private int manifestId;
        [ObservableProperty]
        private string? productName;
        [ObservableProperty]
        private int quantity;
        [ObservableProperty]
        private decimal unitPrice;
        [ObservableProperty]
        private string? notes;

        public SalesTransactionListViewModel(SalesService salesService, DeliveryManifestService manifestService)
        {
            _salesService = salesService;
            _manifestService = manifestService;
        }

        [RelayCommand]
        public async Task LoadManifestsAndTransactionsAsync()
        {
            Manifests = new ObservableCollection<DeliveryManifest>(await _manifestService.GetAllManifestsAsync());
            var list = await _salesService.GetAllTransactionsAsync();
            Transactions = new ObservableCollection<SalesTransaction>(list);
        }

        [RelayCommand]
        public async Task AddTransactionAsync()
        {
            if (SelectedManifest != null && !string.IsNullOrWhiteSpace(ProductName) && Quantity > 0 && UnitPrice > 0)
            {
                var tx = new SalesTransaction
                {
                    ManifestId = SelectedManifest.Id,
                    ProductName = ProductName!,
                    Quantity = Quantity,
                    UnitPrice = UnitPrice,
                    Notes = Notes
                };
                await _salesService.CreateTransactionAsync(tx);
                await LoadManifestsAndTransactionsAsync();
                SelectedManifest = null;
                ProductName = null;
                Quantity = 0;
                UnitPrice = 0;
                Notes = null;
            }
        }

        [RelayCommand]
        public async Task RemoveTransactionAsync()
        {
            if (SelectedTransaction != null)
            {
                await _salesService.RemoveTransactionAsync(SelectedTransaction.Id);
                await LoadManifestsAndTransactionsAsync();
            }
        }
    }
}
