namespace CakeBistro.Core.Models
{
    public class SalesOrderItem : BaseEntity
    {
        public int Id { get; set; }
        public int? SalesOrderId { get; set; }
        public int? ProductId { get; set; }
        public int Quantity { get; set; }
        public decimal UnitPrice { get; set; }
        public decimal TotalPrice { get; set; }

        public virtual SalesOrder? SalesOrder { get; set; }
        public virtual Product? Product { get; set; }
    }
}
