// SalesService.cs
using Microsoft.EntityFrameworkCore;
using CakeBistro.Core.Models;
using CakeBistro.Core.Interfaces;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace CakeBistro.Services
{
    /// <summary>
    /// Service class for managing sales-related operations, including
    /// creating sales orders, processing transactions, and generating reports.
    /// </summary>
    public class SalesService : ISalesService
    {
        private readonly CakeBistroContext _context;

        public SalesService(CakeBistroContext context)
        {
            _context = context;
        }

        /// <summary>
        /// Tracks items released from packing to the loading section.
        /// </summary>
        /// <param name="movement">The stock movement information.</param>
        /// <returns>The managed stock movement.</returns>
        public async Task<StockMovement> ManageLoadingBayAsync(StockMovement movement)
        {
            _context.StockMovements.Add(movement);
            await _context.SaveChangesAsync();
            return movement;
        }

        /// <summary>
        /// Registers a new vehicle and driver for a customer.
        /// </summary>
        /// <param name="customer">The customer information including vehicle and driver details.</param>
        /// <returns>The registered customer with vehicle and driver information.</returns>
        public async Task<Customer> RegisterVehicleDriverAsync(Customer customer)
        {
            _context.Customers.Add(customer);
            await _context.SaveChangesAsync();
            return customer;
        }

        /// <summary>
        /// Creates a new sales order, with validation and error handling.
        /// </summary>
        /// <param name="order">The sales order to create.</param>
        /// <returns>The created sales order with updated information.</returns>
        /// <exception cref="ArgumentNullException">Thrown when the order is null.</exception>
        /// <exception cref="ArgumentException">Thrown when the order has invalid data.</exception>
        /// <exception cref="InvalidOperationException">Thrown when there is insufficient stock for an item.</exception>
        /// <exception cref="ApplicationException">Thrown when there is an error creating the sales order.</exception>
        public async Task<SalesOrder> CreateSalesOrderAsync(SalesOrder order)
        {
            // Validate the order before creating it
            if (order == null)
                throw new ArgumentNullException(nameof(order), "Order cannot be null");

            if (order.CustomerId <= 0)
                throw new ArgumentException("Valid customer ID is required", nameof(order.CustomerId));

            if (order.Items == null || !order.Items.Any())
                throw new ArgumentException("At least one item is required for the order", nameof(order.Items));

            if (order.Items.Any(i => i.ProductId <= 0))
                throw new ArgumentException("Each item must have a valid product ID", nameof(order.Items));

            if (order.Items.Any(i => i.Quantity <= 0))
                throw new ArgumentException("Quantity must be greater than zero for all items", nameof(order.Items));

            if (order.Items.Any(i => i.UnitPrice < 0))
                throw new ArgumentException("Unit price cannot be negative", nameof(order.Items));

            if (order.TaxRate < 0 || order.TaxRate > 1)
                throw new ArgumentException("Tax rate must be between 0 and 1", nameof(order.TaxRate));

            if (order.Discount < 0 || order.Discount > 1)
                throw new ArgumentException("Discount must be between 0 and 1", nameof(order.Discount));

            if (order.FuelCost < 0)
                throw new ArgumentException("Fuel cost cannot be negative", nameof(order.FuelCost));

            try
            {
                // Set default values if not provided
                order.OrderDate ??= DateTime.Today;
                order.Status ??= "New";
                
                // Validate products exist and get their details
                var productIds = order.Items.Select(i => i.ProductId).Distinct();
                var products = await _productRepository.GetAllAsync();
                var availableProducts = products.ToDictionary(p => p.Id, p => p);
                
                foreach (var itemId in productIds)
                {
                    if (!availableProducts.ContainsKey(itemId))
                        throw new ArgumentException($"Product with ID {itemId} not found", nameof(order.Items));
                }
                
                // Check stock availability for all items
                foreach (var item in order.Items)
                {
                    var product = availableProducts[item.ProductId];
                    if (product.CurrentStock < item.Quantity)
                    {
                        throw new InvalidOperationException(
                            $"Insufficient stock for {product.Name}. Available: {product.CurrentStock}, Requested: {item.Quantity}");
                    }
                }
                
                // Calculate total amount including tax and discount
                order.SubTotal = order.Items.Sum(i => i.Quantity * i.UnitPrice);
                
                // Apply discount
                var discountAmount = order.SubTotal * order.Discount;
                var discountedTotal = order.SubTotal - discountAmount;
                
                // Apply tax
                var taxAmount = discountedTotal * order.TaxRate;
                
                // Add fuel cost
                order.TotalAmount = Math.Round(discountedTotal + taxAmount + order.FuelCost, 2);
                
                // Save the order
                var createdOrder = await _salesRepository.AddAsync(order);
                
                // If we're here, the order was successfully created
                return createdOrder;
            }
            catch (Exception ex)
            {
                // Log the error (in a real application)
                // For now, just return the error message
                throw new ApplicationException($"Error creating sales order: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Processes sales transactions, including returns, damages, exchanges, discounts, and fuel costs.
        /// </summary>
        /// <param name="transaction">The transaction details to process.</param>
        /// <returns>The result of the transaction processing.</returns>
        public async Task<TransactionResult> ProcessTransactionAsync(Transaction transaction)
        {
            if (transaction == null)
                throw new ArgumentNullException(nameof(transaction), "Transaction cannot be null");

            if (transaction.TransactionType == TransactionType.Unknown)
                throw new ArgumentException("Transaction type must be specified", nameof(transaction.TransactionType));

            if (transaction.Items == null || !transaction.Items.Any())
                throw new ArgumentException("At least one item is required for the transaction", nameof(transaction.Items));

            var result = new TransactionResult();

            try
            {
                // Handle different transaction types
                switch (transaction.TransactionType)
                {
                    case TransactionType.Return:
                        await ProcessReturnAsync(transaction, result);
                        break;
                    
                    case TransactionType.Damage:
                        await ProcessDamageAsync(transaction, result);
                        break;
                    
                    case TransactionType.Exchange:
                        await ProcessExchangeAsync(transaction, result);
                        break;
                    
                    case TransactionType.Discount:
                        await ProcessDiscountAsync(transaction, result);
                        break;
                    
                    case TransactionType.FuelCost:
                        await ProcessFuelCostAsync(transaction, result);
                        break;
                    
                    default:
                        throw new ArgumentException($"Unsupported transaction type: {transaction.TransactionType}", 
                            nameof(transaction.TransactionType));
                }

                // Save changes to database
                await _context.SaveChangesAsync();
                
                // Set success status
                result.Success = true;
                return result;
            }
            catch (Exception ex)
            {
                // Log error
                _logger.LogError(ex, $"Error processing {transaction.TransactionType} transaction");
                
                // Set failure status and error message
                result.Success = false;
                result.ErrorMessage = ex.Message;
                
                // Re-throw exception with context
                throw new ApplicationException($"Error processing {transaction.TransactionType} transaction: {ex.Message}", ex);
            }
        }

        private async Task ProcessReturnAsync(Transaction transaction, TransactionResult result)
        {
            // Validate return items
            foreach (var item in transaction.Items)
            {
                if (item.ProductId <= 0)
                    throw new ArgumentException("Valid product ID is required for all items", nameof(transaction.Items));

                if (item.Quantity <= 0)
                    throw new ArgumentException("Quantity must be greater than zero for all items", nameof(transaction.Items));
            }

            // Add inventory for returned items
            foreach (var item in transaction.Items)
            {
                var product = await _context.Products.FindAsync(item.ProductId);
                if (product == null)
                    throw new ArgumentException($"Product with ID {item.ProductId} not found", nameof(transaction.Items));

                // Increase inventory
                product.CurrentStock += item.Quantity;
                
                // Record inventory change
                var movement = new StockMovement
                {
                    ProductId = item.ProductId,
                    Quantity = item.Quantity,
                    MovementType = MovementType.Inbound,
                    Reason = $"Return from customer {transaction.CustomerId}",
                    Date = transaction.Date ?? DateTime.UtcNow,
                    UserId = transaction.UserId
                };

                _context.StockMovements.Add(movement);
                result.StockMovements.Add(movement);
            }

            // Create credit note
            var creditNote = new CreditNote
            {
                CustomerId = transaction.CustomerId,
                Amount = transaction.Items.Sum(i => i.Quantity * i.UnitPrice),
                IssueDate = transaction.Date ?? DateTime.UtcNow,
                Reason = transaction.Notes ?? "Customer Return",
                Status = "Pending"
            };

            _context.CreditNotes.Add(creditNote);
            result.CreditNote = creditNote;
            
            // Update customer account
            var customer = await _context.Customers.FindAsync(transaction.CustomerId);
            if (customer != null)
            {
                customer.AccountBalance -= creditNote.Amount;
                result.CustomerAccountBalance = customer.AccountBalance;
            }
        }

        private async Task ProcessDamageAsync(Transaction transaction, TransactionResult result)
        {
            // Validate damage items
            foreach (var item in transaction.Items)
            {
                if (item.ProductId <= 0)
                    throw new ArgumentException("Valid product ID is required for all items", nameof(transaction.Items));

                if (item.Quantity <= 0)
                    throw new ArgumentException("Quantity must be greater than zero for all items", nameof(transaction.Items));
            }

            // Record damage for each item
            foreach (var item in transaction.Items)
            {
                var product = await _context.Products.FindAsync(item.ProductId);
                if (product == null)
                    throw new ArgumentException($"Product with ID {item.ProductId} not found", nameof(transaction.Items));

                // Decrease inventory
                if (product.CurrentStock < item.Quantity)
                {
                    throw new InvalidOperationException(
                        $"Insufficient stock for {product.Name}. Available: {product.CurrentStock}, Requested: {item.Quantity}");
                }

                product.CurrentStock -= item.Quantity;
                
                // Record damage
                var damageRecord = new DamageRecord
                {
                    ProductId = item.ProductId,
                    Quantity = item.Quantity,
                    DamageDate = transaction.Date ?? DateTime.UtcNow,
                    Reason = transaction.Notes ?? "Unknown",
                    Location = transaction.Location ?? "Unknown",
                    ReportedBy = transaction.ReportedBy ?? "Unknown"
                };

                _context.DamageRecords.Add(damageRecord);
                result.DamageRecords.Add(damageRecord);
            }

            // Update inventory valuation
            result.InventoryValuation = await CalculateInventoryValuationAsync();
        }

        private async Task ProcessExchangeAsync(Transaction transaction, TransactionResult result)
        {
            // For simplicity, we assume exchange consists of return (old items) and sale (new items)
            // In a real system, this would be more complex with proper tracking
            
            // Process return part of exchange
            var returnTransaction = new Transaction
            {
                TransactionType = TransactionType.Return,
                CustomerId = transaction.CustomerId,
                Items = transaction.Items.Where(i => i.IsReturn).ToList(),
                Date = transaction.Date,
                Notes = $"Exchange - {transaction.Notes}",
                UserId = transaction.UserId
            };
            
            await ProcessReturnAsync(returnTransaction, result);
            
            // Process sale part of exchange
            var saleTransaction = new Transaction
            {
                TransactionType = TransactionType.Sale,
                CustomerId = transaction.CustomerId,
                Items = transaction.Items.Where(i => !i.IsReturn).ToList(),
                Date = transaction.Date,
                Notes = $"Exchange - {transaction.Notes}",
                UserId = transaction.UserId
            };
            
            await ProcessSaleAsync(saleTransaction, result);
        }

        private async Task ProcessDiscountAsync(Transaction transaction, TransactionResult result)
        {
            // Validate discount applies to existing sales
            if (string.IsNullOrWhiteSpace(transaction.ReferenceNumber))
                throw new ArgumentException("Reference number is required for discounts", nameof(transaction.ReferenceNumber));

            // Find original sale
            var originalSale = await _context.SalesOrders
                .Include(o => o.Items)
                .FirstOrDefaultAsync(o => o.OrderNumber == transaction.ReferenceNumber);

            if (originalSale == null)
                throw new ArgumentException($"Original sale with order number {transaction.ReferenceNumber} not found", nameof(transaction.ReferenceNumber));

            // Apply discount
            decimal totalDiscount = 0;
            foreach (var item in transaction.Items)
            {
                var originalItem = originalSale.Items.FirstOrDefault(i => i.ProductId == item.ProductId);
                if (originalItem == null)
                    throw new ArgumentException($"Product {item.ProductId} not found in original sale", nameof(transaction.Items));

                // Calculate discount amount
                decimal discountAmount = 0;
                if (item.DiscountType == DiscountType.Percentage)
                {
                    discountAmount = originalItem.UnitPrice * originalItem.Quantity * item.DiscountValue / 100;
                }
                else if (item.DiscountType == DiscountType.Fixed)
                {
                    discountAmount = item.DiscountValue;
                }
                
                // Apply discount to sale
                originalItem.DiscountAmount += discountAmount;
                totalDiscount += discountAmount;
            }
            
            // Update sale total
            originalSale.DiscountAmount += totalDiscount;
            originalSale.TotalAmount -= totalDiscount;
            
            result.AppliedDiscount = totalDiscount;
            result.UpdatedTotal = originalSale.TotalAmount;
        }

        private async Task ProcessFuelCostAsync(Transaction transaction, TransactionResult result)
        {
            // Validate fuel cost
            if (transaction.VehicleId <= 0)
                throw new ArgumentException("Vehicle ID is required for fuel cost transactions", nameof(transaction.VehicleId));

            if (transaction.Amount <= 0)
                throw new ArgumentException("Fuel cost amount must be greater than zero", nameof(transaction.Amount));

            // Create fuel cost record
            var fuelCost = new FuelCost
            {
                VehicleId = transaction.VehicleId,
                CostDate = transaction.Date ?? DateTime.UtcNow,
                Amount = transaction.Amount,
                Description = transaction.Notes ?? "Fuel Cost",
                CreatedBy = transaction.UserId
            };

            _context.FuelCosts.Add(fuelCost);
            result.FuelCost = fuelCost;
            
            // Update vehicle maintenance records
            var vehicle = await _context.Vehicles.FindAsync(transaction.VehicleId);
            if (vehicle != null)
            {
                vehicle.LastFuelCost = fuelCost.Amount;
                vehicle.LastFuelDate = fuelCost.CostDate;
            }
        }

        private async Task ProcessSaleAsync(Transaction transaction, TransactionResult result)
        {
            // Similar to CreateSalesOrder but simplified for exchange scenario
            var order = new SalesOrder
            {
                CustomerId = transaction.CustomerId,
                Items = transaction.Items.Select(i => new SalesOrderItem
                {
                    ProductId = i.ProductId,
                    Quantity = i.Quantity,
                    UnitPrice = i.UnitPrice,
                    DiscountAmount = i.DiscountAmount
                }).ToList(),
                TaxRate = transaction.TaxRate,
                Discount = transaction.Discount,
                FuelCost = transaction.FuelCost,
                OrderDate = transaction.Date ?? DateTime.UtcNow,
                Status = "Completed",
                UserId = transaction.UserId
            };
            
            // Calculate totals
            order.SubTotal = order.Items.Sum(i => i.Quantity * i.UnitPrice);
            
            // Apply discount
            var discountAmount = order.SubTotal * order.Discount;
            var discountedTotal = order.SubTotal - discountAmount;
            
            // Apply tax
            var taxAmount = discountedTotal * order.TaxRate;
            
            // Add fuel cost
            order.TotalAmount = Math.Round(discountedTotal + taxAmount + order.FuelCost, 2);
            
            // Check stock availability
            foreach (var item in order.Items)
            {
                var product = await _context.Products.FindAsync(item.ProductId);
                if (product == null)
                    throw new ArgumentException($"Product with ID {item.ProductId} not found", nameof(transaction.Items));

                if (product.CurrentStock < item.Quantity)
                {
                    throw new InvalidOperationException(
                        $"Insufficient stock for {product.Name}. Available: {product.CurrentStock}, Requested: {item.Quantity}");
                }
            }
            
            // Deduct inventory
            foreach (var item in order.Items)
            {
                var product = await _context.Products.FindAsync(item.ProductId);
                if (product != null)
                {
                    product.CurrentStock -= item.Quantity;
                    
                    // Record inventory movement
                    var movement = new StockMovement
                    {
                        ProductId = item.ProductId,
                        Quantity = item.Quantity,
                        MovementType = MovementType.Outbound,
                        Reason = $"Sale to customer {order.CustomerId}",
                        Date = order.OrderDate,
                        UserId = order.UserId
                    };

                    _context.StockMovements.Add(movement);
                    result.StockMovements.Add(movement);
                }
            }
            
            // Save the order
            _context.SalesOrders.Add(order);
            result.SalesOrder = order;
        }

        private async Task<decimal> CalculateInventoryValuationAsync()
        {
            var products = await _context.Products.ToListAsync();
            return products.Sum(p => p.CurrentStock * p.CostPrice);
        }

        /// <summary>
        /// Reconciles the cashier's transactions for a given date with the expected sales.
        /// </summary>
        /// <param name="cashierId">The ID of the cashier to reconcile.</param>
        /// <param name="date">The date of the transactions to reconcile.</param>
        /// <returns>The result of the cashier reconciliation.</returns>
        public async Task<CashierReconciliationResult> ReconcileCashierAsync(int cashierId, DateTime date)
        {
            if (cashierId <= 0)
                throw new ArgumentException("Valid cashier ID is required", nameof(cashierId));

            // Get all sales and payments for the cashier on the specified date
            var startDate = date.Date;
            var endDate = startDate.AddDays(1);
            
            // Get sales made by the cashier
            var sales = await _context.SalesOrders
                .Where(o => o.UserId == cashierId && o.OrderDate >= startDate && o.OrderDate < endDate)
                .Include(o => o.Items)
                .ToListAsync();
            
            // Get payments received by the cashier
            var payments = await _context.Payments
                .Where(p => p.UserId == cashierId && p.PaymentDate >= startDate && p.PaymentDate < endDate)
                .ToListAsync();
            
            // Get returns processed by the cashier
            var returns = await _context.CreditNotes
                .Where(r => r.CreatedBy == cashierId && r.IssueDate >= startDate && r.IssueDate < endDate)
                .ToListAsync();
            
            // Calculate totals
            var result = new CashierReconciliationResult
            {
                CashierId = cashierId,
                ReconciliationDate = date,
                ExpectedSalesTotal = sales.Sum(s => s.TotalAmount),
                ActualPaymentsTotal = payments.Sum(p => p.Amount),
                ReturnsTotal = returns.Sum(r => r.Amount),
                ExpectedNetSales = sales.Sum(s => s.TotalAmount) - returns.Sum(r => r.Amount)
            };
            
            // Calculate variance
            result.Variance = result.ActualPaymentsTotal - result.ExpectedNetSales;
            result.IsBalanced = Math.Abs(result.Variance) < 0.01m; // Allow small rounding differences
            
            // If there's a variance, get detailed information
            if (!result.IsBalanced)
            {
                result.UnmatchedTransactions = await FindUnmatchedTransactionsAsync(cashierId, date);
                result.TransactionDifferences = await FindTransactionDifferencesAsync(cashierId, date);
            }
            
            return result;
        }
        
        private async Task<List<UnmatchedTransaction>> FindUnmatchedTransactionsAsync(int cashierId, DateTime date)
        {
            var startDate = date.Date;
            var endDate = startDate.AddDays(1);
            
            // Find sales without corresponding payments
            var unmatchedSales = await _context.SalesOrders
                .Where(o => o.UserId == cashierId && !o.PaymentReceived && 
                            o.OrderDate >= startDate && o.OrderDate < endDate)
                .Select(o => new UnmatchedTransaction
                {
                    TransactionType = "Sale",
                    TransactionId = o.Id,
                    Amount = o.TotalAmount,
                    Description = $"Unpaid sale {o.OrderNumber}"
                })
                .ToListAsync();
            
            // Find payments without corresponding sales
            var unmatchedPayments = await _context.Payments
                .Where(p => p.UserId == cashierId && p.PaymentDate >= startDate && p.PaymentDate < endDate && 
                            !_context.SalesOrders.Any(o => o.Id == p.SalesOrderId))
                .Select(p => new UnmatchedTransaction
                {
                    TransactionType = "Payment",
                    TransactionId = p.Id,
                    Amount = p.Amount,
                    Description = $"Payment without matching sale {p.PaymentReference}"
                })
                .ToListAsync();
            
            // Find credit notes without corresponding returns
            var unmatchedReturns = await _context.CreditNotes
                .Where(r => r.CreatedBy == cashierId && r.IssueDate >= startDate && r.IssueDate < endDate && !r.AppliedToOrder)
                .Select(r => new UnmatchedTransaction
                {
                    TransactionType = "Return",
                    TransactionId = r.Id,
                    Amount = r.Amount,
                    Description = $"Credit note not applied {r.ReferenceNumber}"
                })
                .ToListAsync();
            
            return unmatchedSales.Concat(unmatchedPayments).Concat(unmatchedReturns).ToList();
        }
        
        private async Task<List<TransactionDifference>> FindTransactionDifferencesAsync(int cashierId, DateTime date)
        {
            var startDate = date.Date;
            var endDate = startDate.AddDays(1);
            
            // Find sales where payment amount doesn't match
            var salesWithPaymentIssues = await _context.SalesOrders
                .Where(o => o.UserId == cashierId && o.PaymentReceived && 
                            o.OrderDate >= startDate && o.OrderDate < endDate)
                .Join(_context.Payments, 
                    o => o.Id, 
                    p => p.SalesOrderId ?? 0, 
                    (sale, payment) => new { Sale = sale, Payment = payment })
                .Where(sp => sp.Sale.TotalAmount != sp.Payment.Amount)
                .Select(sp => new TransactionDifference
                {
                    TransactionType = "Sale/Payment",
                    TransactionId = sp.Sale.Id,
                    ExpectedAmount = sp.Sale.TotalAmount,
                    ActualAmount = sp.Payment.Amount,
                    Difference = sp.Sale.TotalAmount - sp.Payment.Amount,
                    Description = $"Mismatch between sale {sp.Sale.OrderNumber} and payment {sp.Payment.PaymentReference}"
                })
                .ToListAsync();
            
            // Find partial payments that don't add up to total sale amount
            var partialPayments = await _context.Payments
                .Where(p => p.UserId == cashierId && p.PaymentDate >= startDate && p.PaymentDate < endDate)
                .GroupBy(p => p.SalesOrderId)
                .Where(g => g.Key.HasValue && g.Sum(p => p.Amount) != g.First().Sale?.TotalAmount)
                .SelectMany(g => g, (g, p) => new TransactionDifference
                {
                    TransactionType = "Partial Payment",
                    TransactionId = p.Id,
                    ExpectedAmount = g.Key.HasValue ? g.First().Sale?.TotalAmount ?? 0 : 0,
                    ActualAmount = g.Sum(pay => pay.Amount),
                    Difference = g.Key.HasValue ? (g.First().Sale?.TotalAmount ?? 0 - g.Sum(pay => pay.Amount)) : 0,
                    Description = $"Partial payments for sale {g.Key} do not add up to total amount"
                })
                .ToListAsync();
            
            return salesWithPaymentIssues.Concat(partialPayments).ToList();
        }

        /// <summary>
        /// Generates a sales report for a specified date range.
        /// </summary>
        /// <param name="startDate">The start date for the report.</param>
        /// <param name="endDate">The end date for the report (exclusive).</param>
        /// <returns>A sales report containing aggregated sales data.</returns>
        public async Task<SalesReport> GenerateSalesReportAsync(DateTime startDate, DateTime endDate)
        {
            if (endDate <= startDate)
                throw new ArgumentException("End date must be after start date", nameof(endDate));

            // Adjust dates to include entire day
            var adjustedStartDate = startDate.Date;
            var adjustedEndDate = endDate.Date;

            // Get all sales within the date range
            var sales = await _context.SalesOrders
                .Where(o => o.OrderDate >= adjustedStartDate && o.OrderDate < adjustedEndDate)
                .Include(o => o.Items)
                .Include(o => o.Customer)
                .ToListAsync();

            // Get all payments within the date range
            var payments = await _context.Payments
                .Where(p => p.PaymentDate >= adjustedStartDate && p.PaymentDate < adjustedEndDate)
                .ToListAsync();

            // Get all returns (credit notes) within the date range
            var returns = await _context.CreditNotes
                .Where(r => r.IssueDate >= adjustedStartDate && r.IssueDate < adjustedEndDate)
                .ToListAsync();

            // Create report
            var report = new SalesReport
            {
                ReportStartDate = adjustedStartDate,
                ReportEndDate = adjustedEndDate.AddDays(-1), // Show as inclusive
                TotalTransactions = sales.Count,
                TotalReturns = returns.Count,
                TotalCustomers = sales.Select(o => o.CustomerId).Distinct().Count(),
                TotalProductsSold = sales.Sum(o => o.Items.Sum(i => i.Quantity)),
                GrossSalesTotal = sales.Sum(o => o.SubTotal),
                DiscountsTotal = sales.Sum(o => o.DiscountAmount),
                NetSalesBeforeTax = sales.Sum(o => o.SubTotal) - sales.Sum(o => o.DiscountAmount),
                TaxTotal = sales.Sum(o => o.TaxAmount),
                NetSalesAfterTax = sales.Sum(o => o.TotalAmount),
                PaymentsReceived = payments.Sum(p => p.Amount),
                ReturnsTotal = returns.Sum(r => r.Amount),
                NetRevenue = payments.Sum(p => p.Amount) - returns.Sum(r => r.Amount),
                SalesByPaymentMethod = new Dictionary<string, decimal>(),
                SalesByProduct = new Dictionary<int, ProductSalesSummary>(),
                SalesByCustomer = new Dictionary<int, CustomerSalesSummary>(),
                SalesByCashier = new Dictionary<int, CashierSalesSummary>(),
                DailySalesTrend = new Dictionary<DateTime, DailySalesSummary>()
            };

            // Calculate sales by payment method
            var salesByPaymentMethod = sales
                .GroupBy(o => o.PaymentMethod ?? "Unspecified")
                .ToDictionary(g => g.Key, g => new PaymentMethodSalesSummary
                {
                    TransactionCount = g.Count(),
                    GrossSalesTotal = g.Sum(o => o.SubTotal),
                    DiscountTotal = g.Sum(o => o.DiscountAmount),
                    TaxTotal = g.Sum(o => o.TaxAmount),
                    NetSalesTotal = g.Sum(o => o.TotalAmount)
                });

            report.SalesByPaymentMethod = salesByPaymentMethod;

            // Calculate sales by product
            var salesByProduct = sales
                .SelectMany(o => o.Items.Select(i => new { Item = i, Order = o }))
                .GroupBy(x => x.Item.ProductId)
                .ToDictionary(
                    g => g.Key,
                    g => new ProductSalesSummary
                    {
                        ProductId = g.Key,
                        TransactionCount = g.Count(),
                        QuantitySold = g.Sum(x => x.Item.Quantity),
                        GrossSalesTotal = g.Sum(x => x.Item.Quantity * x.Item.UnitPrice),
                        DiscountTotal = g.Sum(x => x.Item.DiscountAmount),
                        TaxTotal = g.Sum(x => x.Order.TaxRate * (x.Item.Quantity * x.Item.UnitPrice - x.Item.DiscountAmount)),
                        NetSalesTotal = g.Sum(x => x.Item.Quantity * x.Item.UnitPrice - x.Item.DiscountAmount + 
                            x.Order.TaxRate * (x.Item.Quantity * x.Item.UnitPrice - x.Item.DiscountAmount))
                    });

            report.SalesByProduct = salesByProduct;

            // Calculate sales by customer
            var salesByCustomer = sales
                .GroupBy(o => o.CustomerId)
                .ToDictionary(
                    g => g.Key,
                    g => new CustomerSalesSummary
                    {
                        CustomerId = g.Key,
                        CustomerName = g.First().Customer?.Name ?? "Unknown",
                        TransactionCount = g.Count(),
                        ItemsSold = g.Sum(o => o.Items.Sum(i => i.Quantity)),
                        GrossSalesTotal = g.Sum(o => o.SubTotal),
                        DiscountTotal = g.Sum(o => o.DiscountAmount),
                        TaxTotal = g.Sum(o => o.TaxAmount),
                        NetSalesTotal = g.Sum(o => o.TotalAmount)
                    });

            report.SalesByCustomer = salesByCustomer;

            // Calculate sales by cashier
            var salesByCashier = sales
                .GroupBy(o => o.UserId)
                .ToDictionary(
                    g => g.Key,
                    g => new CashierSalesSummary
                    {
                        CashierId = g.Key,
                        TransactionCount = g.Count(),
                        ItemsSold = g.Sum(o => o.Items.Sum(i => i.Quantity)),
                        GrossSalesTotal = g.Sum(o => o.SubTotal),
                        DiscountTotal = g.Sum(o => o.DiscountAmount),
                        TaxTotal = g.Sum(o => o.TaxAmount),
                        NetSalesTotal = g.Sum(o => o.TotalAmount)
                    });

            report.SalesByCashier = salesByCashier;

            // Calculate daily sales trend
            var dailySalesTrend = sales
                .GroupBy(o => o.OrderDate.Date)
                .ToDictionary(
                    g => g.Key,
                    g => new DailySalesSummary
                    {
                        Date = g.Key,
                        TransactionCount = g.Count(),
                        ItemsSold = g.Sum(o => o.Items.Sum(i => i.Quantity)),
                        GrossSalesTotal = g.Sum(o => o.SubTotal),
                        DiscountTotal = g.Sum(o => o.DiscountAmount),
                        TaxTotal = g.Sum(o => o.TaxAmount),
                        NetSalesTotal = g.Sum(o => o.TotalAmount)
                    });

            report.DailySalesTrend = dailySalesTrend;

            return report;
        }

        /// <summary>
        /// Manages the delivery of sales orders, including scheduling and tracking.
        /// </summary>
        /// <param name="delivery">The delivery details to manage.</param>
        /// <returns>The result of the delivery management.</returns>
        public async Task<DeliveryResult> ManageDeliveryAsync(Delivery delivery)
        {
            // TODO: Implement delivery management logic
            return new DeliveryResult();
        }

        /// <summary>
        /// Registers a new vehicle or driver, or updates existing information.
        /// </summary>
        /// <param name="vehicle">The vehicle information to register.</param>
        /// <param name="driver">The driver information to register.</param>
        /// <returns>The result of the vehicle or driver registration.</returns>
        public async Task<VehicleRegistrationResult> RegisterVehicleOrDriverAsync(Vehicle vehicle, Driver driver)
        {
            // TODO: Implement vehicle/driver registration logic
            return new VehicleRegistrationResult();
        }

        // Additional get methods for UI display
        public async Task<List<Customer>> GetCustomersAsync()
        {
            return await _context.Customers.ToListAsync();
        }

        public async Task<List<SalesOrder>> GetAllSalesOrdersAsync()
        {
            return await _context.SalesOrders.ToListAsync();
        }
    }
}
