using CakeBistro.ViewModels;
using CakeBistro.Core.Models;
using Microsoft.Maui.Controls;

namespace CakeBistro.Views
{
    public partial class ReportPage : ContentPage
    {
        private readonly ReportViewModel _viewModel;
        
        public ReportPage(ReportViewModel viewModel)
        {
            InitializeComponent();
            _viewModel = viewModel;
            BindingContext = _viewModel;
        }
        
        protected override void OnAppearing()
        {
            base.OnAppearing();
            // Initialize or refresh data when page appears
            _viewModel.RefreshCommand.Execute(null);
        }
    }
}
