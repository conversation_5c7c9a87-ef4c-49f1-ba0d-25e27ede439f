<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2022/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="CakeBistro.Views.SalesAnalysisPage"
             Title="Sales Analysis">
    <ContentPage.Content>
        <VerticalStackLayout Spacing="20" Padding="20">
            <Label Text="Sales Analysis" FontSize="24"/>
            <HorizontalStackLayout>
                <Label Text="Start Date:"/>
                <DatePicker Date="{Binding StartDate, Mode=TwoWay}"/>
                <Label Text="End Date:"/>
                <DatePicker Date="{Binding EndDate, Mode=TwoWay}"/>
                <Button Text="Load Summary" Command="{Binding LoadSalesSummaryCommand}"/>
            </HorizontalStackLayout>
            <Label Text="Total Sales:"/>
            <Label Text="{Binding TotalSales, StringFormat='Total: {0:C2}'}" FontSize="18"/>
            <Label Text="Order Count:"/>
            <Label Text="{Binding OrderCount}"/>
            <Label Text="Average Order Value:"/>
            <Label Text="{Binding AverageOrderValue, StringFormat='{0:C2}'}"/>
            <Label Text="Top Selling Product:"/>
            <Label Text="{Binding TopSellingProduct}"/>
            <Button Text="Export to PDF" Command="{Binding ExportSalesSummaryCommand}" CommandParameter="pdf"/>
            <Button Text="Export to Excel" Command="{Binding ExportSalesSummaryCommand}" CommandParameter="excel"/>
            <Label Text="{Binding ExportResultMessage}" TextColor="Green"/>
            <Label Text="{Binding StatusMessage}"
                   IsVisible="{Binding StatusMessage, Converter={StaticResource EmptyToFalseConverter}}"
                   TextColor="{Binding StatusColor}" />
        </VerticalStackLayout>
    </ContentPage.Content>
</ContentPage>
