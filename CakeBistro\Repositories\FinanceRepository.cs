using CakeBistro.Core.Models;
using CakeBistro.Core.Interfaces;
using Microsoft.EntityFrameworkCore;
using System.Linq.Expressions;

namespace CakeBistro.Repositories
{
    public class FinanceRepository : BaseRepository<BankAccount>, IFinanceRepository
    {
        public FinanceRepository(CakeBistroContext context) : base(context)
        {
        }

        public async Task<IEnumerable<Transaction>> GetTransactionsByAccountAsync(int accountId, DateTime startDate, DateTime endDate)
        {
            return await _context.Transactions
                .Where(t => t.AccountId == accountId && t.Date >= startDate && t.Date <= endDate)
                .ToListAsync();
        }

        public async Task ReconcileAccountAsync(int accountId, DateTime statementDate, decimal statementBalance)
        {
            var account = await _context.BankAccounts.FindAsync(accountId);
            if (account != null)
            {
                account.LastReconciled = statementDate;
                account.ReconciledBalance = statementBalance;
                await _context.SaveChangesAsync();
            }
        }
    }
}
