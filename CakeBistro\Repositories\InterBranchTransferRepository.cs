using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using CakeBistro.Core.Models;
using CakeBistro.Repositories;

namespace CakeBistro.Repositories
{
    // Implementation of inter-branch transfer repository with inventory-specific data operations
    public class InterBranchTransferRepository : RepositoryBase<InterBranchTransfer>, IInterBranchTransferRepository
    {
        private readonly InventoryContext _context;
        
        public InterBranchTransferRepository(InventoryContext context)
        {
            _context = context;
        }
        
        public override async Task<IEnumerable<InterBranchTransfer>> GetAllAsync()
        {
            return await _context.InterBranchTransfers.ToListAsync();
        }
        
        public override async Task<InterBranchTransfer> GetByIdAsync(Guid id)
        {
            return await _context.InterBranchTransfers.FindAsync(id);
        }
        
        public override async Task AddAsync(InterBranchTransfer transfer)
        {
            await _context.InterBranchTransfers.AddAsync(transfer);
            await _context.SaveChangesAsync();
        }
        
        public override async Task UpdateAsync(InterBranchTransfer transfer)
        {
            _context.InterBranchTransfers.Update(transfer);
            await _context.SaveChangesAsync();
        }
        
        public override async Task DeleteAsync(Guid id)
        {
            var transfer = await _context.InterBranchTransfers.FindAsync(id);
            if (transfer != null)
            {
                _context.InterBranchTransfers.Remove(transfer);
                await _context.SaveChangesAsync();
            }
        }
        
        public async Task<IEnumerable<InterBranchTransfer>> GetInterBranchTransfersAsync(Guid materialId)
        {
            return await _context.InterBranchTransfers
                .Where(t => t.RawMaterialId == materialId)
                .ToListAsync();
        }
        
        public async Task<IEnumerable<InterBranchTransfer>> GetTransfersByStatusAsync(TransferStatus status)
        {
            return await _context.InterBranchTransfers
                .Where(t => t.Status == status)
                .ToListAsync();
        }
        
        public async Task<IEnumerable<InterBranchTransfer>> GetTransfersByBranchAsync(Guid branchId)
        {
            return await _context.InterBranchTransfers
                .Where(t => t.FromBranchId == branchId || t.ToBranchId == branchId)
                .ToListAsync();
        }
        
        public async Task<decimal> GetTotalTransferredQuantityAsync(Guid materialId)
        {
            var transfers = await _context.InterBranchTransfers
                .Where(t => t.RawMaterialId == materialId && t.Status == TransferStatus.Completed)
                .ToListAsync();
            
            return transfers.Sum(t => t.Quantity);
        }
    }
}
