using CakeBistro.Services;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using System;

namespace CakeBistro.HealthCheck
{
    public static class HealthCheckMiddlewareExtensions
    {
        /// <summary>
        /// Adds a health check dashboard middleware to the application pipeline.
        /// </summary>
        /// <param name="app">The IApplicationBuilder instance.</param>
        /// <returns>The IApplicationBuilder instance.</returns>
        public static IApplicationBuilder UseHealthCheckDashboard(this IApplicationBuilder app)
        {
            if (app == null)
            {
                throw new ArgumentNullException(nameof(app));
            }

            // Register the health check dashboard endpoint
            app.UseEndpoints(endpoints =>
            {
                endpoints.MapGet("/health", async context =>
                {
                    var dashboardService = context.RequestServices.GetRequiredService<HealthCheckDashboardService>();
                    await dashboardService.WriteHealthReportResponseAsync(context);
                });
                
                // Add health check UI endpoint
                endpoints.MapHealthChecksUI();
            });

            return app;
        }
    }
}