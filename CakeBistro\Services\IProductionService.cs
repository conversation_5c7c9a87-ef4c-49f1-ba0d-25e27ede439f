
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using CakeBistro.Models;

namespace CakeBistro.Services
{
    /// <summary>
    /// Interface defining methods for production control operations as specified in PRD 4.2
    /// </summary>
    public interface IProductionService
    {
        /// <summary>
        /// Calculates the cost of production for a finished product based on raw materials and labor costs.
        /// Implements PRD 4.2 requirement for accurate cost calculation.
        /// </summary>
        /// <param name="productId">The unique identifier of the product</param>
        /// <returns>The calculated production cost</returns>
        /// <exception cref="BusinessRuleValidationException">
        /// Thrown when validation fails:
        /// - Product ID is not valid (ruleName: "InvalidProductID")
        /// - Product not found (ruleName: "ProductNotFound")
        /// </exception>
        Task<decimal> CalculateProductionCostAsync(int productId);

        /// <summary>
        /// Analyzes profitability for a product by comparing production cost with sale price.
        /// Implements PRD 4.2 requirement for profitability analysis.
        /// </summary>
        /// <param name="productId">The unique identifier of the product</param>
        /// <returns>A ProfitabilityAnalysis object containing detailed analysis</returns>
        /// <exception cref="BusinessRuleValidationException">
        /// Thrown when validation fails:
        /// - Product ID is not valid (ruleName: "InvalidProductID")
        /// - Product not found (ruleName: "ProductNotFound")
        /// </exception>
        Task<ProfitabilityAnalysis> AnalyzeProfitabilityAsync(int productId);

        /// <summary>
        /// Registers damaged goods against specific batches with classification of damage reasons.
        /// Implements PRD 4.2 requirement for effective damage tracking and management.
        /// </summary>
        /// <param name="damageReport">The damage report containing details about the damage</param>
        /// <returns>True if the damage was successfully registered, false otherwise</returns>
        /// <exception cref="BusinessRuleValidationException">
        /// Thrown when validation fails:
        /// - Damage report is null (ruleName: "DamageReportNullCheck")
        /// - Batch ID is not valid (ruleName: "InvalidBatchID")
        /// - Quantity is not positive (ruleName: "QuantityMustBePositive")
        /// </exception>
        Task<bool> RegisterDamageAsync(DamageReport damageReport);

        /// <summary>
        /// Automatically updates finished product stock when a production batch is completed.
        /// Implements PRD 4.2 requirement for one-click stock updates.
        /// </summary>
        /// <param name="batchId">The unique identifier of the production batch</param>
        /// <returns>True if the stock was successfully updated, false otherwise</returns>
        /// <exception cref="BusinessRuleValidationException">
        /// Thrown when validation fails:
        /// - Batch ID is not valid (ruleName: "InvalidBatchID")
        /// - Batch not found (ruleName: "BatchNotFound")
        /// </exception>
        Task<bool> UpdateFinishedStockAsync(int batchId);

        /// <summary>
        /// Transfers finished product stock from Production to the Packing department.
        /// Implements PRD 4.2 requirement for inter-departmental transfers.
        /// </summary>
        /// <param name="transferRequest">The transfer request containing transfer details</param>
        /// <returns>The created transfer record</returns>
        /// <exception cref="BusinessRuleValidationException">
        /// Thrown when validation fails:
        /// - Transfer request is null (ruleName: "TransferRequestNullCheck")
        /// - Product ID is not valid (ruleName: "InvalidProductID")
        /// - Quantity is not positive (ruleName: "QuantityMustBePositive")
        /// </exception>
        Task<InterBranchTransfer> TransferToPackingAsync(TransferRequest transferRequest);
    }

    /// <summary>
    /// Represents a damage report for tracking damaged goods
    /// </summary>
    public class DamageReport
    {
        /// <summary>
        /// Gets or sets the ID of the batch where damage occurred
        /// </summary>
        public int BatchId { get; set; }
        
        /// <summary>
        /// Gets or sets the quantity of damaged items
        /// </summary>
        public int Quantity { get; set; }
        
        /// <summary>
        /// Gets or sets the reason for the damage
        /// </summary>
        public string Reason { get; set; }
        
        /// <summary>
        /// Gets or sets the date when damage was reported
        /// </summary>
        public DateTime ReportDate { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// Represents the result of a profitability analysis
    /// </summary>
    public class ProfitabilityAnalysis
    {
        /// <summary>
        /// Gets or sets the ID of the product being analyzed
        /// </summary>
        public int ProductId { get; set; }
        
        /// <summary>
        /// Gets or sets the calculated production cost
        /// </summary>
        public decimal ProductionCost { get; set; }
        
        /// <summary>
        /// Gets or sets the current sale price of the product
        /// </summary>
        public decimal SalePrice { get; set; }
        
        /// <summary>
        /// Gets or sets the profit margin calculated as percentage
        /// </summary>
        public decimal ProfitMarginPercentage { get; set; }
        
        /// <summary>
        /// Gets or sets the absolute profit amount
        /// </summary>
        public decimal AbsoluteProfit => SalePrice - ProductionCost;
        
        /// <summary>
        /// Gets or sets the date when the analysis was performed
        /// </summary>
        public DateTime AnalysisDate { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// Represents a request to transfer items from production to packing
    /// </summary>
    public class TransferRequest
    {
        /// <summary>
        /// Gets or sets the ID of the product to transfer
        /// </summary>
        public int ProductId { get; set; }
        
        /// <summary>
        /// Gets or sets the quantity to transfer
        /// </summary>
        public int Quantity { get; set; }
        
        /// <summary>
        /// Gets or sets the destination department
        /// </summary>
        public string DestinationDepartment { get; set; }
        
        /// <summary>
        /// Gets or sets the reason for transfer
        /// </summary>
        public string ReasonForTransfer { get; set; }
    }
}