<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:vm="clr-namespace:CakeBistro.ViewModels.Admin"
             x:Class="CakeBistro.Views.Admin.UserManagementPage">
    <ContentPage.BindingContext>
        <vm:UserManagementViewModel />
    </ContentPage.BindingContext>
    <StackLayout Padding="20">
        <Label Text="User Management" FontSize="24" FontAttributes="Bold" />
        <!-- User list and actions will go here -->
        <CollectionView ItemsSource="{Binding Users}">
            <CollectionView.ItemTemplate>
                <DataTemplate>
                    <StackLayout Orientation="Horizontal" Spacing="10">
                        <Label Text="{Binding Username}" />
                        <Label Text="{Binding Email}" />
                        <Label Text="{Binding Role.Name}" />
                        <Button Text="Edit" Command="{Binding Source={RelativeSource AncestorType={x:Type vm:UserManagementViewModel}}, Path=EditUserCommand}" CommandParameter="{Binding .}" />
                        <Button Text="Delete" Command="{Binding Source={RelativeSource AncestorType={x:Type vm:UserManagementViewModel}}, Path=DeleteUserCommand}" CommandParameter="{Binding .}" />
                    </StackLayout>
                </DataTemplate>
            </CollectionView.ItemTemplate>
        </CollectionView>
        <Button Text="Add User" Command="{Binding AddUserCommand}" />
    </StackLayout>
</ContentPage>
