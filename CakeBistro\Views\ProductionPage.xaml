<!-- ... existing code ...

<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2022/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="CakeBistro.Views.ProductionPage"
             Title="Production Management">
    <ContentPage.Content>
        <ScrollView>
            <VerticalStackLayout Spacing="25" Padding="30">
                <!-- Recipe Management -->
                <Label Text="Recipes" FontSize="24" HorizontalOptions="Start"/>
                
                <HorizontalStackLayout>
                    <Entry Placeholder="Search recipes..." WidthRequest="200"/>
                    <Button Text="Add" Clicked="OnAddRecipeClicked"/>
                </HorizontalStackLayout>
                
                <CollectionView ItemsSource="{Binding Products}"
                                  SelectedItem="{Binding SelectedProduct}">
                    <CollectionView.ItemsLayout>
                        <LinearItemsLayout Orientation="Vertical"/>
                    </CollectionView.ItemsLayout>
                    <CollectionView.ItemTemplate>
                        <DataTemplate>
                            <Grid ColumnDefinitions="*,*,*" RowDefinitions="Auto,Auto">
                                <Label Grid.Row="0" Grid.Column="0" Text="{Binding Name}"/>
                                <Label Grid.Row="0" Grid.Column="1" Text="{Binding PricePerUnit, StringFormat='Price: {0:C2}'}"/>
                                <Label Grid.Row="0" Grid.Column="2" Text="{Binding CurrentStock, StringFormat='Stock: {0:F2} units'}"/>
                                <Label Grid.Row="1" Grid.Column="0" Text="{Binding Category}"/>
                                <Label Grid.Row="1" Grid.Column="1" Text="{Binding Cost, StringFormat='Cost: {0:C2}'}"/>
                                <Label Grid.Row="1" Grid.Column="2" Text="{Binding ProfitMargin, StringFormat='Margin: {0:P2}'}"/>
                            </Grid>
                        </DataTemplate>
                    </CollectionView.ItemTemplate>
                </CollectionView>
                
                <Label Text="Selected Recipe Details" FontSize="18" HorizontalOptions="Start"/>
                
                <Grid ColumnDefinitions="*,*" RowDefinitions="Auto,Auto,Auto,Auto,Auto,Auto">
                    <Label Grid.Row="0" Grid.Column="0" Text="Name:"/>
                    <Entry Grid.Row="0" Grid.Column="1" Text="{Binding SelectedProduct.Name, Mode=TwoWay}"/>
                    
                    <Label Grid.Row="1" Grid.Column="0" Text="Description:"/>
                    <Editor Grid.Row="1" Grid.Column="1" Text="{Binding SelectedProduct.Description, Mode=TwoWay}"/>
                    
                    <Label Grid.Row="2" Grid.Column="0" Text="Category:"/>
                    <Entry Grid.Row="2" Grid.Column="1" Text="{Binding SelectedProduct.Category, Mode=TwoWay}"/>
                    
                    <Label Grid.Row="3" Grid.Column="0" Text="Current Stock:"/>
                    <Label Grid.Row="3" Grid.Column="1" Text="{Binding SelectedProduct.CurrentStock, StringFormat={}{0:F2} units"/>
                    
                    <Label Grid.Row="4" Grid.Column="0" Text="Reorder Level:"/>
                    <Entry Grid.Row="4" Grid.Column="1" Text="{Binding SelectedProduct.ReorderLevel, Mode=TwoWay, StringFormat={}{0:F2}}"/>
                    
                    <Label Grid.Row="5" Grid.Column="0" Text="Profit Margin:"/>
                    <Entry Grid.Row="5" Grid.Column="1" Text="{Binding SelectedProduct.ProfitMargin, Mode=TwoWay, StringFormat={}{0:P2}}"/>
                </Grid>
                
                <Button Text="Save Changes" Clicked="OnSaveRecipeChanges"/>
                <Button Text="Delete Recipe" Clicked="OnDeleteRecipe"/>
                
                <!-- Recipe Ingredients -->
                <Label Text="Ingredients" FontSize="18" HorizontalOptions="Start"/>
                
                <Grid ColumnDefinitions="*,*,*" RowDefinitions="Auto,Auto,Auto,Auto">
                    <Picker Grid.Row="0" Grid.Column="0" ItemsSource="{Binding AvailableRawMaterials}" SelectedItem="{Binding SelectedRawMaterial, Mode=TwoWay}"/>
                    <Entry Grid.Row="0" Grid.Column="1" Text="{Binding RawMaterialQuantity, Mode=TwoWay, StringFormat={}{0:F2}}"/>
                    <Button Grid.Row="0" Grid.Column="2" Text="Add Ingredient" Clicked="OnAddIngredient"/>
                    
                    <ListView Grid.Row="1" Grid.ColumnSpan="3" ItemsSource="{Binding SelectedProduct.RawMaterials}"
                              SelectedItem="{Binding SelectedIngredient}">
                        <ListView.ItemTemplate>
                            <DataTemplate>
                                <ViewCell>
                                    <Grid ColumnDefinitions="*,*">
                                        <Label Text="{Binding RawMaterial.Name}"/>
                                        <Label Text="{Binding Quantity, StringFormat='{0:F2} units'}" HorizontalOptions="End"/>
                                    </Grid>
                                </ViewCell>
                            </DataTemplate>
                        </ListView.ItemTemplate>
                    </ListView>
                    
                    <Button Grid.Row="2" Grid.Column="2" Text="Remove Ingredient" Clicked="OnRemoveIngredient" IsEnabled="{Binding SelectedIngredient != null}"/>
                    
                    <Label Grid.Row="3" Grid.Column="0" Text="Total Cost:"/>
                    <Label Grid.Row="3" Grid.Column="1" Text="{Binding TotalCost, StringFormat='{0:C2}'}"/>
                    <Button Grid.Row="3" Grid.Column="2" Text="Calculate Cost" Clicked="OnCalculateCost"/>
                </Grid>
                
                <!-- Batch Management -->
                <Label Text="Batch Tracking" FontSize="24" HorizontalOptions="Start"/>
                
                <HorizontalStackLayout>
                    <DatePicker Date="{Binding StartDate, Mode=TwoWay, StringFormat={0:yyyy-MM-dd}}"/>
                    <DatePicker Date="{Binding EndDate, Mode=TwoWay, StringFormat={0:yyyy-MM-dd}}"/>
                    <Button Text="Filter" Clicked="OnFilterBatches"/>
                </HorizontalStackLayout>
                
                <CollectionView ItemsSource="{Binding InventoryBatches}"
                                  SelectedItem="{Binding SelectedBatch}">
                    <CollectionView.ItemsLayout>
                        <LinearItemsLayout Orientation="Vertical"/>
                    </CollectionView.ItemsLayout>
                    <CollectionView.ItemTemplate>
                        <DataTemplate>
                            <Grid ColumnDefinitions="*,*,*" RowDefinitions="Auto,Auto">
                                <Label Grid.Row="0" Grid.Column="0" Text="{Binding Product.Name}"/>
                                <Label Grid.Row="0" Grid.Column="1" Text="{Binding BatchNumber}"/>
                                <Label Grid.Row="0" Grid.Column="2" Text="{Binding ExpiryDate, StringFormat='Expires: {0:yyyy-MM-dd}'}"/>
                                <Label Grid.Row="1" Grid.Column="0" Text="{Binding ProductionDate, StringFormat='Produced: {0:yyyy-MM-dd}'}"/>
                                <Label Grid.Row="1" Grid.Column="1" Text="{Binding ProducedQuantity, StringFormat='{0:F2} units'}"/>
                                <Label Grid.Row="1" Grid.Column="2" Text="{Binding DamagedQuantity, StringFormat='Damaged: {0:F2} units'}"/>
                            </Grid>
                        </DataTemplate>
                    </CollectionView.ItemTemplate>
                </CollectionView>
                
                <Label Text="Selected Batch Details" FontSize="18" HorizontalOptions="Start"/>
                
                <Grid ColumnDefinitions="*,*" RowDefinitions="Auto,Auto,Auto,Auto,Auto">
                    <Label Grid.Row="0" Grid.Column="0" Text="Batch Number:"/>
                    <Label Grid.Row="0" Grid.Column="1" Text="{Binding SelectedBatch.BatchNumber}"/>
                    
                    <Label Grid.Row="1" Grid.Column="0" Text="Production Date:"/>
                    <DatePicker Grid.Row="1" Grid.Column="1" Date="{Binding SelectedBatch.ProductionDate, Mode=TwoWay, StringFormat={0:yyyy-MM-dd}}"/>
                    
                    <Label Grid.Row="2" Grid.Column="0" Text="Expiry Date:"/>
                    <DatePicker Grid.Row="2" Grid.Column="1" Date="{Binding SelectedBatch.ExpiryDate, Mode=TwoWay, StringFormat={0:yyyy-MM-dd}}"/>
                    
                    <Label Grid.Row="3" Grid.Column="0" Text="Produced Quantity:"/>
                    <Entry Grid.Row="3" Grid.Column="1" Text="{Binding ProducedQuantity, Mode=TwoWay, StringFormat={}{0:F2}}"/>
                    
                    <Label Grid.Row="4" Grid.Column="0" Text="Damaged Quantity:"/>
                    <Entry Grid.Row="4" Grid.Column="1" Text="{Binding DamagedQuantity, Mode=TwoWay, StringFormat={}{0:F2}}"/>
                </Grid>
                
                <Button Text="Update Stock" Clicked="OnUpdateStock" IsEnabled="{Binding SelectedBatch != null && ProducedQuantity > 0}"/>
                <Button Text="Record Damage" Clicked="OnRecordDamage" IsEnabled="{Binding SelectedBatch != null && DamagedQuantity > 0}"/>
                
                <!-- Production Summary -->
                <Label Text="Production Summary" FontSize="24" HorizontalOptions="Start"/>
                
                <Grid ColumnDefinitions="*,*,*,*" RowDefinitions="Auto,Auto,Auto,Auto">
                    <Label Text="Total Batches:" Grid.Row="0" Grid.Column="0"/>
                    <Label Text="{Binding TotalBatches}" Grid.Row="0" Grid.Column="1"/>
                    
                    <Label Text="Total Produced:" Grid.Row="1" Grid.Column="0"/>
                    <Label Text="{Binding TotalProduced, StringFormat='{0:F2} units'}" Grid.Row="1" Grid.Column="1"/>
                    
                    <Label Text="Total Damaged:" Grid.Row="2" Grid.Column="0"/>
                    <Label Text="{Binding TotalDamaged, StringFormat='{0:F2} units'}" Grid.Row="2" Grid.Column="1"/>
                    
                    <Label Text="Average Yield Rate:" Grid.Row="3" Grid.Column="0"/>
                    <Label Text="{Binding AverageYieldRate, StringFormat='{0:P2}'}" Grid.Row="3" Grid.Column="1"/>
                    
                    <Label Text="Last Production Run:" Grid.Row="0" Grid.Column="2"/>
                    <Label Text="{Binding LastProductionRun, StringFormat='{0:yyyy-MM-dd}'}" Grid.Row="0" Grid.Column="3"/>
                    
                    <Label Text="Next Scheduled Production:" Grid.Row="1" Grid.Column="2"/>
                    <Label Text="{Binding NextScheduledProduction, StringFormat='{0:yyyy-MM-dd}'}" Grid.Row="1" Grid.Column="3"/>
                    
                    <Label Text="Current Shelf Life (days):" Grid.Row="2" Grid.Column="2"/>
                    <Label Text="{Binding CurrentShelfLife}" Grid.Row="2" Grid.Column="3"/>
                    
                    <Label Text="Wastage Rate:" Grid.Row="3" Grid.Column="2"/>
                    <Label Text="{Binding WastageRate, StringFormat='{0:P2}'}" Grid.Row="3" Grid.Column="3"/>
                </Grid>
                
                <Button Text="Generate Production Report" Clicked="OnGenerateProductionReport"/>
                <Button Text="Schedule New Production" Clicked="OnScheduleNewProduction"/>
            </VerticalStackLayout>
        </ScrollView>
    </ContentPage.Content>
</ContentPage>