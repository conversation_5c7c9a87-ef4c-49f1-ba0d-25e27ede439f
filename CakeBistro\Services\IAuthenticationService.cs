using CakeBistro.Core.Models;

namespace CakeBistro.Services
{
    public interface IAuthenticationService
    {
        // Authenticate User and return User object if successful
        Task<User?> AuthenticateAsync(string username, string password, string ipAddress);

        // Get all users
        Task<IEnumerable<User>> GetAllUsersAsync();

        // Get User by ID
        Task<User?> GetUserByIdAsync(Guid id);

        // Create a new User
        Task<Guid> CreateUserAsync(User user, string password);

        // Create a new User with creator info
        Task<Guid> CreateUserAsync(User user, User creator, string password);

        // Update User details
        Task UpdateUserAsync(User user);

        // Update User with updater info
        Task UpdateUserAsync(User user, User updater);

        // Delete a User
        Task DeleteUserAsync(Guid id);

        // Get all roles
        Task<IEnumerable<Role>> GetAllRolesAsync();

        // Get Role by ID
        Task<Role?> GetRoleByIdAsync(Guid id);

        // Create a new Role
        Task<Guid> CreateRoleAsync(Role role);

        // Create a new Role with creator info
        Task<Guid> CreateRoleAsync(Role role, Role creator);

        // Update Role details
        Task UpdateRoleAsync(Role role);

        // Update Role with updater info
        Task UpdateRoleAsync(Role role, Role updater);

        // Delete a Role
        Task DeleteRoleAsync(Guid id);

        // Get all permissions
        Task<IEnumerable<Permission>> GetAllPermissionsAsync();

        // Assign permissions to Role
        Task AssignPermissionsToRoleAsync(Guid roleId, IEnumerable<Guid> permissionIds);

        // Remove permissions from Role
        Task RemovePermissionsFromRoleAsync(Guid roleId, IEnumerable<Guid> permissionIds);

        // Log User activity
        Task LogUserActivityAsync(Guid userId, string activityType, string description, string ipAddress);

        // Get User activity logs
        Task<IEnumerable<UserActivity>> GetUserActivityLogsAsync(Guid userId, DateTime startDate, DateTime endDate);

        // Check if User has specific permission
        Task<bool> HasUserPermissionAsync(Guid userId, string permissionName);
    }
}
