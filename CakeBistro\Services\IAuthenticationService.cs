using CakeBistro.Core.Models;

namespace CakeBistro.Services
{
    public interface IAuthenticationService
    {
        // Authenticate CakeBistro.Core.Models.User and return CakeBistro.Core.Models.User object if successful
        Task<CakeBistro.Core.Models.User> AuthenticateAsync(string username, string password, string ipAddress);
        
        // Get all users
        Task<IEnumerable<CakeBistro.Core.Models.User>> GetAllUsersAsync();
        
        // Get CakeBistro.Core.Models.User by ID
        Task<CakeBistro.Core.Models.User> GetUserByIdAsync(Guid id);
        
        // Create a new CakeBistro.Core.Models.User
        Task<Guid> CreateUserAsync(CakeBistro.Core.Models.User CakeBistro.Core.Models.User, string password);
        
        // Update CakeBistro.Core.Models.User details
        Task UpdateUserAsync(CakeBistro.Core.Models.User CakeBistro.Core.Models.User);
        
        // Delete a CakeBistro.Core.Models.User
        Task DeleteUserAsync(Guid id);
        
        // Get all roles
        Task<IEnumerable<CakeBistro.Core.Models.Role>> GetAllRolesAsync();
        
        // Get CakeBistro.Core.Models.Role by ID
        Task<CakeBistro.Core.Models.Role> GetRoleByIdAsync(Guid id);
        
        // Create a new CakeBistro.Core.Models.Role
        Task<Guid> CreateRoleAsync(CakeBistro.Core.Models.Role CakeBistro.Core.Models.Role);
        
        // Update CakeBistro.Core.Models.Role details
        Task UpdateRoleAsync(CakeBistro.Core.Models.Role CakeBistro.Core.Models.Role);
        
        // Delete a CakeBistro.Core.Models.Role
        Task DeleteRoleAsync(Guid id);
        
        // Get all permissions
        Task<IEnumerable<Permission>> GetAllPermissionsAsync();
        
        // Assign permissions to CakeBistro.Core.Models.Role
        Task AssignPermissionsToRoleAsync(Guid roleId, IEnumerable<Guid> permissionIds);
        
        // Remove permissions from CakeBistro.Core.Models.Role
        Task RemovePermissionsFromRoleAsync(Guid roleId, IEnumerable<Guid> permissionIds);
        
        // Log CakeBistro.Core.Models.User activity
        Task LogUserActivityAsync(Guid userId, string activityType, string description, string ipAddress);
        
        // Get CakeBistro.Core.Models.User activity logs
        Task<IEnumerable<UserActivity>> GetUserActivityLogsAsync(Guid userId, DateTime startDate, DateTime endDate);
        
        // Check if CakeBistro.Core.Models.User has specific permission
        Task<bool> HasUserPermissionAsync(Guid userId, string permissionName);
    }
}
