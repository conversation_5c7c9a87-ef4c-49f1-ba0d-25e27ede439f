<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:vm="clr-namespace:CakeBistro.ViewModels;assembly=CakeBistro"
             x:Class="CakeBistro.Views.DeliveryManifestDetailPage"
             Title="Delivery Manifest Details">
    <!-- Set the BindingContext in code-behind instead, or ensure the ViewModel type and namespace are correct -->
    <ScrollView>
        <StackLayout Padding="20">
            <Label Text="Delivery Manifest Details" FontSize="24" HorizontalOptions="Center" />
            <DatePicker Date="{Binding Manifest.DeliveryDate}" />
            <Entry Placeholder="Notes" Text="{Binding Manifest.Notes}" />
            <Picker Title="Vehicle" ItemsSource="{Binding VehicleOptions}" ItemDisplayBinding="{Binding RegistrationNumber}" SelectedItem="{Binding Manifest.Vehicle}" />
            <Picker Title="Driver" ItemsSource="{Binding DriverOptions}" ItemDisplayBinding="{Binding FirstName}" SelectedItem="{Binding Manifest.Driver}" />
            <Picker Title="Status" ItemsSource="{Binding StatusOptions}" SelectedItem="{Binding Manifest.Status}" />
            <Button Text="Save" Command="{Binding SaveManifestCommand}" Margin="10" />
        </StackLayout>
    </ScrollView>
</ContentPage>
