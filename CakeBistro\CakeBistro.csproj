<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0-windows10.0.19041.0</TargetFramework>
    <!-- Uncomment to also build the tizen app. You will need to install tizen by following this: https://github.com/Samsung/Tizen.NET -->
    <!-- <TargetFrameworks>$(TargetFrameworks);net8.0-tizen</TargetFrameworks> -->
    <OutputType>Exe</OutputType>
    <RootNamespace>CakeBistro</RootNamespace>
    <UseMaui>true</UseMaui>
    <SingleProject>true</SingleProject>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    <!-- Display Version -->
    <ApplicationDisplayVersion>1.0</ApplicationDisplayVersion>
    <ApplicationVersion>1</ApplicationVersion>
  </PropertyGroup>
  <PropertyGroup>
    <SupportedOSPlatformVersion
      Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'android'"
      >21.0</SupportedOSPlatformVersion>
    
    <SupportedOSPlatformVersion
      Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'ios'"
      >11.0</SupportedOSPlatformVersion
    >
    <SupportedOSPlatformVersion
      Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'maccatalyst'"
      >13.1</SupportedOSPlatformVersion
    >
    <SupportedOSPlatformVersion
      Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'windows'"
      >10.0.17763.0</SupportedOSPlatformVersion
    >
    <TargetPlatformMinVersion
      Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'windows'"
      >10.0.17763.0</TargetPlatformMinVersion
    >
    <SupportedOSPlatformVersion
      Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'tizen'"
      >6.5</SupportedOSPlatformVersion
    >
  </PropertyGroup>
  <ItemGroup>
    <!-- App Icon -->
    <MauiIcon
      Include="Resources\AppIcon\appicon.svg"
      ForegroundFile="Resources\AppIcon\appiconfg.svg"
      Color="#512BD4"
    />
    <!-- Splash Screen -->
    <MauiSplashScreen Include="Resources\Splash\splash.svg" Color="#512BD4" BaseSize="128,128" />
    <!-- Images -->
    <MauiImage Include="Resources\Images\*" />
    <MauiImage Update="Resources\Images\dotnet_bot.svg" BaseSize="168,208" />
    <!-- Custom Fonts -->
    <MauiFont Include="Resources\Fonts\*" />
    <!-- Raw Assets (also remove the "Resources\Raw" prefix) -->
    <MauiAsset Include="Resources\Raw\**" LogicalName="%(RecursiveDir)%(Filename)%(Extension)" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="EPPlus" Version="8.0.7" />
    <PackageReference Include="iTextSharp.LGPLv2.Core" Version="3.7.4" />
    <PackageReference Include="Microcharts.Maui" Version="1.0.1" />
    <PackageReference Include="Microsoft.Maui.Controls" Version="8.0.100" />
    <PackageReference Include="Microsoft.Maui.Controls.Compatibility" Version="8.0.100" />
    <PackageReference Include="Microsoft.Extensions.Logging.Debug" Version="8.0.1" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite" Version="8.0.1" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.1" />
    <PackageReference Include="CommunityToolkit.Mvvm" Version="8.2.2" />
    <PackageReference Include="CommunityToolkit.Maui" Version="7.0.1" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.1" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="8.0.1" />
    <PackageReference Include="System.ComponentModel.Annotations" Version="5.0.0" />
    <PackageReference Include="BCrypt.Net-Next" Version="4.0.3" />
  </ItemGroup>
  <ItemGroup>
    <MauiXaml Include="Views\**\*.xaml" />
    <MauiXaml Include="Controls\**\*.xaml" />
    <Compile Update="Views\**\*.xaml.cs">
      <DependentUpon>%(Filename).xaml</DependentUpon>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <Compile Remove="tests\**" />
    <EmbeddedResource Remove="tests\**" />
    <None Remove="tests\**" />
    <!-- <Compile Remove="Repositories\**" />
    <EmbeddedResource Remove="Repositories\**" />
    <None Remove="Repositories\**" /> -->
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\CakeBistro.Core\CakeBistro.Core.csproj" />
  </ItemGroup>
</Project>
