// Create RepositoryTests.cs
using CakeBistro.Repositories;
using CakeBistro.Core.Models;
using Microsoft.EntityFrameworkCore;
using Xunit;

namespace CakeBistro.Tests;

public class RepositoryTests<T> where T : class
{
    private readonly CakeBistroContext _context;
    private readonly IRepository<T> _repository;

    public RepositoryTests()
    {
        // Set up in-memory database
        var options = new DbContextOptionsBuilder<CakeBistroContext>()
            .UseInMemoryDatabase(databaseName: "TestDatabase")
            .Options;

        _context = new CakeBistroContext(options);
        _repository = new BaseRepository<T>(_context);
    }

    [Fact]
    public async Task AddAsync_ShouldAddEntity()
    {
        // Arrange - This needs to be implemented in derived classes
    }

    [Fact]
    public async Task GetByIdAsync_ShouldReturnEntity()
    {
        // Arrange - This needs to be implemented in derived classes
    }

    [Fact]
    public async Task UpdateAsync_ShouldUpdateEntity()
    {
        // Arrange - This needs to be implemented in derived classes
    }

    [Fact]
    public async Task DeleteAsync_ShouldRemoveEntity()
    {
        // Arrange - This needs to be implemented in derived classes
    }
}

// Specific test classes for each entity
class RawMaterialRepositoryTests : RepositoryTests<RawMaterial>
{
    [Fact]
    public override async Task AddAsync_ShouldAddEntity()
    {
        // Arrange
        var entity = new RawMaterial
        {
            Name = "Test Material",
            Unit = "kg",
            PricePerUnit = 10.0m,
            CurrentStock = 100,
            MinimumStock = 20
        };

        // Act
        var result = await _repository.AddAsync(entity);
        
        // Assert
        Assert.NotNull(result);
        Assert.Equal("Test Material", result.Name);
        Assert.True(result.Id > 0);
    }

    [Fact]
    public override async Task GetByIdAsync_ShouldReturnEntity()
    {
        // Arrange
        var entity = new RawMaterial
        {
            Name = "Test Material",
            Unit = "kg",
            PricePerUnit = 10.0m,
            CurrentStock = 100,
            MinimumStock = 20
        };
        
        var addedEntity = await _repository.AddAsync(entity);
        
        // Act
        var result = await _repository.GetByIdAsync(addedEntity.Id);
        
        // Assert
        Assert.NotNull(result);
        Assert.Equal(addedEntity.Id, result.Id);
        Assert.Equal("Test Material", result.Name);
    }

    [Fact]
    public override async Task UpdateAsync_ShouldUpdateEntity()
    {
        // Arrange
        var entity = new RawMaterial
        {
            Name = "Test Material",
            Unit = "kg",
            PricePerUnit = 10.0m,
            CurrentStock = 100,
            MinimumStock = 20
        };
        
        var addedEntity = await _repository.AddAsync(entity);
        
        // Modify entity
        addedEntity.Name = "Updated Material";
        addedEntity.PricePerUnit = 15.0m;
        
        // Act
        await _repository.UpdateAsync(addedEntity);
        
        // Get updated entity
        var result = await _repository.GetByIdAsync(addedEntity.Id);
        
        // Assert
        Assert.NotNull(result);
        Assert.Equal("Updated Material", result.Name);
        Assert.Equal(15.0m, result.PricePerUnit);
    }

    [Fact]
    public override async Task DeleteAsync_ShouldRemoveEntity()
    {
        // Arrange
        var entity = new RawMaterial
        {
            Name = "Test Material",
            Unit = "kg",
            PricePerUnit = 10.0m,
            CurrentStock = 100,
            MinimumStock = 20
        };
        
        var addedEntity = await _repository.AddAsync(entity);
        
        // Act
        await _repository.DeleteAsync(addedEntity.Id);
        
        // Try to get deleted entity
        var result = await _repository.GetByIdAsync(addedEntity.Id);
        
        // Assert
        Assert.Null(result);
    }
}