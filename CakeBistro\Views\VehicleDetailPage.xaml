<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:viewmodels="clr-namespace:CakeBistro.ViewModels"
             x:Class="CakeBistro.Views.VehicleDetailPage"
             Title="Vehicle Details">
    <ContentPage.BindingContext>
        <viewmodels:VehicleDetailViewModel />
    </ContentPage.BindingContext>
    <ScrollView>
        <StackLayout Padding="20" Spacing="12">
            <Label Text="Vehicle Details" FontSize="24" HorizontalOptions="Center" />
            <Label Text="Registration Number *" AutomationId="lblRegNum" />
            <Entry Placeholder="Registration Number" Text="{Binding Vehicle.RegistrationNumber}" AutomationId="entryRegNum" />
            <Label TextColor="Red" FontSize="12" IsVisible="{Binding RegistrationNumberErrorVisible}" Text="{Binding RegistrationNumberError}" />
            <Label Text="Make *" />
            <Entry Placeholder="Make" Text="{Binding Vehicle.Make}" />
            <Label TextColor="Red" FontSize="12" IsVisible="{Binding MakeErrorVisible}" Text="{Binding MakeError}" />
            <Label Text="Model *" />
            <Entry Placeholder="Model" Text="{Binding Vehicle.Model}" />
            <Label TextColor="Red" FontSize="12" IsVisible="{Binding ModelErrorVisible}" Text="{Binding ModelError}" />
            <Label Text="Year" />
            <Entry Placeholder="Year" Text="{Binding Vehicle.Year}" Keyboard="Numeric" />
            <Label Text="Capacity (kg)" />
            <Entry Placeholder="Capacity (kg)" Text="{Binding Vehicle.Capacity}" Keyboard="Numeric" />
            <Label Text="Fuel Type" />
            <Entry Placeholder="Fuel Type" Text="{Binding Vehicle.FuelType}" />
            <Label Text="Last Maintenance Date" />
            <DatePicker Date="{Binding Vehicle.LastMaintenanceDate}" />
            <Label Text="Next Maintenance Date" />
            <DatePicker Date="{Binding Vehicle.NextMaintenanceDate}" />
            <Label Text="Status" />
            <Picker Title="Status" ItemsSource="{Binding StatusOptions}" SelectedItem="{Binding Vehicle.Status}" />
            <Label FontSize="10" TextColor="Gray" Text="* Required fields. Hover for help." />
            <Button Text="Save" Command="{Binding SaveVehicleCommand}" Margin="10" />
        </StackLayout>
    </ScrollView>
</ContentPage>
