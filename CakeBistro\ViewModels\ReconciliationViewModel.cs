using System;
using System.Threading.Tasks;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using CakeBistro.Services;
using CakeBistro.Models;

namespace CakeBistro.ViewModels;

public partial class ReconciliationViewModel : ObservableObject
{
    private readonly FinanceService _financeService;

    [ObservableProperty]
    private DateTime reconciliationDate = DateTime.Today;

    [ObservableProperty]
    private decimal statementBalance;

    [ObservableProperty]
    private decimal varianceTolerance = 0.01m;

    [ObservableProperty]
    private string reconciliationNotes;

    [ObservableProperty]
    private string reconciliationResult;

    [ObservableProperty]
    private int selectedAccountId;

    public IRelayCommand PerformReconciliationCommand { get; }

    public ReconciliationViewModel(FinanceService financeService)
    {
        _financeService = financeService;
        PerformReconciliationCommand = new AsyncRelayCommand(PerformReconciliationAsync, CanReconcile);
    }

    private bool CanReconcile() => SelectedAccountId > 0;

    private async Task PerformReconciliationAsync()
    {
        try
        {
            await _financeService.ReconcileAccountAsync(SelectedAccountId, ReconciliationDate, StatementBalance);
            ReconciliationResult = $"Reconciliation complete for account {SelectedAccountId}.";
        }
        catch (Exception ex)
        {
            ReconciliationResult = $"Error: {ex.Message}";
        }
    }
}
