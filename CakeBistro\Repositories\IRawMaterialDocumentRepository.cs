using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using CakeBistro.Core.Models;

namespace CakeBistro.Repositories
{
    public interface IRawMaterialDocumentRepository : IRepository<RawMaterialDocument>
    {
        Task<IEnumerable<RawMaterialDocument>> GetDocumentsByMaterialAsync(Guid materialId);
        Task<IEnumerable<RawMaterialDocument>> GetDocumentsByTypeAsync(string documentType);
        Task<IEnumerable<RawMaterialDocument>> GetRecentDocumentsAsync(int count);
    }
}
