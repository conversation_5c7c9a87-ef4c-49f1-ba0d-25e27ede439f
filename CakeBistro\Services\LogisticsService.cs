using Microsoft.EntityFrameworkCore;
using CakeBistro.Core.Interfaces;
using CakeBistro.Core.Models;
using System.Device.Location;

namespace CakeBistro.Services
{
    public class LogisticsService : ILogisticsService
    {
        private readonly CakeBistroContext _context;

        public LogisticsService(CakeBistroContext context)
        {
            _context = context;
        }

        #region Vehicle Management

        public async Task<Vehicle> AddVehicleAsync(Vehicle vehicle)
        {
            _context.Vehicles.Add(vehicle);
            await _context.SaveChangesAsync();
            return vehicle;
        }

        public async Task<Vehicle> UpdateVehicleAsync(Vehicle vehicle)
        {
            _context.Entry(vehicle).State = EntityState.Modified;
            await _context.SaveChangesAsync();
            return vehicle;
        }

        public async Task<Vehicle> GetVehicleByIdAsync(int id)
        {
            return await _context.Vehicles
                .Include(v => v.MaintenanceRecords)
                .FirstOrDefaultAsync(v => v.Id == id);
        }

        public async Task<IEnumerable<Vehicle>> GetAllVehiclesAsync()
        {
            return await _context.Vehicles
                .Include(v => v.MaintenanceRecords)
                .ToListAsync();
        }

        public async Task<IEnumerable<Vehicle>> GetAvailableVehiclesAsync(DateTime date)
        {
            var busyVehicleIds = await _context.DeliveryManifests
                .Where(m => m.DeliveryDate.Date == date.Date && m.Status != "Cancelled")
                .Select(m => m.VehicleId)
                .ToListAsync();

            return await _context.Vehicles
                .Where(v => !busyVehicleIds.Contains(v.Id) && v.Status == "Active")
                .ToListAsync();
        }

        public async Task<bool> DeleteVehicleAsync(int id)
        {
            var vehicle = await _context.Vehicles.FindAsync(id);
            if (vehicle == null) return false;

            _context.Vehicles.Remove(vehicle);
            await _context.SaveChangesAsync();
            return true;
        }

        #endregion

        #region Driver Management

        public async Task<Driver> AddDriverAsync(Driver driver)
        {
            _context.Drivers.Add(driver);
            await _context.SaveChangesAsync();
            return driver;
        }

        public async Task<Driver> UpdateDriverAsync(Driver driver)
        {
            _context.Entry(driver).State = EntityState.Modified;
            await _context.SaveChangesAsync();
            return driver;
        }

        public async Task<Driver> GetDriverByIdAsync(int id)
        {
            return await _context.Drivers.FindAsync(id);
        }

        public async Task<IEnumerable<Driver>> GetAllDriversAsync()
        {
            return await _context.Drivers.ToListAsync();
        }

        public async Task<IEnumerable<Driver>> GetAvailableDriversAsync(DateTime date)
        {
            var busyDriverIds = await _context.DeliveryManifests
                .Where(m => m.DeliveryDate.Date == date.Date && m.Status != "Cancelled")
                .Select(m => m.DriverId)
                .ToListAsync();

            return await _context.Drivers
                .Where(d => !busyDriverIds.Contains(d.Id) && d.Status == "Active")
                .ToListAsync();
        }

        public async Task<bool> DeleteDriverAsync(int id)
        {
            var driver = await _context.Drivers.FindAsync(id);
            if (driver == null) return false;

            _context.Drivers.Remove(driver);
            await _context.SaveChangesAsync();
            return true;
        }

        #endregion

        #region Maintenance Records

        public async Task<MaintenanceRecord> AddMaintenanceRecordAsync(MaintenanceRecord record)
        {
            _context.MaintenanceRecords.Add(record);
            var vehicle = await _context.Vehicles.FindAsync(record.VehicleId);
            if (vehicle != null)
            {
                vehicle.LastMaintenanceDate = record.ServiceDate;
                vehicle.NextMaintenanceDate = record.NextServiceDate ?? record.ServiceDate.AddMonths(3);
            }
            await _context.SaveChangesAsync();
            return record;
        }

        public async Task<IEnumerable<MaintenanceRecord>> GetVehicleMaintenanceHistoryAsync(int vehicleId)
        {
            return await _context.MaintenanceRecords
                .Where(r => r.VehicleId == vehicleId)
                .OrderByDescending(r => r.ServiceDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<Vehicle>> GetVehiclesDueForMaintenanceAsync()
        {
            var twoWeeksFromNow = DateTime.UtcNow.AddDays(14);
            return await _context.Vehicles
                .Where(v => v.NextMaintenanceDate <= twoWeeksFromNow)
                .OrderBy(v => v.NextMaintenanceDate)
                .ToListAsync();
        }

        #endregion

        #region Delivery Manifests

        public async Task<DeliveryManifest> CreateManifestAsync(DeliveryManifest manifest)
        {
            _context.DeliveryManifests.Add(manifest);
            await _context.SaveChangesAsync();

            // Optimize the route after creation
            manifest.Stops = (await OptimizeRouteAsync(manifest)).ToList();
            var (distance, duration) = await CalculateRouteMetricsAsync(manifest.Stops);
            manifest.EstimatedDistance = distance;
            manifest.EstimatedDuration = duration;

            await _context.SaveChangesAsync();
            return manifest;
        }

        public async Task<DeliveryManifest> UpdateManifestAsync(DeliveryManifest manifest)
        {
            _context.Entry(manifest).State = EntityState.Modified;
            await _context.SaveChangesAsync();
            return manifest;
        }

        public async Task<DeliveryManifest> GetManifestByIdAsync(int id)
        {
            return await _context.DeliveryManifests
                .Include(m => m.Vehicle)
                .Include(m => m.Driver)
                .Include(m => m.Stops)
                    .ThenInclude(s => s.Order)
                .FirstOrDefaultAsync(m => m.Id == id);
        }

        public async Task<IEnumerable<DeliveryManifest>> GetManifestsForDateAsync(DateTime date)
        {
            return await _context.DeliveryManifests
                .Include(m => m.Vehicle)
                .Include(m => m.Driver)
                .Include(m => m.Stops)
                .Where(m => m.DeliveryDate.Date == date.Date)
                .OrderBy(m => m.DeliveryDate)
                .ToListAsync();
        }

        public async Task<bool> DeleteManifestAsync(int id)
        {
            var manifest = await _context.DeliveryManifests.FindAsync(id);
            if (manifest == null) return false;

            _context.DeliveryManifests.Remove(manifest);
            await _context.SaveChangesAsync();
            return true;
        }

        #endregion

        #region Route Optimization

        public async Task<IEnumerable<DeliveryStop>> OptimizeRouteAsync(DeliveryManifest manifest)
        {
            var stops = await _context.DeliveryStops
                .Where(s => s.ManifestId == manifest.Id)
                .ToListAsync();

            // Simple nearest neighbor algorithm
            var optimizedStops = new List<DeliveryStop>();
            var remainingStops = stops.ToList();

            if (!remainingStops.Any())
                return optimizedStops;

            // Start with the first stop
            var currentStop = remainingStops.First();
            optimizedStops.Add(currentStop);
            remainingStops.RemoveAt(0);

            while (remainingStops.Any())
            {
                var nearestStop = FindNearestStop(currentStop, remainingStops);
                optimizedStops.Add(nearestStop);
                remainingStops.Remove(nearestStop);
                currentStop = nearestStop;
            }

            // Update sequence numbers
            for (int i = 0; i < optimizedStops.Count; i++)
            {
                optimizedStops[i].SequenceNumber = i + 1;
            }

            return optimizedStops;
        }

        public async Task<(decimal Distance, TimeSpan Duration)> CalculateRouteMetricsAsync(IEnumerable<DeliveryStop> stops)
        {
            decimal totalDistance = 0;
            var totalDuration = TimeSpan.Zero;

            var stopsList = stops.OrderBy(s => s.SequenceNumber).ToList();
            for (int i = 0; i < stopsList.Count - 1; i++)
            {
                var current = stopsList[i];
                var next = stopsList[i + 1];

                var distance = CalculateDistance(
                    current.Latitude, current.Longitude,
                    next.Latitude, next.Longitude);

                totalDistance += distance;
                // Assume average speed of 40 km/h in urban areas
                totalDuration += TimeSpan.FromHours((double)distance / 40);
                // Add 15 minutes per stop for delivery time
                totalDuration += TimeSpan.FromMinutes(15);
            }

            return (totalDistance, totalDuration);
        }

        public async Task UpdateStopStatusAsync(int stopId, string status, string notes = null)
        {
            var stop = await _context.DeliveryStops.FindAsync(stopId);
            if (stop != null)
            {
                stop.Status = status;
                if (notes != null)
                    stop.DeliveryNotes = notes;
                if (status == "Delivered")
                    stop.ActualArrival = DateTime.UtcNow;

                await _context.SaveChangesAsync();
            }
        }

        #endregion

        #region Helper Methods

        private DeliveryStop FindNearestStop(DeliveryStop current, List<DeliveryStop> remainingStops)
        {
            return remainingStops.OrderBy(s => CalculateDistance(
                current.Latitude, current.Longitude,
                s.Latitude, s.Longitude))
                .First();
        }

        private decimal CalculateDistance(decimal lat1, decimal lon1, decimal lat2, decimal lon2)
        {
            var coord1 = new GeoCoordinate((double)lat1, (double)lon1);
            var coord2 = new GeoCoordinate((double)lat2, (double)lon2);
            return (decimal)coord1.GetDistanceTo(coord2) / 1000; // Convert to kilometers
        }

        /// <summary>
        /// Adjusts inventory levels for a product.
        /// </summary>
        /// <param name="adjustment">The adjustment details.</param>
        /// <returns>The adjusted product inventory.</returns>
        public async Task<Product> AdjustStockAsync(StockAdjustment adjustment)
        {
            if (adjustment == null)
                throw new ArgumentNullException(nameof(adjustment), "Adjustment cannot be null");

            if (adjustment.ProductId <= 0)
                throw new ArgumentException("Valid product ID is required", nameof(adjustment.ProductId));

            if (string.IsNullOrWhiteSpace(adjustment.Reason))
                throw new ArgumentException("Reason for adjustment is required", nameof(adjustment.Reason));

            if (adjustment.Quantity == 0)
                throw new ArgumentException("Quantity must be non-zero", nameof(adjustment.Quantity));

            if (adjustment.UserId <= 0)
                throw new ArgumentException("Valid user ID is required", nameof(adjustment.UserId));

            // Get the product from database
            var product = await _context.Products.FindAsync(adjustment.ProductId);
            if (product == null)
                throw new ArgumentException($"Product with ID {adjustment.ProductId} not found", nameof(adjustment.ProductId));

            // Apply the adjustment
            if (adjustment.Quantity > 0)
            {
                // Increase inventory - e.g., stock receipt
                product.CurrentStock += adjustment.Quantity;
                
                // Create inbound stock movement
                var movement = new StockMovement
                {
                    ProductId = adjustment.ProductId,
                    Quantity = adjustment.Quantity,
                    MovementType = MovementType.Inbound,
                    Reason = adjustment.Reason,
                    Date = adjustment.AdjustmentDate ?? DateTime.UtcNow,
                    UserId = adjustment.UserId
                };

                _context.StockMovements.Add(movement);
            }
            else
            {
                // Decrease inventory - e.g., stock loss
                if (product.CurrentStock < Math.Abs(adjustment.Quantity))
                {
                    throw new InvalidOperationException(
                        $"Insufficient stock for {product.Name}. Available: {product.CurrentStock}, Requested: {Math.Abs(adjustment.Quantity)}");
                }

                product.CurrentStock += adjustment.Quantity; // Adjustment.Quantity will be negative
                
                // Create outbound stock movement
                var movement = new StockMovement
                {
                    ProductId = adjustment.ProductId,
                    Quantity = Math.Abs(adjustment.Quantity),
                    MovementType = MovementType.Outbound,
                    Reason = adjustment.Reason,
                    Date = adjustment.AdjustmentDate ?? DateTime.UtcNow,
                    UserId = adjustment.UserId
                };

                _context.StockMovements.Add(movement);
            }

            // Update product last modified date
            product.UpdatedDate = DateTime.UtcNow;
            
            // Save changes to database
            await _context.SaveChangesAsync();
            
            return product;
        }

        #endregion
    }
}
