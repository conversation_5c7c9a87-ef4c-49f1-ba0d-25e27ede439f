<?xml version="1.0" encoding="utf-8" ?>
<ResourceDictionary xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
                    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml">
    <!-- Accessibility Styles: High Contrast, Large Font, Focus Visuals -->
    <Style x:Key="HighContrastLabel" TargetType="Label">
        <Setter Property="TextColor" Value="Black" />
        <Setter Property="BackgroundColor" Value="Yellow" />
        <Setter Property="FontAttributes" Value="Bold" />
    </Style>
    <Style x:Key="LargeFontLabel" TargetType="Label">
        <Setter Property="FontSize" Value="22" />
        <Setter Property="FontAttributes" Value="Bold" />
    </Style>
    <Style x:Key="FocusVisual" TargetType="VisualElement">
        <Setter Property="BorderColor" Value="{StaticResource Accent}" />
        <Setter Property="BorderWidth" Value="2" />
    </Style>
</ResourceDictionary>
