
using System;

namespace CakeBistro.Services.Exceptions
{
    /// <summary>
    /// Exception thrown when a business rule validation fails
    /// </summary>
    public class BusinessRuleValidationException : Exception
    {
        /// <summary>
        /// Gets the name of the business rule that failed
        /// </summary>
        public string RuleName { get; }

        /// <summary>
        /// Gets the error message associated with the failed business rule
        /// </summary>
        public override string Message { get; }

        /// <summary>
        /// Initializes a new instance of the <see cref="BusinessRuleValidationException"/> class.
        /// </summary>
        /// <param name="ruleName">Name of the business rule that failed</param>
        /// <param name="message">Error message for the failed business rule</param>
        public BusinessRuleValidationException(string ruleName, string message) 
            : base(message)
        {
            RuleName = ruleName;
            Message = message;
        }
    }
}