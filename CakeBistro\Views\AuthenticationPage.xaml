<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="MCakeBistro.Views.AuthenticationPage"
             Title="Login">
    <ScrollView>
        <VerticalStackLayout Spacing="20" Padding="30">
            <!-- Logo or application title -->
            <Label Text="MCakeBistro" 
                   FontSize="36"
                   HorizontalOptions="Center"
                   TextColor="#e74c3c"/>
            
            <!-- Login/Register toggle -->
            <HorizontalStackLayout HorizontalOptions="Center">
                <Button Text="Login"
                        Command="{Binding ToggleModeCommand}"
                        IsEnabled="{Binding IsLoginMode, Converter={StaticResource InverseBooleanConverter}}"
                        BackgroundColor="{Binding IsLoginMode, Converter={StaticResource BooleanToColorConverter}, ConverterParameter='Selected'}"
                        TextColor="White"/>
                <Button Text="Register"
                        Command="{Binding ToggleModeCommand}"
                        IsEnabled="{Binding IsLoginMode, Converter={StaticResource BooleanToInvertBooleanConverter}}"
                        BackgroundColor="{Binding IsLoginMode, Converter={StaticResource BooleanToColorConverter}, ConverterParameter='NotSelected'}"
                        TextColor="White"/>
            </HorizontalStackLayout>
            
            <!-- Login form -->
            <ContentView IsVisible="{Binding IsLoginMode}">
                <VerticalStackLayout Spacing="15">
                    <Entry Placeholder="Username"
                           Text="{Binding Username}"
                           Keyboard="Default"
                           ReturnType="Next"/>
                    <Entry Placeholder="Password"
                           Text="{Binding Password}"
                           IsPassword="True"
                           Keyboard="Default"
                           ReturnType="Done"/>
                    <Button Text="Login"
                            Command="{Binding LoginCommand}"
                            BackgroundColor="#e74c3c"
                            TextColor="White"/>
                </VerticalStackLayout>
            </ContentView>
            
            <!-- Registration form -->
            <ContentView IsVisible="{Binding IsLoginMode, Converter={StaticResource InverseBooleanConverter}}">
                <VerticalStackLayout Spacing="15">
                    <Entry Placeholder="Username"
                           Text="{Binding Username}"
                           Keyboard="Default"
                           ReturnType="Next"/>
                    <Entry Placeholder="Email"
                           Text="{Binding Email}"
                           Keyboard="Email"
                           ReturnType="Next"/>
                    <Entry Placeholder="Password"
                           Text="{Binding Password}"
                           IsPassword="True"
                           Keyboard="Default"
                           ReturnType="Next"/>
                    <Entry Placeholder="First Name"
                           Text="{Binding FirstName}"
                           Keyboard="Default"
                           ReturnType="Next"/>
                    <Entry Placeholder="Last Name"
                           Text="{Binding LastName}"
                           Keyboard="Default"
                           ReturnType="Done"/>
                    <Button Text="Register"
                            Command="{Binding RegisterCommand}"
                            BackgroundColor="#e74c3c"
                            TextColor="White"/>
                </VerticalStackLayout>
            </ContentView>
            
            <!-- Application features section (common to both modes) -->
            <VerticalStackLayout Spacing="10">
                <Label Text="Features" FontSize="Medium" FontAttributes="Bold" HorizontalOptions="Center"/>
                <Label Text="• Secure user authentication" />
                <Label Text="• Role-based access control" />
                <Label Text="• Activity logging" />
                <Label Text="• Profile management" />
            </VerticalStackLayout>
        </VerticalStackLayout>
    </ScrollView>
</ContentPage>