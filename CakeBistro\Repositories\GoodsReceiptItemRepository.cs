using CakeBistro.Core.Models;

namespace CakeBistro.Repositories
{
    public class GoodsReceiptItemRepository : Repository<GoodsReceiptItem>, IGoodsReceiptItemRepository
    {
        public GoodsReceiptItemRepository(InventoryContext context) : base(context)
        {
        }
        
        public async Task<IEnumerable<GoodsReceiptItem>> GetAllAsync()
        {
            return await _context.GoodsReceiptItems.ToListAsync();
        }
        
        public async Task<GoodsReceiptItem> GetByIdAsync(Guid id)
        {
            return await _context.GoodsReceiptItems.FindAsync(id);
        }
        
        public async Task AddAsync(GoodsReceiptItem item)
        {
            await _context.GoodsReceiptItems.AddAsync(item);
            await _context.SaveChangesAsync();
        }
        
        public async Task UpdateAsync(GoodsReceiptItem item)
        {
            _context.GoodsReceiptItems.Update(item);
            await _context.SaveChangesAsync();
        }
        
        public async Task DeleteAsync(Guid id)
        {
            var item = await _context.GoodsReceiptItems.FindAsync(id);
            if (item != null)
            {
                _context.GoodsReceiptItems.Remove(item);
                await _context.SaveChangesAsync();
            }
        }
        
        public async Task<IEnumerable<GoodsReceiptItem>> GetItemsByReceiptAsync(Guid receiptId)
        {
            return await _context.GoodsReceiptItems
                .Where(i => i.GoodsReceiptId == receiptId)
                .ToListAsync();
        }
        
        public async Task<IEnumerable<GoodsReceiptItem>> GetItemsByPurchaseOrderItemAsync(Guid purchaseOrderItemId)
        {
            return await _context.GoodsReceiptItems
                .Where(i => i.PurchaseOrderItemId == purchaseOrderItemId)
                .ToListAsync();
        }
    }
}
