<?xml version="1.0" encoding="utf-8" ?>
<views:FormTemplate xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
                    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
                    xmlns:views="clr-namespace:CakeBistro.Views"
                    x:Class="CakeBistro.Views.SupplierFormFields">
    <StackLayout>
        <Entry Placeholder="Name" Text="{Binding Supplier.Name}" />
        <Entry Placeholder="Contact Name" Text="{Binding Supplier.ContactName}" />
        <Entry Placeholder="Category" Text="{Binding Supplier.Category}" />
        <Entry Placeholder="Email" Text="{Binding Supplier.Email}" Keyboard="Email" />
        <Entry Placeholder="Phone" Text="{Binding Supplier.Phone}" Keyboard="Telephone" />
        <Entry Placeholder="Address" Text="{Binding Supplier.Address}" />
        <Entry Placeholder="Account Number" Text="{Binding Supplier.AccountNumber}" />
        <Entry Placeholder="Tax ID" Text="{Binding Supplier.TaxId}" />
        <Switch IsToggled="{Binding Supplier.IsActive}" />
        <Label Text="Active Supplier" />
    </StackLayout>
</views:FormTemplate>
