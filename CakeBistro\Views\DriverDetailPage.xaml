<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:vm="clr-namespace:CakeBistro.ViewModels"
             x:Class="CakeBistro.Views.DriverDetailPage"
             Title="Driver Details">
    <ContentPage.BindingContext>
        <vm:DriverDetailViewModel />
    </ContentPage.BindingContext>
    <ScrollView>
        <StackLayout Padding="20">
            <Label Text="Driver Details" FontSize="24" HorizontalOptions="Center" />
            <Entry Placeholder="First Name" Text="{Binding Driver.FirstName}" />
            <Entry Placeholder="Last Name" Text="{Binding Driver.LastName}" />
            <Entry Placeholder="License Number" Text="{Binding Driver.LicenseNumber}" />
            <DatePicker Date="{Binding Driver.LicenseExpiryDate}" />
            <Entry Placeholder="Phone Number" Text="{Binding Driver.PhoneNumber}" />
            <Entry Placeholder="Address" Text="{Binding Driver.Address}" />
            <Picker Title="Status" ItemsSource="{Binding StatusOptions}" SelectedItem="{Binding Driver.Status}" />
            <Button Text="Save" Command="{Binding SaveDriverCommand}" Margin="10" />
        </StackLayout>
    </ScrollView>
</ContentPage>
