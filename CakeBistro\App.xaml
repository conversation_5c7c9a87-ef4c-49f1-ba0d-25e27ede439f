<?xml version="1.0" encoding="utf-8"?>
<Application xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:converters="clr-namespace:CakeBistro.Converters"
             x:Class="CakeBistro.App">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="Resources/Styles/AppTheme.xaml" />
                <ResourceDictionary Source="Resources/Styles/DarkTheme.xaml" />
                <ResourceDictionary Source="Resources/Styles/Accessibility.xaml" />
                <ResourceDictionary Source="Resources/Animations/ButtonAnimations.xaml" />
            </ResourceDictionary.MergedDictionaries>
            <!-- Value Converters -->
            <converters:StockLevelColorConverter x:Key="StockLevelColorConverter" />
            <converters:StockLevelTextConverter x:Key="StockLevelTextConverter" />
            <converters:GreaterThanZeroConverter x:Key="GreaterThanZeroConverter" />
            <converters:NullToFalseConverter x:Key="NullToFalseConverter" />
            
            <!-- Global Styles -->
            <Style TargetType="Button" x:Key="PrimaryButton">
                <Setter Property="BackgroundColor" Value="#512BD4" />
                <Setter Property="TextColor" Value="White" />
                <Setter Property="CornerRadius" Value="8" />
                <Setter Property="Padding" Value="15,10" />
            </Style>
            
            <Style TargetType="Frame" x:Key="CardStyle">
                <Setter Property="BackgroundColor" Value="White" />
                <Setter Property="HasShadow" Value="True" />
                <Setter Property="CornerRadius" Value="10" />
                <Setter Property="Padding" Value="15" />
            </Style>
        </ResourceDictionary>
    </Application.Resources>
</Application>