using System;

namespace CakeBistro.Core.Models
{
    public class InventoryBatch : BaseEntity
    {
        public int Id { get; set; }
        public int RawMaterialId { get; set; }
        public string BatchNumber { get; set; } = string.Empty;
        public DateTime ProductionDate { get; set; }
        public DateTime ExpiryDate { get; set; }
        public int Quantity { get; set; }
        public decimal Cost { get; set; }
        public string StorageLocation { get; set; } = string.Empty;
        public string TraceabilityCode { get; set; } = string.Empty;
        public BatchStatus Status { get; set; }
    }

    public enum BatchStatus
    {
        InProgress,
        Completed,
        Expired
    }
}
