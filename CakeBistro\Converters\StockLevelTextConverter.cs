

// Create StockLevelTextConverter.cs
using System;
using System.Globalization;
using Microsoft.Maui.Controls;

namespace CakeBistro.Converters;

public class StockLevelTextConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is not CakeBistro.Core.Models.RawMaterial material)
            return string.Empty;
        
        if (material.CurrentStock < material.MinimumStock)
            return "CRITICAL LOW STOCK";
        
        if (material.CurrentStock < material.MinimumStock * 1.1m)
            return "Low Stock";
        
        return "Adequate Stock";
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        return "Unknown";
    }