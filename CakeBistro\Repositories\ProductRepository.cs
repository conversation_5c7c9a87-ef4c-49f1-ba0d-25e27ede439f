using CakeBistro.Core.Models;
using CakeBistro.Core.Interfaces;
using Microsoft.EntityFrameworkCore;
using System.Linq.Expressions;

namespace CakeBistro.Repositories
{
    public class ProductRepository : BaseRepository<Product>, IProductRepository
{
    public ProductRepository(CakeBistroContext context) : base(context)
    {
    }

    public async Task<IEnumerable<Product>> GetByCategoryAsync(string category)
    {
        return await _context.Products
            .Where(p => p.Category == category)
            .ToListAsync();
    }

    public async Task<IEnumerable<Product>> GetLowStockProductsAsync(int threshold = 10)
    {
        return await _context.Products
            .Where(p => p.CurrentStock < threshold)
            .ToListAsync();
    }
}
}
