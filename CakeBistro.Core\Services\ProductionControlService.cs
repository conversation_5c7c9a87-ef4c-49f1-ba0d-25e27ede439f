using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using CakeBistro.Core.Interfaces;
using CakeBistro.Core.Models;

namespace CakeBistro.Core.Services
{
    public class ProductionControlService
    {
        private readonly IInventoryBatchRepository _batchRepository;

        public ProductionControlService(IInventoryBatchRepository batchRepository)
        {
            _batchRepository = batchRepository;
        }

        public async Task<InventoryBatch> CreateBatchAsync(
            int rawMaterialId,
            string batchNumber,
            DateTime productionDate,
            DateTime expiryDate,
            int quantity,
            decimal cost,
            string storageLocation,
            string traceabilityCode)
        {
            var batch = new InventoryBatch
            {
                RawMaterialId = rawMaterialId,
                BatchNumber = batchNumber,
                ProductionDate = productionDate,
                ExpiryDate = expiryDate,
                Quantity = quantity,
                Cost = cost,
                StorageLocation = storageLocation,
                TraceabilityCode = traceabilityCode,
                Status = BatchStatus.InProgress
            };
            await _batchRepository.AddAsync(batch);
            return batch;
        }

        public async Task UpdateBatchStatusAsync(Guid batchId, BatchStatus newStatus)
        {
            var batch = await _batchRepository.GetByIdAsync(batchId);
            if (batch != null)
            {
                batch.Status = newStatus;
                await _batchRepository.UpdateAsync(batch);
            }
        }

        public async Task<IEnumerable<InventoryBatch>> GetBatchesExpiringSoonAsync(DateTime thresholdDate)
        {
            return await _batchRepository.GetExpiringBatchesAsync(thresholdDate);
        }

        public async Task<decimal> CalculateBatchCostAsync(Guid batchId)
        {
            var batch = await _batchRepository.GetByIdAsync(batchId);
            if (batch == null) return 0;
            // TODO: Add real cost calculation logic here
            return batch.Cost;
        }

        public async Task<bool> IsBatchExpiredAsync(Guid batchId)
        {
            var batch = await _batchRepository.GetByIdAsync(batchId);
            if (batch == null) return false;
            return batch.ExpiryDate < DateTime.UtcNow;
        }

        public async Task<IEnumerable<InventoryBatch>> GetTraceabilityBatchesAsync(string traceabilityCode)
        {
            var allBatches = await _batchRepository.GetAllAsync();
            return allBatches.Where(b => b.TraceabilityCode == traceabilityCode);
        }
    }
}
