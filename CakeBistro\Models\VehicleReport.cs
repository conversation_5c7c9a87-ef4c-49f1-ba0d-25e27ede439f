using System;
using System.Collections.Generic;

namespace CakeBistro.Models
{
    public class VehicleReport
    {
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public List<VehicleReportDetail> Details { get; set; } = new();
    }

    public class VehicleReportDetail
    {
        public int VehicleId { get; set; }
        public string? VehicleName { get; set; }
        public int TotalDeliveries { get; set; }
        public decimal TotalSales { get; set; }
    }
}
