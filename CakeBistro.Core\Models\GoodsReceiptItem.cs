using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace CakeBistro.Core.Models
{
    public class GoodsReceiptItem : BaseEntity
    {
        [Required]
        public Guid GoodsReceiptId { get; set; }
        [Required]
        public Guid PurchaseOrderItemId { get; set; }
        [Required]
        public decimal ReceivedQuantity { get; set; }
        public string BatchNumber { get; set; }
        public DateTime? ExpiryDate { get; set; }
        public bool IsAccepted { get; set; }
        public string RejectionReason { get; set; }
        [ForeignKey("GoodsReceiptId")]
        public GoodsReceipt GoodsReceipt { get; set; }
        [ForeignKey("PurchaseOrderItemId")]
        public PurchaseOrderItem PurchaseOrderItem { get; set; }
        public RawMaterial RawMaterial => PurchaseOrderItem?.RawMaterial;
    }
}
