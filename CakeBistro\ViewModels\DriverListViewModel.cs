using CakeBistro.Models;
using CakeBistro.Services;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.Collections.ObjectModel;
using System.Threading.Tasks;

namespace CakeBistro.ViewModels
{
    public partial class DriverListViewModel : ObservableObject
    {
        private readonly DriverService _driverService;

        [ObservableProperty]
        private ObservableCollection<Driver> drivers = new();
        [ObservableProperty]
        private string? newName;
        [ObservableProperty]
        private string? newPhone;
        [ObservableProperty]
        private string? newLicenseNumber;
        [ObservableProperty]
        private string? newNotes;
        [ObservableProperty]
        private Driver? selectedDriver;

        public DriverListViewModel(DriverService driverService)
        {
            _driverService = driverService;
        }

        [RelayCommand]
        public async Task LoadDriversAsync()
        {
            var list = await _driverService.GetAllDriversAsync();
            Drivers = new ObservableCollection<Driver>(list);
        }

        [RelayCommand]
        public async Task AddDriverAsync()
        {
            if (!string.IsNullOrWhiteSpace(NewName))
            {
                var driver = new Driver
                {
                    Name = NewName!,
                    Phone = NewPhone ?? string.Empty,
                    LicenseNumber = NewLicenseNumber ?? string.Empty,
                    Notes = NewNotes
                };
                await _driverService.CreateDriverAsync(driver);
                await LoadDriversAsync();
                NewName = null;
                NewPhone = null;
                NewLicenseNumber = null;
                NewNotes = null;
            }
        }

        [RelayCommand]
        public async Task RemoveDriverAsync()
        {
            if (SelectedDriver != null)
            {
                await _driverService.RemoveDriverAsync(SelectedDriver.Id);
                await LoadDriversAsync();
            }
        }
    }
}
