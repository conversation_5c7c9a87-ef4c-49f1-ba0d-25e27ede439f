<?xml version="1.0" encoding="utf-8" ?>
<ContentView xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="CakeBistro.Controls.CardView">
    <ContentView.Triggers>
        <EventTrigger Event="Appearing">
            <BeginStoryboard>
                <Storyboard>
                    <DoubleAnimation Storyboard.TargetProperty="Opacity" From="0" To="1" Duration="0:0:0.3" />
                    <DoubleAnimation Storyboard.TargetProperty="TranslationY" From="20" To="0" Duration="0:0:0.3" />
                    <DoubleAnimation Storyboard.TargetProperty="Scale" From="0.96" To="1" Duration="0:0:0.3" />
                </Storyboard>
            </BeginStoryboard>
        </EventTrigger>
    </ContentView.Triggers>
    <Frame x:Name="CardFrame"
           CornerRadius="16"
           Padding="16"
           BackgroundColor="{StaticResource Surface}"
           HasShadow="True"
           Margin="0,8,0,8"
           ShadowColor="#33000000"
           Elevation="8">
        <Frame.GestureRecognizers>
            <TapGestureRecognizer>
                <TapGestureRecognizer.Command>
                    <Command>
                        <Command.Behaviors>
                            <Behavior>
                                <Behavior.Triggers>
                                    <EventTrigger Event="Tapped">
                                        <BeginStoryboard>
                                            <Storyboard>
                                                <ColorAnimation Storyboard.TargetProperty="BackgroundColor" To="#e0e0e0" Duration="0:0:0.1" AutoReverse="True" />
                                                <DoubleAnimation Storyboard.TargetProperty="Elevation" To="16" Duration="0:0:0.1" AutoReverse="True" />
                                            </Storyboard>
                                        </BeginStoryboard>
                                    </EventTrigger>
                                </Behavior.Triggers>
                            </Behavior>
                        </Command.Behaviors>
                    </Command>
                </TapGestureRecognizer.Command>
            </TapGestureRecognizer>
        </Frame.GestureRecognizers>
        <ContentPresenter />
    </Frame>
</ContentView>
