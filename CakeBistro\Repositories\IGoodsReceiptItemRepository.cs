using CakeBistro.Core.Models;

namespace CakeBistro.Repositories
{
    public interface IGoodsReceiptItemRepository : IRepository<GoodsReceiptItem>
    {
        // Get all goods receipt items
        Task<IEnumerable<GoodsReceiptItem>> GetAllAsync();
        
        // Get goods receipt item by ID
        Task<GoodsReceiptItem> GetByIdAsync(Guid id);
        
        // Add a new goods receipt item
        Task AddAsync(GoodsReceiptItem item);
        
        // Update an existing goods receipt item
        Task UpdateAsync(GoodsReceiptItem item);
        
        // Delete a goods receipt item
        Task DeleteAsync(Guid id);
        
        // Get items for a specific goods receipt
        Task<IEnumerable<GoodsReceiptItem>> GetItemsByReceiptAsync(Guid receiptId);
        
        // Get items by purchase order item
        Task<IEnumerable<GoodsReceiptItem>> GetItemsByPurchaseOrderItemAsync(Guid purchaseOrderItemId);
    }
}
