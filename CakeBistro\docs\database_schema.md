# Database Schema

## 🗃️ Overview

The CakeBistro application uses a comprehensive database schema to manage all aspects of bakery operations. The schema has been fully implemented and tested with comprehensive unit, integration, and end-to-end tests.

## 📐 Entity Relationship Diagram

```mermaid
erDiagram
    Product ||--o{ StockAdjustment : "has"
    Product ||--o{ StockMovement : "has"
    Product ||--o{ SalesOrderItem : "has"
    Customer ||--o{ SalesOrder : "places"
    User ||--o{ SalesOrder : "processes"
    User ||--o{ StockAdjustment : "adjusts"
    User ||--o{ JournalEntry : "creates"
    User ||--o{ FixedAsset : "manages"
    SalesOrder ||--o{ Payment : "has"
    SalesOrder ||--o{ CreditNote : "has"
    SalesOrder ||--o{ DeliveryManifest : "fulfills"
    Vehicle ||--o{ DeliveryManifest : "assigned"
    Driver ||--o{ DeliveryManifest : "assigned"
    DeliveryManifest ||--o{ DeliveryStop : "includes"
    Route ||--o{ DeliveryStop : "used"
    CustomTheme ||--o{ ThemePreference : "user"
    SalesReport ||--o{ ReportSchedule : "scheduled"
    Account ||--o{ JournalEntryLine : "has"
    JournalEntry ||--o{ JournalEntryLine : "contains"
    FixedAsset ||--o{ DepreciationSchedule : "has"
    FixedAsset ||--o{ AssetDisposal : "disposed"
```

## 📚 Tables

### Inventory Management
- **Products**: Stores information about available products
  - Id (PK)
  - Name
  - Description
  - UnitPrice
  - CurrentStock
  - MinimumStockLevel
  - CreatedDate
  - UpdatedDate

- **StockAdjustments**: Records manual stock adjustments
  - Id (PK)
  - ProductId (FK)
  - Quantity (positive for increase, negative for decrease)
  - Reason
  - AdjustmentDate
  - UserId (FK)

- **StockMovements**: Tracks automatic stock movements
  - Id (PK)
  - ProductId (FK)
  - Quantity
  - MovementType (Inbound/Outbound)
  - Reason
  - Date
  - UserId (FK)

### Production Control
- **ProductionCosts**: Tracks production costs
  - Id (PK)
  - ProductId (FK)
  - RawMaterialId (FK)
  - QuantityUsed
  - CostPerUnit
  - TotalCost
  - DateProduced

- **Damages**: Records product damages
  - Id (PK)
  - ProductId (FK)
  - Quantity
  - Reason
  - DateRecorded
  - UserId (FK)

### Sales & Distribution
- **SalesOrders**: Main sales order table
  - Id (PK)
  - CustomerId (FK)
  - UserId (FK)
  - OrderNumber
  - OrderDate
  - SubTotal
  - DiscountAmount
  - TaxAmount
  - TotalAmount
  - PaymentMethod
  - PaymentReceived
  - CreatedDate
  - UpdatedDate

- **SalesOrderItems**: Individual items in a sales order
  - Id (PK)
  - SalesOrderId (FK)
  - ProductId (FK)
  - Quantity
  - UnitPrice
  - DiscountAmount
  - TotalPrice

- **Payments**: Records payments received
  - Id (PK)
  - SalesOrderId (FK)
  - UserId (FK)
  - Amount
  - PaymentMethod
  - PaymentDate
  - PaymentReference

- **CreditNotes**: Records returns and credits
  - Id (PK)
  - SalesOrderId (FK)
  - CreatedBy (FK)
  - IssueDate
  - Amount
  - Reason
  - AppliedToOrder

### Logistics
- **Vehicles**: Information about delivery vehicles
  - Id (PK)
  - RegistrationNumber
  - Make
  - Model
  - Year
  - Capacity
  - CreatedDate
  - UpdatedDate

- **Drivers**: Information about delivery drivers
  - Id (PK)
  - Name
  - LicenseNumber
  - ContactInformation
  - CreatedDate
  - UpdatedDate

- **DeliveryManifests**: Delivery plans
  - Id (PK)
  - VehicleId (FK)
  - DriverId (FK)
  - DateScheduled
  - Status
  - CreatedDate
  - UpdatedDate

- **DeliveryStops**: Individual stops on a delivery route
  - Id (PK)
  - DeliveryManifestId (FK)
  - CustomerId (FK)
  - Address
  - Sequence
  - Status
  - Notes

### Reporting
- **SalesReports**: Pre-generated sales reports
  - Id (PK)
  - ReportStartDate
  - ReportEndDate
  - TotalTransactions
  - TotalReturns
  - TotalCustomers
  - TotalProductsSold
  - GrossSalesTotal
  - DiscountsTotal
  - NetSalesBeforeTax
  - TaxTotal
  - NetSalesAfterTax
  - PaymentsReceived
  - ReturnsTotal
  - NetRevenue

- **ReportSchedules**: Scheduled report configurations
  - Id (PK)
  - ReportType
  - ScheduleFrequency
  - LastRunDate
  - NextRunDate
  - RecipientEmails
  - FormatOptions

### Theme Management
- **CustomThemes**: User-defined themes
  - Id (PK)
  - Name
  - PrimaryColor
  - SecondaryColor
  - BackgroundColor
  - TextColor
  - AccentColor
  - FontFamily
  - CreatedDate
  - UpdatedDate

- **ThemePreferences**: User theme preferences
  - Id (PK)
  - UserId (FK)
  - SelectedThemeId (FK)
  - UseSystemTheme
  - FontSize
  - HighContrastMode

The database schema reflects the complete implementation of all phases including comprehensive testing and validation.

## 🧪 TESTING INFRASTRUCTURE

### 🔍 Unit Testing ✅ COMPLETE
- Comprehensive unit tests for all models ✅ COMPLETE
- Validation rules testing complete ✅ COMPLETE
- Relationship validation complete ✅ COMPLETE
- Basic CRUD operation testing complete ✅ COMPLETE
- Data integrity verification complete ✅ COMPLETE

### 🔗 Integration Testing ✅ COMPLETE
- End-to-end service integration scenarios complete ✅ COMPLETE
- Complex query validation complete ✅ COMPLETE
- Transaction management testing complete ✅ COMPLETE
- Concurrency handling verification complete ✅ COMPLETE
- Performance benchmarking complete ✅ COMPLETE

### 🚀 End-to-End Testing ✅ COMPLETE
- Complete workflow validation from UI to database complete ✅ COMPLETE
- Complex scenario testing (bulk operations, multiple users) complete ✅ COMPLETE
- Stress testing with large datasets complete ✅ COMPLETE
- Security validation complete ✅ COMPLETE
- Audit trail verification complete ✅ COMPLETE

No change needed here - this block was already updated in the previous SEARCH/REPLACE operation
## FIXED ASSET ENTITIES

### FixedAsset
| Property | Type | Description |
|----------|------|-------------|
| Id | int | Unique identifier for the asset |
| Name | string | Name of the fixed asset |
| Description | string? | Detailed description of the asset |
| SerialNumber | string? | Manufacturer serial number |
| AcquisitionDate | DateTime | Date when the asset was acquired |
| Cost | decimal | Original cost of the asset |
| SalvageValue | decimal | Estimated residual value at end of life |
| UsefulLifeYears | int | Expected useful life in years |
| DepreciationMethod | string | Method used for depreciation calculation |
| DepartmentId | int | ID of the department using the asset |
| Location | string? | Physical location of the asset |
| Status | string | Current status (Active, Inactive, Disposed) |
| CreatedDate | DateTime | Date when the asset was registered |
| UpdatedDate | DateTime | Date when the asset details were last updated |

### DepreciationSchedule
| Property | Type | Description |
|----------|------|-------------|
| Id | int | Unique identifier for the schedule |
| AssetId | int | Reference to the fixed asset |
| ScheduleDate | DateTime | Date when the schedule was generated |
| PeriodStart | DateTime | Start date for the depreciation period |
| PeriodEnd | DateTime | End date for the depreciation period |
| OpeningBalance | decimal | Book value at beginning of period |
| DepreciationAmount | decimal | Amount of depreciation for the period |
| AccumulatedDepreciation | decimal | Total accumulated depreciation |
| ClosingBalance | decimal | Book value at end of period |
| CreatedDate | DateTime | Date when the schedule was generated |

### AssetDisposal
| Property | Type | Description |
|----------|------|-------------|
| Id | int | Unique identifier for the disposal record |
| AssetId | int | Reference to the disposed asset |
| DisposalDate | DateTime | Date when the asset was disposed |
| Proceeds | decimal | Amount received from disposal |
| DisposalMethod | string | Method of disposal (Sale, Retirement, Transfer) |
| Comments | string? | Additional comments about the disposal |
| UserId | int | ID of the user who processed the disposal |
| CreatedDate | DateTime | Date when the disposal was recorded |

## ACCOUNTING ENTITIES

### Account
| Property | Type | Description |
|----------|------|-------------|
| Id | int | Unique identifier for the account |
| Name | string | Name of the account |
| AccountType | string | Type of account (Asset, Liability, Equity, Income, Expense) |
| OpeningBalance | decimal | Opening balance at account creation |
| CreatedDate | DateTime | Date and time when the account was created |
| UpdatedDate | DateTime | Date and time when the account was last updated |

### JournalEntry
| Property | Type | Description |
|----------|------|-------------|
| Id | int | Unique identifier for the journal entry |
| Date | DateTime | Date of the transaction |
| Description | string | Description of the journal entry |
| Posted | bool | Indicates if the entry has been posted to the ledger |
| PostedDate | DateTime? | Date when the entry was posted |
| CreatedByUserId | int | User who created the entry |

### JournalEntryLine
| Property | Type | Description |
|----------|------|-------------|
| Id | int | Unique identifier for the line |
| AccountId | int | Reference to the account |
| Debit | decimal? | Debit amount |
| Credit | decimal? | Credit amount |
| Description | string? | Description of the line |
| JournalEntryId | int | Reference to the parent journal entry |

All database components have been thoroughly tested with comprehensive unit, integration, and end-to-end tests.