using Microsoft.Extensions.Hosting;
using System;
using System.Threading;
using System.Threading.Tasks;
using CakeBistro.Core.Models;
using CakeBistro.Core.Interfaces;

namespace CakeBistro.Services
{
    public class QualityCheckBackgroundService : BackgroundService
    {
        private readonly IQualityControlService _qualityControlService;
        private readonly INotificationService _notificationService;
        private readonly TimeSpan _interval = TimeSpan.FromMinutes(30); // configurable

        public QualityCheckBackgroundService(IQualityControlService qualityControlService, INotificationService notificationService)
        {
            _qualityControlService = qualityControlService;
            _notificationService = notificationService;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    var failedChecks = await _qualityControlService.GetFailedChecksAsync(DateTime.UtcNow.AddDays(-1));
                    foreach (var check in failedChecks)
                    {
                        await _notificationService.SendQualityCheckNotificationAsync(check);
                    }

                    var pendingChecks = await _qualityControlService.GetPendingChecksAsync();
                    foreach (var check in pendingChecks)
                    {
                        await _notificationService.SendQualityCheckNotificationAsync(check);
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"QualityCheckBackgroundService error: {ex.Message}");
                }
                await Task.Delay(_interval, stoppingToken);
            }
        }
    }
}
