using CakeBistro.Services;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace CakeBistro.ViewModels
{
    public partial class SupplierDetailViewModel : ObservableObject
    {
        private readonly IInventoryService _inventoryService;
        private readonly ILogger<SupplierDetailViewModel> _logger;
        private readonly RetryPolicy _retryPolicy;
        
        [ObservableProperty]
        private ObservableCollection<Supplier> suppliers = new();
        
        [ObservableProperty]
        private Supplier selectedSupplier = new();
        
        [ObservableProperty]
        private string statusMessage;

        private Color _statusColor = Colors.Transparent;
        public Color StatusColor
        {
            get => _statusColor;
            set => SetProperty(ref _statusColor, value);
        }

        public SupplierDetailViewModel(IInventoryService inventoryService, ILogger<SupplierDetailViewModel> logger, RetryPolicy retryPolicy)
        {
            _inventoryService = inventoryService;
            _logger = logger;
            _retryPolicy = retryPolicy;
            
            LoadSuppliersCommand = new AsyncRelayCommand(LoadSuppliersAsync);
            SaveSupplierCommand = new AsyncRelayCommand(SaveSupplierAsync);
            DeleteSupplierCommand = new AsyncRelayCommand(DeleteSupplierAsync);
        }
        
        private async Task LoadSuppliersAsync()
        {
            try
            {
                StatusMessage = "Loading suppliers...";
                StatusColor = Colors.Transparent;
                
                var result = await _retryPolicy.ExecuteAsync(
                    async () => await _inventoryService.GetSuppliersAsync(),
                    "LoadSuppliers"
                );
                
                Suppliers.Clear();
                foreach (var supplier in result.Items)
                {
                    Suppliers.Add(supplier);
                }
                
                StatusMessage = $"Loaded {result.TotalCount} suppliers.";
                StatusColor = Color.FromArgb("#388E3C");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading suppliers: {Message}", ex.Message);
                StatusMessage = $"Error: {ex.Message}";
                StatusColor = Color.FromArgb("#D32F2F");
            }
        }
        
        private async Task SaveSupplierCommand()
        {
            try
            {
                if (SelectedSupplier == null)
                {
                    _logger.LogWarning("Attempted to save null supplier");
                    StatusMessage = "Error: No supplier selected for saving";
                    StatusColor = Color.FromArgb("#D32F2F");
                    return;
                }
                
                if (string.IsNullOrWhiteSpace(SelectedSupplier.Name))
                {
                    _logger.LogWarning("Attempted to save supplier with empty name");
                    StatusMessage = "Error: Supplier name is required";
                    StatusColor = Color.FromArgb("#D32F2F");
                    return;
                }
                
                var result = await _inventoryService.SaveSupplierAsync(SelectedSupplier);
                StatusMessage = "Supplier saved successfully.";
                StatusColor = Color.FromArgb("#388E3C");
                await LoadSuppliersAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving supplier: {Message}", ex.Message);
                StatusMessage = $"Error: {ex.Message}";
                StatusColor = Color.FromArgb("#D32F2F");
            }
        }
    }
}