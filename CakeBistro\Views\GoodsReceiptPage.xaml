<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="MCakeBistro.Views.GoodsReceiptPage"
             Title="Goods Receipts">
    <VerticalStackLayout>
        <!-- Search and filter -->
        <HorizontalStackLayout Spacing="10" Padding="10">
            <Entry Placeholder="Search receipts..."
                   Text="{Binding SearchText}"
                   HorizontalOptions="FillAndExpand"/>
            
            <Picker ItemsSource="{Binding Statuses}"
                    SelectedItem="{Binding SelectedStatus}"
                    ItemDisplayPath="ToString()"
                    WidthRequest="150"/>
        </HorizontalStackLayout>
        
        <!-- Receipt list -->
        <CollectionView ItemsSource="{Binding GoodsReceipts}"
                        SelectionMode="Single"
                        SelectedItem="{Binding SelectedReceipt}">
            <CollectionView.ItemTemplate>
                <DataTemplate>
                    <Grid Padding="10">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        
                        <VerticalStackLayout>
                            <Label Text="{Binding ReceiptNumber}"
                                   FontAttributes="Bold"/>
                            <Label Text="{Binding PurchaseOrder.Supplier.Name}"
                                   TextColor="Gray"/>
                            <Label Text="{Binding ReceiptDate, StringFormat='{0:MMM d, yyyy}'}"/>
                        </VerticalStackLayout>
                        
                        <Label Grid.Column="1"
                               Text="{Binding Status}"
                               HorizontalOptions="End"
                               VerticalOptions="Center"
                               FontAttributes="Bold"/>
                    </Grid>
                </DataTemplate>
            </CollectionView.ItemTemplate>
        </CollectionView>
        
        <!-- Action buttons -->
        <HorizontalStackLayout Spacing="10" Padding="10">
            <Button Text="New Receipt"
                    Command="{Binding NewReceiptCommand}"
                    HorizontalOptions="FillAndExpand"/>
            <Button Text="View Details"
                    Command="{Binding ViewReceiptDetailsCommand}"
                    CommandParameter="{Binding SelectedReceipt}"
                    HorizontalOptions="FillAndExpand"/>
            <Button Text="Refresh"
                    Command="{Binding RefreshCommand}"
                    HorizontalOptions="FillAndExpand"/>
        </HorizontalStackLayout>
    </VerticalStackLayout>
</ContentPage>