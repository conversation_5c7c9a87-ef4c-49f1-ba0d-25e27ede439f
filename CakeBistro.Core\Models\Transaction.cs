namespace CakeBistro.Core.Models
{
    public class Transaction
    {
        public int Id { get; set; }
        
        /// <summary>
        /// Gets or sets the type of transaction (e.g., Sale, Return, Exchange, Discount, FuelCost).
        /// </summary>
        public string Type { get; set; }
        
        /// <summary>
        /// Gets or sets the amount of the transaction.
        /// Positive values represent inflows (e.g., sales), negative values represent outflows (e.g., returns).
        /// </summary>
        public decimal Amount { get; set; }
        
        /// <summary>
        /// Gets or sets the date and time when the transaction occurred.
        /// Defaults to current time if not specified.
        /// </summary>
        public DateTime Date { get; set; } = DateTime.UtcNow;
        
        /// <summary>
        /// Gets or sets a description of the transaction.
        /// </summary>
        public string? Description { get; set; }
        
        /// <summary>
        /// Gets or sets the ID of the cashier/user who processed the transaction.
        /// </summary>
        public int CashierId { get; set; }
        
        /// <summary>
        /// Gets or sets the customer associated with the transaction.
        /// May be null for some transaction types (e.g., fuel cost).
        /// </summary>
        public int? CustomerId { get; set; }
        
        /// <summary>
        /// Gets or sets the order ID if this transaction is related to an order.
        /// </summary>
        public int? OrderId { get; set; }
        
        /// <summary>
        /// Gets or sets the payment method used for this transaction.
        /// (e.g., Cash, Credit Card, Mobile Payment, Account)
        /// </summary>
        public string? PaymentMethod { get; set; }
        
        // Navigation properties
        public virtual User? Cashier { get; set; }
        public virtual Customer? Customer { get; set; }
        public virtual SalesOrder? Order { get; set; }
    }
}