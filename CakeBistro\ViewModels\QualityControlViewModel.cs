using System.Threading.Tasks;
using System.Windows.Input;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using CakeBistro.Services;
using Microsoft.Maui.Controls;

namespace CakeBistro.ViewModels
{
    public partial class QualityControlViewModel : ObservableObject
    {
        public IRelayCommand GenerateReportCommand { get; }

        public QualityControlViewModel()
        {
            GenerateReportCommand = new AsyncRelayCommand(OnGenerateReport);
        }

        private async Task OnGenerateReport()
        {
            await Shell.Current.GoToAsync("QualityCheckReportPage");
        }

        public async Task Initialize()
        {
            // Initialization logic for the page
        }
    }
}
