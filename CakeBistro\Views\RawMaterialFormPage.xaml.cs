using System;
using System.Threading.Tasks;
using CakeBistro.ViewModels;
using CakeBistro.Core.Interfaces;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Maui.Controls;

namespace CakeBistro.Views
{
    [QueryProperty(nameof(RawMaterialId), "id")]
    public partial class RawMaterialFormPage : ContentPage
    {
        public int? RawMaterialId { get; set; }
        private RawMaterialFormViewModel _viewModel;
        public RawMaterialFormPage()
        {
            InitializeComponent();
            var inventoryService = MauiProgram.ServiceProvider.GetService<IInventoryService>();
            var rawMaterialService = MauiProgram.ServiceProvider.GetService<IRawMaterialService>();
            var supplierService = MauiProgram.ServiceProvider.GetService<ISupplierService>();
            _viewModel = new RawMaterialFormViewModel(inventoryService, rawMaterialService, supplierService);
            BindingContext = _viewModel;
        }

        protected override async void OnAppearing()
        {
            base.OnAppearing();
            if (RawMaterialId.HasValue)
            {
                await _viewModel.LoadAsync(RawMaterialId.Value);
            }
        }
    }
}
