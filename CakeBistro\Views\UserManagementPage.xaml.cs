using CakeBistro.ViewModels;
using Microsoft.Maui.Controls;

namespace CakeBistro.Views
{
    public partial class UserManagementPage : ContentPage
    {
        private readonly UserManagementViewModel _viewModel;
        
        public UserManagementPage(UserManagementViewModel viewModel)
        {
            InitializeComponent();
            _viewModel = viewModel;
            BindingContext = _viewModel;
            
            // Subscribe to messaging center events
            MessagingCenter.Subscribe<UserManagementViewModel, string>(this, "NavigateTo", (sender, args) =>
            {
                // Handle navigation requests from view model
                if (args == "MainPage")
                {
                    // Navigate to main page
                    Shell.Current.GoToAsync("//MainPage");
                }
            });
        }
        
        protected override async void OnAppearing()
        {
            base.OnAppearing();
            
            // Load users when page appears
            await _viewModel.LoadUsersCommand.ExecuteAsync(null);
        }
        
        protected override void OnDisappearing()
        {
            base.OnDisappearing();
            // Unsubscribe from events when page is not visible
            MessagingCenter.Unsubscribe<UserManagementViewModel, string>(this, "NavigateTo");
        }
    }
}
