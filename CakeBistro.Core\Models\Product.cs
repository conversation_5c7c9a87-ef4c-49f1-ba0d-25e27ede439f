using System.Linq;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace CakeBistro.Core.Models
{
    public class Product : BaseEntity
    {
        [Key]
        public new int Id { get; set; }
        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;
        [Range(0, double.MaxValue)]
        public decimal Price { get; set; }
        public string Description { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public string Sku { get; set; } = string.Empty;
        public int StockQuantity { get; set; }
        // Navigation properties
        public ICollection<SalesOrderItem> SalesOrderItems { get; set; } = new List<SalesOrderItem>();

        // Computed property for reporting
        public int TotalSold => SalesOrderItems?.Sum(item => item.Quantity) ?? 0;
    }
}
