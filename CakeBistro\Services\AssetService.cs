// Update AssetService.cs
global using Microsoft.EntityFrameworkCore;
using CakeBistro.Core.Models;
using CakeBistro.Models;
using CakeBistro.Repositories;

namespace CakeBistro.Services;

/// <summary>
/// Service class implementing IAssetService interface to manage fixed assets using repository pattern
/// </summary>
public class AssetService : IAssetService
{
    private readonly IRepository<FixedAsset> _assetRepository;
    private readonly IRepository<InventoryBatch> _batchRepository;
    private readonly IRepository<DepreciationRecord> _depreciationRepository;
    private readonly IRepository<MaintenanceRecord> _maintenanceRepository;

    /// <summary>
    /// Initializes a new instance of the <see cref="AssetService"/> class.
    /// </summary>
    public AssetService(
        IRepository<FixedAsset> assetRepository,
        IRepository<InventoryBatch> batchRepository,
        IRepository<DepreciationRecord> depreciationRepository,
        IRepository<MaintenanceRecord> maintenanceRepository)
    {
        _assetRepository = assetRepository;
        _batchRepository = batchRepository;
        _depreciationRepository = depreciationRepository;
        _maintenanceRepository = maintenanceRepository;
    }

    /// <inheritdoc/>
    public async Task<IEnumerable<FixedAsset>> GetAllAssetsAsync()
    {
        return await _assetRepository.GetAllAsync();
    }

    /// <inheritdoc/>
    public async Task<FixedAsset> GetAssetByIdAsync(int id)
    {
        var asset = await _assetRepository.GetByIdAsync(id);
        if (asset == null)
        {
            throw new KeyNotFoundException($"Asset with ID {id} not found.");
        }
        
        return asset;
    }

    /// <inheritdoc/>
    public async Task<FixedAsset> AddAssetAsync(FixedAsset asset)
    {
        // Validate input
        if (asset == null)
            throw new ArgumentNullException(nameof(asset), "Asset cannot be null");

        if (string.IsNullOrWhiteSpace(asset.Name))
            throw new ArgumentException("Asset name is required", nameof(asset.Name));

        if (string.IsNullOrWhiteSpace(asset.Category))
            throw new ArgumentException("Asset category is required", nameof(asset.Category));

        if (string.IsNullOrWhiteSpace(asset.Type))
            throw new ArgumentException("Asset type is required", nameof(asset.Type));

        if (asset.AcquisitionCost <= 0)
            throw new ArgumentException("Acquisition cost must be greater than zero", nameof(asset.AcquisitionCost));

        if (asset.LifeExpectancyYears <= 0)
            throw new ArgumentException("Life expectancy must be greater than zero", nameof(asset.LifeExpectancyYears));

        if (asset.DepreciationRate <= 0 || asset.DepreciationRate > 1)
            throw new ArgumentException("Depreciation rate must be between 0 and 1", nameof(asset.DepreciationRate));

        try
        {
            // Set default values if not provided
            asset.AcquisitionDate ??= DateTime.Today;
            asset.CurrentValue ??= asset.AcquisitionCost;
            asset.Status ??= "Active";
            
            // Validate location if provided
            if (!string.IsNullOrWhiteSpace(asset.Location))
            {
                var validLocations = await GetValidAssetLocationsAsync();
                if (!validLocations.Contains(asset.Location))
                {
                    throw new ArgumentException($"Invalid location. Valid locations are: {string.Join(", ", validLocations)}");
                }
            }
            
            // Save the asset
            return await _assetRepository.AddAsync(asset);
        }
        catch (Exception ex)
        {
            // Log the error (in a real application)
            // For now, just rethrow with a more descriptive message
            throw new ApplicationException($"Error adding asset: {ex.Message}", ex);
        }
    }

    private async Task<List<string>> GetValidAssetLocationsAsync()
    {
        // In a real application, this would come from configuration or database
        // For now, we'll use some hardcoded valid locations
        return new List<string>
        {
            "Warehouse",
            "Main Store",
            "Production Area",
            "Office",
            "Maintenance"
        };
    }

    /// <inheritdoc/>
    public async Task UpdateAssetAsync(FixedAsset asset)
    {
        // Validate input
        if (asset == null)
        {
            throw new ArgumentNullException(nameof(asset));
        }

        // Ensure we have a valid ID
        if (asset.Id <= 0)
        {
            throw new ArgumentException("Invalid asset ID", nameof(asset.Id));
        }
        
        await _assetRepository.UpdateAsync(asset);
    }

    /// <inheritdoc/>
    public async Task DeleteAssetAsync(int id)
    {
        if (id <= 0)
        {
            throw new ArgumentException("Invalid asset ID", nameof(id));
        }
        
        await _assetRepository.DeleteAsync(id);
    }

    /// <inheritdoc/>
    public async Task<InventoryBatch> UpdateLocationAsync(AssetLocationHistory history)
    {
        // Validate input
        if (history == null)
        {
            throw new ArgumentNullException(nameof(history));
        }

        // Get the current inventory batch
        var batch = await _batchRepository.GetByIdAsync(history.AssetId);
        if (batch == null)
        {
            throw new ArgumentException("Invalid asset ID", nameof(history.AssetId));
        }

        // Update location
        batch.Location = history.NewLocation;
        
        // Save changes
        await _batchRepository.UpdateAsync(batch);
        
        return batch;
    }

    /// <inheritdoc/>
    public async Task<DepreciationRecord> RecordDepreciationAsync(DepreciationRecord record)
    {
        // Validate input
        if (record == null)
        {
            throw new ArgumentNullException(nameof(record));
        }

        // Get the associated asset
        var asset = await _assetRepository.GetByIdAsync(record.AssetId);
        if (asset == null)
        {
            throw new ArgumentException("Invalid asset ID", nameof(record.AssetId));
        }

        // Calculate depreciation amount if not provided
        if (record.Amount == 0)
        {
            // For simplicity, we're using straight-line depreciation over 5 years
            record.Amount = asset.AcquisitionCost / 5; // Simple annual depreciation
        }

        // Set default date if not provided
        record.DepreciationDate ??= DateTime.Today;
        
        // Save the depreciation record
        return await _depreciationRepository.AddAsync(record);
    }

    /// <inheritdoc/>
    public async Task<MaintenanceRecord> ScheduleMaintenanceAsync(MaintenanceRecord schedule)
    {
        // Validate input
        if (schedule == null)
        {
            throw new ArgumentNullException(nameof(schedule));
        }

        // Set default values if not provided
        schedule.ScheduledDate ??= DateTime.Today.AddDays(7);
        schedule.Status ??= "Scheduled";
        
        // Save the schedule
        return await _maintenanceRepository.AddAsync(schedule);
    }

    /// <inheritdoc/>
    public async Task<MaintenanceRecord> PerformMaintenanceAsync(MaintenanceRecord record)
    {
        // Validate input
        if (record == null)
        {
            throw new ArgumentNullException(nameof(record));
        }

        // Get the existing maintenance record if it exists
        if (record.Id > 0)
        {
            var existingRecord = await _maintenanceRepository.GetByIdAsync(record.Id);
            if (existingRecord != null)
            {
                // Update existing record with new information
                existingRecord.ActualDate = record.ActualDate ?? DateTime.Now;
                existingRecord.Description = record.Description;
                existingRecord.Cost = record.Cost;
                existingRecord.Status = "Completed";
                
                await _maintenanceRepository.UpdateAsync(existingRecord);
                return existingRecord;
            }
        }

        // If no ID is provided or record doesn't exist, create a new completed record
        record.ActualDate ??= DateTime.Now;
        record.Status = "Completed";
        return await _maintenanceRepository.AddAsync(record);
    }

    /// <inheritdoc/>
    public async Task<LocationReport> GetLocationReportAsync(string location)
    {
        if (string.IsNullOrWhiteSpace(location))
        {
            throw new ArgumentException("Location cannot be empty", nameof(location));
        }

        var assets = await ((IAssetRepository)_assetRepository).GetByLocationAsync(location);
        
        return new LocationReport
        {
            Location = location,
            AssetCount = assets.Count(),
            TotalAssetValue = assets.Sum(a => a.AcquisitionCost - a.AccumulatedDepreciation),
            Assets = assets.ToList()
        };
    }

    /// <inheritdoc/>
    public async Task<SalesSummary> GetAssetUtilizationAsync(int assetId)
    {
        if (assetId <= 0)
        {
            throw new ArgumentException("Invalid asset ID", nameof(assetId));
        }

        // Implementation would go here
        return new SalesSummary();
    }

    /// <inheritdoc/>
    public async Task<AssetRegister> GetAssetRegisterAsync()
    {
        var allAssets = await _assetRepository.GetAllAsync();
        
        return new AssetRegister
        {
            TotalAssets = allAssets.Count(),
            TotalValue = allAssets.Sum(a => a.AcquisitionCost),
            NetBookValue = allAssets.Sum(a => a.AcquisitionCost - a.AccumulatedDepreciation),
            ByCategory = allAssets.GroupBy(a => a.Category)
                .ToDictionary(g => g.Key, g => g.Sum(a => a.AcquisitionCost - a.AccumulatedDepreciation)),
            DepreciationSchedule = await _depreciationRepository.GetAllAsync()
        };
    }

    /// <inheritdoc/>
    public async Task<decimal> CalculateNetBookValueAsync(int assetId)
    {
        var asset = await _assetRepository.GetByIdAsync(assetId);
        if (asset == null)
        {
            return 0;
        }

        var depreciationRecords = await _depreciationRepository.GetAllAsync();
        var totalDepreciation = depreciationRecords
            .Where(d => d.AssetId == assetId)
            .Sum(d => d.Amount);

        return asset.AcquisitionCost - totalDepreciation;
    }

    /// <inheritdoc/>
    public async Task<IEnumerable<FixedAsset>> GetExpiringAssetsAsync(int days = 30)
    {
        if (days <= 0)
        {
            throw new ArgumentException("Days must be a positive number", nameof(days));
        }

        var expiringDate = DateTime.Today.AddDays(days);
        return await ((IAssetRepository)_assetRepository).GetExpiringStockAsync(expiringDate, days);
    }

    /// <inheritdoc/>
    public async Task<(bool Success, string Message)> TransferAssetAsync(int assetId, string newLocation, string reason = "")
    {
        try
        {
            // Validate inputs
            if (assetId <= 0)
            {
                throw new ArgumentException("Invalid asset ID", nameof(assetId));
            }

            if (string.IsNullOrWhiteSpace(newLocation))
            {
                throw new ArgumentException("New location cannot be empty", nameof(newLocation));
            }

            // Get the asset
            var asset = await _assetRepository.GetByIdAsync(assetId);
            if (asset == null)
            {
                throw new KeyNotFoundException($"Asset with ID {assetId} not found.");
            }

            // Create location history record
            var history = new AssetLocationHistory
            {
                AssetId = assetId,
                OldLocation = asset.Location,
                NewLocation = newLocation,
                ChangeDate = DateTime.Now,
                Notes = reason
            };

            // Update asset location
            asset.Location = newLocation;
            
            // Save changes
            await _assetRepository.UpdateAsync(asset);
            
            return (true, "Asset transferred successfully");
        }
        catch (Exception ex)
        {
            return (false, $"Error transferring asset: {ex.Message}");
        }
    }

    /// <inheritdoc/>
    public async Task<(bool Success, string Message)> DisposeAssetAsync(int assetId, decimal salePrice = 0, string reason = "")
    {
        try
        {
            // Validate inputs
            if (assetId <= 0)
            {
                throw new ArgumentException("Invalid asset ID", nameof(assetId));
            }

            // Get the asset
            var asset = await _assetRepository.GetByIdAsync(assetId);
            if (asset == null)
            {
                throw new KeyNotFoundException($"Asset with ID {assetId} not found.");
            }

            // Calculate net book value
            var netBookValue = await CalculateNetBookValueAsync(assetId);
            
            // Create disposal record
            var disposal = new AssetDisposal
            {
                AssetId = assetId,
                DisposalDate = DateTime.Now,
                SalePrice = salePrice,
                Reason = reason,
                NetBookValue = netBookValue,
                GainLoss = salePrice - netBookValue
            };
            
            // Remove from active assets
            await _assetRepository.DeleteAsync(assetId);
            
            return (true, $"Asset disposed successfully. Gain/Loss: {disposal.GainLoss:C2}");
        }
        catch (Exception ex)
        {
            return (false, $"Error disposing asset: {ex.Message}");
        }
    }

    public async Task<AssetRegisterResult> RegisterAssetAsync(Asset asset)
    {
        // TODO: Implement asset registration logic
        return new AssetRegisterResult();
    }

    public async Task<DepreciationResult> CalculateDepreciationAsync(int assetId, DateTime asOfDate)
    {
        // TODO: Implement depreciation calculation logic
        return new DepreciationResult();
    }

    public async Task<MaintenanceResult> TrackAssetMaintenanceAsync(int assetId, MaintenanceRecord record)
    {
        // TODO: Implement maintenance tracking logic
        return new MaintenanceResult();
    }

    public async Task<AssetReport> GenerateAssetReportAsync(DateTime startDate, DateTime endDate)
    {
        // TODO: Implement asset reporting logic
        return new AssetReport();
    }
}
