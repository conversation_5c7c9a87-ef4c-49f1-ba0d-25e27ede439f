using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using System;
using System.Net.Http;
using System.Threading.Tasks;

namespace CakeBistro.Services
{
    public class RetryPolicy
    {
        private readonly ILogger<RetryPolicy> _logger;
        private readonly int _maxRetries;
        private readonly TimeSpan _retryDelay;
        private readonly TimeSpan _maxBackoff;
        private readonly bool _useExponentialBackoff;

        public RetryPolicy(ILogger<RetryPolicy> logger, int maxRetries = 3, TimeSpan? retryDelay = null, bool useExponentialBackoff = true, TimeSpan? maxBackoff = null)
        {
            _logger = logger;
            _maxRetries = maxRetries;
            _retryDelay = retryDelay ?? TimeSpan.FromSeconds(1);
            _useExponentialBackoff = useExponentialBackoff;
            _maxBackoff = maxBackoff ?? TimeSpan.FromSeconds(10);
        }

        public async Task<T> ExecuteAsync<T>(Func<Task<T>> action, string operationName = "unknown")
        {
            var retryCount = 0;
            
            while (true)
            {
                try
                {
                    return await action();
                }
                catch (Exception ex) when (IsTransientException(ex))
                {
                    retryCount++;
                    
                    if (retryCount >= _maxRetries)
                    {
                        _logger.LogError(ex, "Operation {OperationName} failed after {RetryCount} retries: {Message}", 
                            operationName, retryCount, ex.Message);
                        throw;
                    }
                    
                    // Calculate delay with exponential backoff if configured
                    var delay = _useExponentialBackoff 
                        ? CalculateExponentialBackoff(retryCount) 
                        : _retryDelay;
                    
                    _logger.LogWarning("Transient error in operation {OperationName}. Retrying... Attempt {RetryCount} of {_maxRetries}. Error: {Message}. Delaying for {Delay} ms", 
                        operationName, retryCount, _maxRetries, ex.Message, delay.TotalMilliseconds);
                    
                    await Task.Delay(delay);
                }
            }
        }

        public async Task ExecuteAsync(Func<Task> action, string operationName = "unknown")
        {
            var retryCount = 0;
            
            while (true)
            {
                try
                {
                    await action();
                    return;
                }
                catch (Exception ex) when (IsTransientException(ex))
                {
                    retryCount++;
                    
                    if (retryCount >= _maxRetries)
                    {
                        _logger.LogError(ex, "Operation {OperationName} failed after {RetryCount} retries: {Message}", 
                            operationName, retryCount, ex.Message);
                        throw;
                    }
                    
                    // Calculate delay with exponential backoff if configured
                    var delay = _useExponentialBackoff 
                        ? CalculateExponentialBackoff(retryCount) 
                        : _retryDelay;
                    
                    _logger.LogWarning("Transient error in operation {OperationName}. Retrying... Attempt {RetryCount} of {_maxRetries}. Error: {Message}. Delaying for {Delay} ms", 
                        operationName, retryCount, _maxRetries, ex.Message, delay.TotalMilliseconds);
                    
                    await Task.Delay(delay);
                }
            }
        }

        private TimeSpan CalculateExponentialBackoff(int retryCount)
        {
            // Calculate exponential backoff with jitter to avoid thundering herd problem
            var baseDelay = Math.Pow(2, retryCount) * _retryDelay.TotalMilliseconds;
            
            // Add random jitter between 0-20% of base delay
            var jitter = new Random().NextDouble() * (baseDelay * 0.2);
            
            var totalDelay = baseDelay + jitter;
            
            // Ensure we don't exceed max backoff
            return TimeSpan.FromMilliseconds(Math.Min(totalDelay, _maxBackoff.TotalMilliseconds));
        }

        private bool IsTransientException(Exception ex)
        {
            // Implement logic to determine if the exception is transient
            // For database operations, consider:
            // - Network-related exceptions
            // - Timeout exceptions
            // - Connection exceptions
            // - Deadlock exceptions
            // - SQL Server specific error codes
            
            if (ex is Microsoft.EntityFrameworkCore.DbUpdateException ||
                (ex.InnerException is System.Data.Common.DbException))
            {
                return true;
            }
            
            // Add more conditions as needed for your specific use cases
            
            return false;
        }
    }
}