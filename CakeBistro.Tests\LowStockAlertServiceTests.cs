
using System;
using System.Linq;
using System.Threading.Tasks;
using CakeBistro.Models;
using CakeBistro.Services;
using Microsoft.EntityFrameworkCore;
using Xunit;

namespace CakeBistro.Tests
{
    public class LowStockAlertServiceTests
    {
        private readonly CakeBistroContext _context;
        private readonly ILowStockAlertService _lowStockAlertService;

        public LowStockAlertServiceTests()
        {
            // Set up in-memory database for testing
            var options = new DbContextOptionsBuilder<CakeBistroContext>()
                .UseInMemoryDatabase(databaseName: $"LowStockAlertTestDb_{Guid.NewGuid()}")
                .Options;

            _context = new CakeBistroContext(options);
            _lowStockAlertService = new LowStockAlertService(_context);
        }

        [Fact]
        public async Task CheckLowStockAsync_ItemsBelowThreshold_GeneratesLowStockWarnings()
        {
            // Arrange
            // Add raw materials with low stock
            var rawMaterial1 = new RawMaterial
            {
                Name = "Flour",
                CurrentStock = 5,  // Below minimum threshold (default threshold is 20%)
                MinimumStockThreshold = 50,
                PricePerUnit = 2.5m,
                Unit = "kg"
            };
            
            var rawMaterial2 = new RawMaterial
            {
                Name = "Sugar",
                CurrentStock = 10,  // Exactly at minimum threshold (not below)
                MinimumStockThreshold = 10,
                PricePerUnit = 1.5m,
                Unit = "kg"
            };
            
            var rawMaterial3 = new RawMaterial
            {
                Name = "Eggs",
                CurrentStock = 15,  // Above minimum threshold
                MinimumStockThreshold = 10,
                PricePerUnit = 3.0m,
                Unit = "dozen"
            };
            
            await _context.RawMaterials.AddRangeAsync(rawMaterial1, rawMaterial2, rawMaterial3);
            
            // Add finished products with low stock
            var finishedProduct1 = new FinishedProduct
            {
                Name = "Chocolate Cake",
                CurrentStock = 2,  // Below minimum threshold
                MinimumStockThreshold = 10,
                SalePrice = 25.0m
            };
            
            var finishedProduct2 = new FinishedProduct
            {
                Name = "Vanilla Cake",
                CurrentStock = 5,  // Exactly at minimum threshold
                MinimumStockThreshold = 5,
                SalePrice = 20.0m
            };
            
            var finishedProduct3 = new FinishedProduct
            {
                Name = "Strawberry Cake",
                CurrentStock = 8,  // Above minimum threshold
                MinimumStockThreshold = 5,
                SalePrice = 22.0m
            };
            
            await _context.FinishedProducts.AddRangeAsync(finishedProduct1, finishedProduct2, finishedProduct3);
            
            await _context.SaveChangesAsync();

            // Act
            var result = await _lowStockAlertService.CheckLowStockAsync();

            // Assert
            Assert.NotNull(result);
            
            // Verify we have warnings for items below threshold
            Assert.Contains(result, r => r.ItemId == rawMaterial1.Id && r.ItemType == "RawMaterial");
            Assert.Contains(result, r => r.ItemId == finishedProduct1.Id && r.ItemType == "FinishedProduct");
            
            // Verify we don't have warnings for items at or above threshold
            Assert.DoesNotContain(result, r => r.ItemId == rawMaterial2.Id && r.ItemType == "RawMaterial");
            Assert.DoesNotContain(result, r => r.ItemId == rawMaterial3.Id && r.ItemType == "RawMaterial");
            Assert.DoesNotContain(result, r => r.ItemId == finishedProduct2.Id && r.ItemType == "FinishedProduct");
            Assert.DoesNotContain(result, r => r.ItemId == finishedProduct3.Id && r.ItemType == "FinishedProduct");
            
            // Verify percentage calculations
            var flourWarning = result.FirstOrDefault(r => r.ItemId == rawMaterial1.Id);
            Assert.NotNull(flourWarning);
            Assert.Equal(10.0, flourWarning.PercentageRemaining);  // 5 / 50 * 100 = 10%
            
            var cakeWarning = result.FirstOrDefault(r => r.ItemId == finishedProduct1.Id);
            Assert.NotNull(cakeWarning);
            Assert.Equal(20.0, cakeWarning.PercentageRemaining);  // 2 / 10 * 100 = 20%
            
            // Verify alerts were created
            var alerts = await _context.LowStockAlerts.Where(a => !a.IsResolved).ToListAsync();
            Assert.NotNull(alerts);
            Assert.Equal(2, alerts.Count);
            
            // Verify alert details
            var flourAlert = alerts.FirstOrDefault(a => a.ItemId == rawMaterial1.Id);
            Assert.NotNull(flourAlert);
            Assert.Equal(rawMaterial1.CurrentStock, flourAlert.CurrentStock);
            Assert.Equal(rawMaterial1.MinimumStockThreshold, flourAlert.MinimumStock);
            Assert.False(flourAlert.IsResolved);
            Assert.Null(flourAlert.ResolvedDate);
            Assert.False(flourAlert.AlertAcknowledged);
            Assert.Contains(flourAlert.AlertMessage, rawMaterial1.Name);
            
            var cakeAlert = alerts.FirstOrDefault(a => a.ItemId == finishedProduct1.Id);
            Assert.NotNull(cakeAlert);
            Assert.Equal(finishedProduct1.CurrentStock, cakeAlert.CurrentStock);
            Assert.Equal(finishedProduct1.MinimumStockThreshold, cakeAlert.MinimumStock);
            Assert.False(cakeAlert.IsResolved);
            Assert.Null(cakeAlert.ResolvedDate);
            Assert.False(cakeAlert.AlertAcknowledged);
            Assert.Contains(cakeAlert.AlertMessage, finishedProduct1.Name);
        }

        [Fact]
        public async Task GetActiveAlertsAsync_ActiveAlerts_ReturnsUnresolvedAlerts()
        {
            // Arrange
            // Create some test alerts
            var alert1 = new LowStockAlert
            {
                ItemId = 1,
                ItemType = "RawMaterial",
                CurrentStock = 5,
                MinimumStock = 50,
                TriggeredDate = DateTime.UtcNow.AddDays(-1),
                IsResolved = false
            };
            
            var alert2 = new LowStockAlert
            {
                ItemId = 2,
                ItemType = "FinishedProduct",
                CurrentStock = 2,
                MinimumStock = 10,
                TriggeredDate = DateTime.UtcNow.AddDays(-2),
                IsResolved = false
            };
            
            var resolvedAlert = new LowStockAlert
            {
                ItemId = 3,
                ItemType = "RawMaterial",
                CurrentStock = 1,
                MinimumStock = 5,
                TriggeredDate = DateTime.UtcNow.AddDays(-3),
                IsResolved = true,
                ResolvedDate = DateTime.UtcNow
            };
            
            await _context.LowStockAlerts.AddRangeAsync(alert1, alert2, resolvedAlert);
            await _context.SaveChangesAsync();

            // Act
            var result = await _lowStockAlertService.GetActiveAlertsAsync();

            // Assert
            Assert.NotNull(result);
            Assert.Equal(2, result.Count);
            
            // Verify contains active alerts
            Assert.Contains(result, a => a.Id == alert1.Id);
            Assert.Contains(result, a => a.Id == alert2.Id);
            
            // Verify does not contain resolved alerts
            Assert.DoesNotContain(result, a => a.Id == resolvedAlert.Id);
            
            // Verify order by date (newest first)
            Assert.Equal(alert2.Id, result.First().Id);
            Assert.Equal(alert1.Id, result.Last().Id);
        }

        [Fact]
        public async Task AcknowledgeAlertAsync_ValidAlert_MarksAsResolved()
        {
            // Arrange
            // Create a test alert
            var alert = new LowStockAlert
            {
                ItemId = 1,
                ItemType = "RawMaterial",
                CurrentStock = 5,
                MinimumStock = 50,
                TriggeredDate = DateTime.UtcNow,
                IsResolved = false
            };
            
            await _context.LowStockAlerts.AddAsync(alert);
            await _context.SaveChangesAsync();

            // Act
            var result = await _lowStockAlertService.AcknowledgeAlertAsync(alert.Id);

            // Assert
            Assert.True(result);
            
            // Verify alert was updated
            var updatedAlert = await _context.LowStockAlerts.FindAsync(alert.Id);
            Assert.NotNull(updatedAlert);
            Assert.True(updatedAlert.IsResolved);
            Assert.NotNull(updatedAlert.ResolvedDate);
            Assert.True((DateTime.UtcNow - updatedAlert.ResolvedDate.Value).TotalSeconds < 10);  // Within 10 seconds
            Assert.True(updatedAlert.AlertAcknowledged);
        }

        [Fact]
        public async Task CreateManualAlertAsync_ValidAlert_CreatesNewAlert()
        {
            // Arrange
            var alert = new LowStockAlert
            {
                ItemId = 1,
                ItemType = "RawMaterial",
                CurrentStock = 5,
                MinimumStock = 50
            };

            // Act
            var result = await _lowStockAlertService.CreateManualAlertAsync(alert);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(alert.ItemId, result.ItemId);
            Assert.Equal(alert.ItemType, result.ItemType);
            Assert.Equal(alert.CurrentStock, result.CurrentStock);
            Assert.Equal(alert.MinimumStock, result.MinimumStock);
            Assert.Equal(DateTime.UtcNow.Date, result.TriggeredDate?.Date);
            Assert.False(result.IsResolved);
            Assert.False(result.AlertAcknowledged);
            Assert.NotEmpty(result.AlertMessage);
            Assert.Contains(result.AlertMessage, alert.ItemType);
            Assert.Contains(result.AlertMessage, result.ItemId.ToString());
        }

        [Fact]
        public async Task GetAlertByIdAsync_ValidAlert_RetrievesAlert()
        {
            // Arrange
            // Create a test alert
            var alert = new LowStockAlert
            {
                ItemId = 1,
                ItemType = "RawMaterial",
                CurrentStock = 5,
                MinimumStock = 50,
                TriggeredDate = DateTime.UtcNow,
                AlertMessage = "Test alert message",
                IsResolved = false
            };
            
            await _context.LowStockAlerts.AddAsync(alert);
            await _context.SaveChangesAsync();

            // Act
            var result = await _lowStockAlertService.GetAlertByIdAsync(alert.Id);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(alert.Id, result.Id);
            Assert.Equal(alert.ItemId, result.ItemId);
            Assert.Equal(alert.ItemType, result.ItemType);
            Assert.Equal(alert.CurrentStock, result.CurrentStock);
            Assert.Equal(alert.MinimumStock, result.MinimumStock);
            Assert.Equal(alert.TriggeredDate.Date, result.TriggeredDate?.Date);
            Assert.Equal(alert.AlertMessage, result.AlertMessage);
            Assert.Equal(alert.IsResolved, result.IsResolved);
        }

        [Fact]
        public async Task GetHistoricalAlertsAsync_DateRange_RetrievesCorrectAlerts()
        {
            // Arrange
            // Create test alerts
            var alert1 = new LowStockAlert
            {
                ItemId = 1,
                ItemType = "RawMaterial",
                CurrentStock = 5,
                MinimumStock = 50,
                TriggeredDate = new DateTime(2023, 1, 15)
            };
            
            var alert2 = new LowStockAlert
            {
                ItemId = 2,
                ItemType = "FinishedProduct",
                CurrentStock = 2,
                MinimumStock = 10,
                TriggeredDate = new DateTime(2023, 2, 15)
            };
            
            var alert3 = new LowStockAlert
            {
                ItemId = 3,
                ItemType = "RawMaterial",
                CurrentStock = 1,
                MinimumStock = 5,
                TriggeredDate = new DateTime(2023, 3, 15)
            };
            
            await _context.LowStockAlerts.AddRangeAsync(alert1, alert2, alert3);
            await _context.SaveChangesAsync();

            // Act
            var result = await _lowStockAlertService.GetHistoricalAlertsAsync(new DateTime(2023, 1, 1), new DateTime(2023, 2, 28));

            // Assert
            Assert.NotNull(result);
            Assert.Equal(2, result.Count);
            
            // Verify correct alerts are returned
            Assert.Contains(result, a => a.Id == alert1.Id);
            Assert.Contains(result, a => a.Id == alert2.Id);
            
            // Verify alert 3 is excluded (March 15th is outside the date range)
            Assert.DoesNotContain(result, a => a.Id == alert3.Id);
            
            // Verify order by date (newest first)
            Assert.Equal(alert2.Id, result.First().Id);
            Assert.Equal(alert1.Id, result.Last().Id);
        }
    }
}