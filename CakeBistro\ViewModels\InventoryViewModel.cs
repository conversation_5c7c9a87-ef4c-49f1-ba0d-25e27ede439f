using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Threading.Tasks;
using System.Windows.Input;
using CakeBistro.Core.Models;
using CakeBistro.Core.Interfaces;
using CommunityToolkit.Mvvm.Input;
using Xamarin.Forms;

namespace CakeBistro.ViewModels
{
    public class InventoryViewModel : BaseViewModel
    {
        private readonly IInventoryService _inventoryService;
        private readonly IRawMaterialService _rawMaterialService;
        private ObservableCollection<RawMaterial> _rawMaterials;
        private RawMaterial _selectedMaterial;
        private bool _isProcessing;
        private string _statusMessage;
        private Color _statusColor = Colors.Transparent;
        private IEnumerable<RawMaterial> _lowStockRecommendations;
        private IEnumerable<InventoryBatch> _selectedMaterialBatches;
        private IEnumerable<ExpiringBatchAlert> _activeExpiringBatchAlerts;

        public ObservableCollection<RawMaterial> RawMaterials
        {
            get => _rawMaterials;
            set => SetProperty(ref _rawMaterials, value);
        }
        public RawMaterial SelectedMaterial
        {
            get => _selectedMaterial;
            set => SetProperty(ref _selectedMaterial, value);
        }
        public bool IsProcessing
        {
            get => _isProcessing;
            set => SetProperty(ref _isProcessing, value);
        }
        public string StatusMessage
        {
            get => _statusMessage;
            set => SetProperty(ref _statusMessage, value);
        }
        public Color StatusColor
        {
            get => _statusColor;
            set => SetProperty(ref _statusColor, value);
        }
        public IEnumerable<RawMaterial> LowStockRecommendations
        {
            get => _lowStockRecommendations;
            set => SetProperty(ref _lowStockRecommendations, value);
        }
        public IEnumerable<InventoryBatch> SelectedMaterialBatches
        {
            get => _selectedMaterialBatches;
            set => SetProperty(ref _selectedMaterialBatches, value);
        }
        public IEnumerable<ExpiringBatchAlert> ActiveExpiringBatchAlerts
        {
            get => _activeExpiringBatchAlerts;
            set => SetProperty(ref _activeExpiringBatchAlerts, value);
        }

        public ICommand LoadDataCommand { get; }
        public ICommand ViewReorderRecommendationsCommand { get; }
        public ICommand PerformMonthlyStockTakeCommand { get; }
        public ICommand TransferStockCommand { get; }
        public ICommand UploadDocumentCommand { get; }

        public InventoryViewModel(IInventoryService inventoryService, IRawMaterialService rawMaterialService)
        {
            _inventoryService = inventoryService;
            _rawMaterialService = rawMaterialService;
            LoadDataCommand = new AsyncRelayCommand(LoadDataAsync);
            ViewReorderRecommendationsCommand = new AsyncRelayCommand(ViewReorderRecommendationsAsync);
            PerformMonthlyStockTakeCommand = new AsyncRelayCommand(PerformMonthlyStockTakeAsync);
            TransferStockCommand = new AsyncRelayCommand(TransferStockAsync);
            UploadDocumentCommand = new AsyncRelayCommand(UploadDocumentAsync);
        }

        public async Task LoadDataAsync()
        {
            IsProcessing = true;
            try
            {
                RawMaterials = new ObservableCollection<RawMaterial>(await _inventoryService.GetAllRawMaterialsAsync());
                LowStockRecommendations = await _inventoryService.GetLowStockAlertsAsync();
                ActiveExpiringBatchAlerts = await _inventoryService.GetBatchExpiryAlertsAsync();
            }
            finally
            {
                IsProcessing = false;
            }
        }

        private async Task ViewReorderRecommendationsAsync()
        {
            // Example: Show reorder recommendations UI
            StatusMessage = "Reorder recommendations loaded.";
            StatusColor = Color.FromArgb("#388E3C");
        }

        private async Task PerformMonthlyStockTakeAsync()
        {
            IsProcessing = true;
            try
            {
                var result = await _inventoryService.PerformMonthlyStockTakeAsync(DateTime.Now);
                StatusMessage = result?.Summary ?? "Stock take complete.";
                StatusColor = Color.FromArgb("#1976D2");
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error: {ex.Message}";
                StatusColor = Color.FromArgb("#D32F2F");
            }
            finally
            {
                IsProcessing = false;
            }
        }

        private async Task TransferStockAsync()
        {
            // Example: Transfer selected material to another branch
            StatusMessage = "Stock transfer initiated.";
            StatusColor = Color.FromArgb("#388E3C");
        }

        private async Task UploadDocumentAsync()
        {
            // Example: Upload document for selected material
            StatusMessage = "Document uploaded.";
            StatusColor = Color.FromArgb("#388E3C");
        }
    }
}
