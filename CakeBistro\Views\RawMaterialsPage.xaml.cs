using CakeBistro.ViewModels;

namespace CakeBistro.Views;

public partial class RawMaterialsPage : ContentPage
{
	public RawMaterialsPage(RawMaterialsViewModel viewModel)
	{
		InitializeComponent();
		BindingContext = viewModel;
	}

    protected override async void OnAppearing()
    {
        base.OnAppearing();
        if (BindingContext is RawMaterialsViewModel vm)
        {
            await vm.LoadDataCommand.ExecuteAsync(null);
        }
    }
}
