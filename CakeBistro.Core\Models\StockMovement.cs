using System;

namespace CakeBistro.Core.Models
{
    public class StockMovement
    {
        public int Id { get; set; }
        public int ProductId { get; set; }
        public decimal Quantity { get; set; }
        public MovementType MovementType { get; set; }
        public DateTime MovementDate { get; set; }
        public string ReferenceNumber { get; set; } = string.Empty;
        public string Notes { get; set; } = string.Empty;
        public StockMovementStatus Status { get; set; } = StockMovementStatus.Completed;
        public RawMaterial RawMaterial { get; set; }
    }

    public enum MovementType
    {
        Incoming,
        Outgoing
    }

    public enum StockMovementStatus
    {
        Pending,
        Completed,
        Cancelled
    }
}
