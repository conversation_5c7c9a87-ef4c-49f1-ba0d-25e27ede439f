
using System;
using System.ComponentModel.DataAnnotations;

namespace CakeBistro.Models
{
    /// <summary>
    /// Represents the result of a profitability analysis for a product
    /// Implements PRD 4.2 requirement for profitability analysis
    /// </summary>
    public class ProfitabilityAnalysis
    {
        /// <summary>
        /// Gets or sets the ID of the product being analyzed
        /// </summary>
        [Key]
        public int ProductId { get; set; }

        /// <summary>
        /// Gets or sets the calculated production cost
        /// </summary>
        public decimal ProductionCost { get; set; }

        /// <summary>
        /// Gets or sets the current sale price of the product
        /// </summary>
        public decimal SalePrice { get; set; }

        /// <summary>
        /// Gets or sets the profit margin calculated as percentage
        /// </summary>
        public decimal ProfitMarginPercentage { get; set; }

        /// <summary>
        /// Gets or sets the date when the analysis was performed
        /// </summary>
        public DateTime AnalysisDate { get; set; }
    }
}