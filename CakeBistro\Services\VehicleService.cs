using CakeBistro.Models;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq;

namespace CakeBistro.Services
{
    public class VehicleService
    {
        private readonly List<Vehicle> _vehicles = new();

        public Task<Vehicle> CreateVehicleAsync(Vehicle vehicle)
        {
            vehicle.Id = _vehicles.Count > 0 ? _vehicles.Max(v => v.Id) + 1 : 1;
            _vehicles.Add(vehicle);
            return Task.FromResult(vehicle);
        }

        public Task<List<Vehicle>> GetAllVehiclesAsync()
        {
            return Task.FromResult(_vehicles.ToList());
        }

        public Task<bool> RemoveVehicleAsync(int id)
        {
            var vehicle = _vehicles.FirstOrDefault(v => v.Id == id);
            if (vehicle == null) return Task.FromResult(false);
            _vehicles.Remove(vehicle);
            return Task.FromResult(true);
        }
    }
}
