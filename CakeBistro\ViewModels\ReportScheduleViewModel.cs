using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using CakeBistro.Services;
using System;
using System.Collections.ObjectModel;
using System.Threading.Tasks;
using Microsoft.UI.Xaml.Media;

namespace CakeBistro.ViewModels;

public partial class ReportScheduleViewModel : ObservableObject
{
    private readonly FinanceService _financeService;

    public ObservableCollection<string> ReportTypes { get; } = new() { "Balance Sheet", "Income Statement", "Cash Flow Statement" };
    public ObservableCollection<string> Frequencies { get; } = new() { "Once", "Daily", "Weekly", "Monthly" };
    public ObservableCollection<ReportSchedule> ScheduledReports { get; } = new();

    [ObservableProperty]
    private string selectedReportType;
    [ObservableProperty]
    private string selectedFrequency;
    [ObservableProperty]
    private DateTime scheduleDate = DateTime.Today;
    [ObservableProperty]
    private string statusMessage;

    private Color _statusColor = Colors.Transparent;
    public Color StatusColor
    {
        get => _statusColor;
        set => SetProperty(ref _statusColor, value);
    }

    public IRelayCommand ScheduleReportCommand { get; }

    public ReportScheduleViewModel(FinanceService financeService)
    {
        _financeService = financeService;
        ScheduleReportCommand = new AsyncRelayCommand(ScheduleReportAsync);
    }

    private async Task ScheduleReportAsync()
    {
        StatusMessage = string.Empty;
        StatusColor = Colors.Transparent;
        if (string.IsNullOrEmpty(SelectedReportType) || string.IsNullOrEmpty(SelectedFrequency))
        {
            StatusMessage = "Please select a report type and frequency.";
            StatusColor = Color.FromArgb("#1976D2");
            return;
        }
        var schedule = new ReportSchedule
        {
            ReportType = SelectedReportType,
            ScheduleDate = ScheduleDate,
            Frequency = SelectedFrequency
        };
        var result = await _financeService.ScheduleReportAsync(schedule);
        if (result.Success)
        {
            ScheduledReports.Add(schedule);
            StatusMessage = "Report scheduled successfully.";
            StatusColor = Color.FromArgb("#388E3C");
        }
        else
        {
            StatusMessage = result.Message;
            StatusColor = Color.FromArgb("#D32F2F");
        }
    }
}
