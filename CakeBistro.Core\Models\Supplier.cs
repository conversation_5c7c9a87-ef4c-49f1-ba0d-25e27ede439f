using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace CakeBistro.Core.Models
{
    public class Supplier : BaseEntity
    {
        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;
        [StringLength(100)]
        public string ContactName { get; set; } = string.Empty;
        [Required]
        [StringLength(20)]
        public string PhoneNumber { get; set; } = string.Empty;
        [StringLength(100)]
        [EmailAddress]
        public string Email { get; set; } = string.Empty;
        [StringLength(200)]
        public string Address { get; set; } = string.Empty;
        [StringLength(50)]
        public string TaxId { get; set; } = string.Empty;
        [StringLength(50)]
        public string AccountNumber { get; set; } = string.Empty;
        [StringLength(100)]
        public string Category { get; set; } = string.Empty;
        public bool IsActive { get; set; } = true;
        public bool HasPendingOrders { get; set; }
    }
}
