using System;
using System.Collections.Generic;
using CakeBistro.Core.Models;

namespace MCakeBistro.Repositories
{
    // Base repository implementation for common CRUD operations
    public abstract class RepositoryBase<T> : IRepository<T> where T : BaseModel, new()
    {
        protected readonly List<T> _context = new List<T>();

        public async Task<IEnumerable<T>> GetAllAsync()
        {
            return await Task.FromResult(_context);
        }

        public async Task<T> GetByIdAsync(Guid id)
        {
            return await Task.FromResult(_context.FirstOrDefault(e => e.Id == id));
        }

        public async Task AddAsync(T entity)
        {
            entity.Id = Guid.NewGuid();
            entity.DateCreated = DateTime.UtcNow;
            entity.DateModified = DateTime.UtcNow;
            _context.Add(entity);
            await Task.CompletedTask;
        }

        public async Task UpdateAsync(T entity)
        {
            var index = _context.FindIndex(e => e.Id == entity.Id);
            if (index != -1)
            {
                _context[index] = entity;
                _context[index].DateModified = DateTime.UtcNow;
            }
            await Task.CompletedTask;
        }

        public async Task DeleteAsync(Guid id)
        {
            var entity = _context.FirstOrDefault(e => e.Id == id);
            if (entity != null)
            {
                _context.Remove(entity);
            }
            await Task.CompletedTask;
        }
    }
}
