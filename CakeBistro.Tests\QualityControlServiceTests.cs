using CakeBistro.Core.Models;
using CakeBistro.Services;
using CakeBistro.Repositories;
using Moq;
using Xunit;

namespace CakeBistro.Tests
{
    public class QualityControlServiceTests
    {
        private readonly Mock<IRepository<QualityCheck>> _mockQualityCheckRepo;
        private readonly Mock<IRepository<ProductionBatch>> _mockProductionBatchRepo;
        private readonly QualityControlService _qualityControlService;

        public QualityControlServiceTests()
        {
            _mockQualityCheckRepo = new Mock<IRepository<QualityCheck>>();
            _mockProductionBatchRepo = new Mock<IRepository<ProductionBatch>>();
            _qualityControlService = new QualityControlService(
                _mockQualityCheckRepo.Object,
                _mockProductionBatchRepo.Object);
        }

        [Fact]
        public async Task AddQualityCheck_WithValidData_ShouldSucceed()
        {
            // Arrange
            var qualityCheck = new QualityCheck
            {
                ProductionBatchId = 1,
                CheckDate = DateTime.UtcNow,
                Type = "Visual",
                Status = "Passed",
                Parameter = "Color",
                CheckedBy = "TestUser",
                Notes = "Test quality check"
            };

            _mockQualityCheckRepo.Setup(repo => repo.AddAsync(It.IsAny<QualityCheck>()))
                                .ReturnsAsync(qualityCheck);

            // Act
            var result = await _qualityControlService.AddQualityCheckAsync(qualityCheck);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(qualityCheck.Type, result.Type);
            Assert.Equal(qualityCheck.Status, result.Status);
            _mockQualityCheckRepo.Verify(repo => repo.AddAsync(It.IsAny<QualityCheck>()), Times.Once());
        }

        [Fact]
        public async Task GetQualityChecksForBatch_ShouldReturnChecks()
        {
            // Arrange
            var batchId = 1;
            var qualityChecks = new List<QualityCheck>
            {
                new QualityCheck { ProductionBatchId = batchId, Type = "Visual", Status = "Passed" },
                new QualityCheck { ProductionBatchId = batchId, Type = "Weight", Status = "Failed" }
            };

            _mockQualityCheckRepo.Setup(repo => repo.GetAllAsync())
                                .ReturnsAsync(qualityChecks);

            // Act
            var result = await _qualityControlService.GetQualityChecksForBatchAsync(batchId);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(2, result.Count());
            Assert.All(result, check => Assert.Equal(batchId, check.ProductionBatchId));
        }

        [Fact]
        public async Task AddQualityCheck_WithInvalidData_ShouldThrowException()
        {
            // Arrange
            var invalidQualityCheck = new QualityCheck(); // Missing required fields

            // Act & Assert
            await Assert.ThrowsAsync<ArgumentException>(() => 
                _qualityControlService.AddQualityCheckAsync(invalidQualityCheck));
        }
    }
}
