[?9001h[?1004h[?25l[2J[m[H






[H]0;C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe[?25h[38;5;9m. : File C:\Users\<USER>\Documents\WindowsPowerShell\Microsoft.PowerShell_profile.ps1 cannot be loaded because running scripts is disabled on this system. For more 
information, see about_Execution_Policies at https:/go.microsoft.com/fwlink/?LinkID=135170.
At line:1 char:3
+ . 'C:\Users\<USER>\Documents\WindowsPowerShell\Microsoft.PowerShe ...
+   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : SecurityError: (:) [], PSSecurityException
    + FullyQualifiedErrorId : UnauthorizedAccess
[m
C:\Users\<USER>\Desktop\CakeBistro>Remove-Item -Path "C:/Users/<USER>/Desktop/CakeBistro/CakeBistro/tests" -Recurse -Force
'Remove-Item' is not recognized as an internal or external command,
operable program or batch file.
