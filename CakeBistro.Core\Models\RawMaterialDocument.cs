using System;

namespace CakeBistro.Core.Models
{
    public class RawMaterialDocument : BaseEntity
    {
        public int RawMaterialId { get; set; }
        public RawMaterial? RawMaterial { get; set; }
        public string FileName { get; set; } = string.Empty;
        public string ContentType { get; set; } = string.Empty;
        public long FileSize { get; set; }
        public byte[]? FileContent { get; set; }
        public string Category { get; set; } = string.Empty; // e.g., Invoice, Certificate, etc.
        public string Description { get; set; } = string.Empty;
    }
}
