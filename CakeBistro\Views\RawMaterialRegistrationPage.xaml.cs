using CakeBistro.ViewModels;
using Microsoft.Maui.Controls;
using System;

namespace CakeBistro.Views
{
    public partial class RawMaterialRegistrationPage : ContentPage
    {
        public RawMaterialRegistrationPage(RawMaterialRegistrationViewModel vm)
        {
            InitializeComponent();
            BindingContext = vm;
        }

        private void OnRegisterClicked(object sender, EventArgs e)
        {
            // TODO: Add registration logic and animated feedback
            DisplayAlert("Success", "Raw material registered!", "OK");
        }
    }
}
