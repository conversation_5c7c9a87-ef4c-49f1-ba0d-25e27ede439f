using CakeBistro.Core.Models;
using CakeBistro.Core.Interfaces;
using Microsoft.EntityFrameworkCore;

namespace CakeBistro.Repositories
{
    public class SalesOrderRepository : BaseRepository<SalesOrder>, ISalesOrderRepository
    {
        public SalesOrderRepository(CakeBistroContext context) : base(context) { }

        public override async Task DeleteAsync(Guid id)
        {
            var order = await _context.SalesOrders.FindAsync(id);
            if (order != null)
            {
                _context.SalesOrders.Remove(order);
                await _context.SaveChangesAsync();
            }
        }
        
        public async Task<IEnumerable<SalesOrder>> GetOrdersByCustomerAsync(Guid customerId)
        {
            return await _context.SalesOrders
                .Where(o => o.CustomerId == customerId)
                .ToListAsync();
        }
        
        public async Task<IEnumerable<SalesOrder>> GetOrdersByStatusAsync(SalesOrderStatus status)
        {
            return await _context.SalesOrders
                .Where(o => o.Status == status)
                .ToListAsync();
        }
        
        public async Task<IEnumerable<SalesOrder>> GetOrdersByDateRangeAsync(DateTime startDate, DateTime endDate)
        {
            return await _context.SalesOrders
                .Where(o => o.OrderDate >= startDate && o.OrderDate <= endDate)
                .ToListAsync();
        }
    }
}
