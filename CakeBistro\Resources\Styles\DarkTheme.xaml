<?xml version="1.0" encoding="utf-8" ?>
<ResourceDictionary xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
                    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml">
    <!-- Dark Color Palette -->
    <Color x:Key="Primary">#B39DDB</Color>
    <Color x:Key="PrimaryDark">#512BD4</Color>
    <Color x:Key="Secondary">#FFD54F</Color>
    <Color x:Key="Accent">#00BFAE</Color>
    <Color x:Key="Surface">#232323</Color>
    <Color x:Key="Background">#181818</Color>
    <Color x:Key="Error">#EF5350</Color>
    <Color x:Key="Success">#66BB6A</Color>
    <Color x:Key="Warning">#FFA726</Color>
    <Color x:Key="Info">#42A5F5</Color>
    <Color x:Key="TextPrimary">#F5F5F5</Color>
    <Color x:Key="TextSecondary">#B0B0B0</Color>

    <!-- Typography and Controls (inherits from AppTheme styles) -->
    <Style TargetType="Label">
        <Setter Property="FontFamily" Value="OpenSansRegular" />
        <Setter Property="TextColor" Value="{StaticResource TextPrimary}" />
    </Style>
    <Style TargetType="Button">
        <Setter Property="FontFamily" Value="OpenSansSemibold" />
        <Setter Property="BackgroundColor" Value="{StaticResource Primary}" />
        <Setter Property="TextColor" Value="Black" />
        <Setter Property="CornerRadius" Value="8" />
        <Setter Property="FontSize" Value="16" />
        <Setter Property="Padding" Value="12,6" />
        <Setter Property="Shadow" Value="True" />
    </Style>
    <Style TargetType="Entry">
        <Setter Property="FontFamily" Value="OpenSansRegular" />
        <Setter Property="FontSize" Value="15" />
        <Setter Property="BackgroundColor" Value="{StaticResource Surface}" />
        <Setter Property="TextColor" Value="{StaticResource TextPrimary}" />
        <Setter Property="Margin" Value="0,0,0,8" />
        <Setter Property="HeightRequest" Value="40" />
        <Setter Property="CornerRadius" Value="6" />
    </Style>
    <Style TargetType="Picker">
        <Setter Property="FontFamily" Value="OpenSansRegular" />
        <Setter Property="FontSize" Value="15" />
        <Setter Property="BackgroundColor" Value="{StaticResource Surface}" />
        <Setter Property="TextColor" Value="{StaticResource TextPrimary}" />
        <Setter Property="Margin" Value="0,0,0,8" />
        <Setter Property="HeightRequest" Value="40" />
        <Setter Property="CornerRadius" Value="6" />
    </Style>
    <Style TargetType="CollectionView">
        <Setter Property="BackgroundColor" Value="{StaticResource Background}" />
    </Style>
    <Style TargetType="ContentPage">
        <Setter Property="BackgroundColor" Value="{StaticResource Background}" />
    </Style>
    <Style x:Key="PrimaryButton" TargetType="Button">
        <Setter Property="BackgroundColor" Value="{StaticResource Primary}" />
        <Setter Property="TextColor" Value="Black" />
        <Setter Property="CornerRadius" Value="8" />
        <Setter Property="FontAttributes" Value="Bold" />
        <Setter Property="FontSize" Value="16" />
        <Setter Property="Padding" Value="12,6" />
        <Setter Property="Shadow" Value="True" />
    </Style>
    <Style x:Key="SecondaryButton" TargetType="Button">
        <Setter Property="BackgroundColor" Value="{StaticResource Secondary}" />
        <Setter Property="TextColor" Value="Black" />
        <Setter Property="CornerRadius" Value="8" />
        <Setter Property="FontAttributes" Value="Bold" />
        <Setter Property="FontSize" Value="16" />
        <Setter Property="Padding" Value="12,6" />
        <Setter Property="Shadow" Value="True" />
    </Style>
</ResourceDictionary>
