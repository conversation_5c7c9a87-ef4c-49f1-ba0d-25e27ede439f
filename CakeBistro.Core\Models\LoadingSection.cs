using System.Collections.Generic;

namespace CakeBistro.Core.Models
{
    public class LoadingSection
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public ICollection<LoadingItem> Items { get; set; } = new List<LoadingItem>();
    }

    public class LoadingItem
    {
        public int Id { get; set; }
        public int LoadingSectionId { get; set; }
        public int ProductId { get; set; }
        public int Quantity { get; set; }
        public Product Product { get; set; }
    }
}
