<?xml version="1.0" encoding="utf-8" ?>
<ResourceDictionary xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
                    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml">
    <!-- Button Press Animation -->
    <Style x:Key="AnimatedButton" TargetType="Button" BasedOn="{StaticResource PrimaryButton}">
        <Setter Property="VisualStateManager.VisualStateGroups">
            <VisualStateGroupList>
                <VisualStateGroup x:Name="CommonStates">
                    <VisualState x:Name="Normal" />
                    <VisualState x:Name="Pressed">
                        <VisualState.Setters>
                            <Setter Property="Scale" Value="0.96" />
                            <Setter Property="BackgroundColor" Value="{StaticResource Accent}" />
                        </VisualState.Setters>
                    </VisualState>
                    <VisualState x:Name="PointerOver">
                        <VisualState.Setters>
                            <Setter Property="BackgroundColor" Value="{StaticResource Secondary}" />
                            <Setter Property="Scale" Value="1.04" />
                        </VisualState.Setters>
                    </VisualState>
                    <VisualState x:Name="Focused">
                        <VisualState.Setters>
                            <Setter Property="BackgroundColor" Value="{StaticResource Info}" />
                            <Setter Property="Scale" Value="1.02" />
                        </VisualState.Setters>
                    </VisualState>
                </VisualStateGroup>
            </VisualStateGroupList>
        </Setter>
        <Setter Property="Shadow" Value="True" />
        <Setter Property="ShadowBrush" Value="#33000000" />
    </Style>
</ResourceDictionary>
