<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="CakeBistro.Views.ReportingPage"
             xmlns:viewModels="clr-namespace:CakeBistro.ViewModels"
             xmlns:models="clr-namespace:CakeBistro.Models"
             Title="Reporting">
    <ContentPage.BindingContext>
        <viewModels:ReportingViewModel />
    </ContentPage.BindingContext>
    
    <ScrollView>
        <VerticalStackLayout Spacing="16" Padding="16">
            <!-- Status Message -->
            <Label Text="{Binding StatusMessage}"
                   IsVisible="{Binding StatusMessage, Converter={StaticResource EmptyToFalseConverter}}"
                   TextColor="{Binding StatusColor}" />
            
            <!-- Inventory Valuation Report Section -->
            <Frame Header="Inventory Valuation Report">
                <VerticalStackLayout Spacing="8">
                    <Button Text="Generate New Report"
                            Command="{Binding GenerateInventoryReportCommand}"
                            IsEnabled="{Binding GenerateInventoryReportCommand.IsExecuting, Converter={StaticResource InvertBooleanConverter}}"
                            BackgroundColor="Blue"
                            TextColor="White" />
                    
                    <Button Text="Export to PDF"
                            Command="{Binding ExportInventoryReportCommand}"
                            IsEnabled="{Binding ExportInventoryReportCommand.IsExecuting, Converter={StaticResource InvertBooleanConverter}}"
                            BackgroundColor="DarkOrange"
                            TextColor="White" />
                    
                    <ActivityIndicator IsRunning="{Binding IsProcessing}"
                                       IsVisible="{Binding IsProcessing}" />
                    
                    <Frame IsVisible="{Binding CurrentInventoryReport, Converter={StaticResource NullToFalseConverter}}"
                           BackgroundColor="LightYellow">
                        <VerticalStackLayout Spacing="4">
                            <Label Text="{Binding CurrentInventoryReport.ReportDate, StringFormat='Report Date: {0:g}'}"
                                   FontAttributes="Bold" />
                            
                            <Label Text="{Binding CurrentInventoryReport.TotalInventoryValue, StringFormat='Total Inventory Value: {0:C}'}"
                                   FontSize="Large"
                                   FontAttributes="Bold" />
                            
                            <Label Text="{Binding CurrentInventoryReport.TotalItemCount, StringFormat='Total Items: {0}'}" />
                            
                            <Label Text="{Binding CurrentInventoryReport.LowStockItemCount, StringFormat='Low Stock Items: {0}'}" />
                            
                            <Label Text="{Binding CurrentInventoryReport.OutOfStockItemCount, StringFormat='Out of Stock Items: {0}'}" />
                            
                            <Label Text="{Binding CurrentInventoryReport.CriticalStockItemCount, StringFormat='Critical Stock Items: {0}'}" />
                            
                            <Label Text="Notes: {Binding CurrentInventoryReport.Notes}"
                                   LineBreakMode="WordWrap"
                                   MaxLines="3" />
                            
                            <!-- Itemized Valuation -->
                            <Expander Header="Itemized Valuation">
                                <CollectionView ItemsSource="{Binding CurrentInventoryReport.ItemValuations}"
                                                  SelectionMode="None">
                                    <CollectionView.ItemTemplate>
                                        <DataTemplate x:DataType="models:InventoryItemValuation">
                                            <Frame Margin="0,4">
                                                <Grid ColumnDefinitions="*, Auto">
                                                    <VerticalStackLayout Grid.Column="0">
                                                        <Label Text="{Binding RawMaterialName}"
                                                               FontAttributes="Bold" />
                                                        <Label Text="{Binding Quantity, StringFormat='Quantity: {0:F0}'}" />
                                                        <Label Text="{Binding UnitPrice, StringFormat='Unit Price: {0:C}'}" />
                                                        <Label Text="{Binding TotalValue, StringFormat='Total Value: {0:C}'}" />
                                                    </VerticalStackLayout>
                                                    
                                                    <VerticalStackLayout Grid.Column="1">
                                                        <Label Text="{Binding Source={x:Reference This}, Converter={StaticResource StockLevelTextConverter}}"
                                                               HorizontalOptions="EndAndExpand"
                                                               TextColor="{Binding Source={x:Reference This}, Converter={StaticResource StockLevelColorConverter}}" />
                                                        <Label Text="{Binding StorageLocation, StringFormat='Loc: {0}'}"
                                                               HorizontalOptions="EndAndExpand" />
                                                    </VerticalStackLayout>
                                                </Grid>
                                            </Frame>
                                        </DataTemplate>
                                    </CollectionView.ItemTemplate>
                                </CollectionView>
                            </Expander>
                            
                            <!-- Recommendations -->
                            <Expander Header="Recommendations" IsExpanded="True">
                                <CollectionView ItemsSource="{Binding CurrentInventoryReport.Recommendations}"
                                                  SelectionMode="None">
                                    <CollectionView.ItemTemplate>
                                        <DataTemplate>
                                            <Frame Margin="0,4" BackgroundColor="LightBlue">
                                                <Label Text="{Binding .}"
                                                       TextColor="Black"
                                                       LineBreakMode="WordWrap" />
                                            </Frame>
                                        </DataTemplate>
                                    </CollectionView.ItemTemplate>
                                </CollectionView>
                            </Expander>
                        </VerticalStackLayout>
                    </Frame>
                </VerticalStackLayout>
            </Frame>
            
            <!-- Batch Expiration Report Section -->
            <Frame Header="Batch Expiration Report">
                <VerticalStackLayout Spacing="8">
                    <Button Text="Generate New Batch Expiration Report"
                            Command="{Binding GenerateExpiringBatchReportCommand}"
                            IsEnabled="{Binding GenerateExpiringBatchReportCommand.IsExecuting, Converter={StaticResource InvertBooleanConverter}}"
                            BackgroundColor="Blue"
                            TextColor="White" />
                    
                    <Button Text="Export to PDF"
                            Command="{Binding ExportExpiringBatchReportCommand}"
                            IsEnabled="{Binding ExportExpiringBatchReportCommand.IsExecuting, Converter={StaticResource InvertBooleanConverter}}"
                            BackgroundColor="DarkOrange"
                            TextColor="White" />
                    
                    <ActivityIndicator IsRunning="{Binding IsProcessing}"
                                       IsVisible="{Binding IsProcessing}" />
                    
                    <Frame IsVisible="{Binding CurrentExpiringBatchReport, Converter={StaticResource NullToFalseConverter}}"
                           BackgroundColor="LightGreen">
                        <VerticalStackLayout Spacing="4">
                            <Label Text="{Binding CurrentExpiringBatchReport.ReportDate, StringFormat='Report Date: {0:g}'}"
                                   FontAttributes="Bold" />
                            
                            <Label Text="{Binding CurrentExpiringBatchReport.TotalExpiringBatches, StringFormat='Total Expiring Batches: {0}'}"
                                   FontSize="Large"
                                   FontAttributes="Bold" />
                            
                            <Label Text="{Binding CurrentExpiringBatchReport.BatchesExpiringWithin30Days, StringFormat='Expiring Within 30 Days: {0}'}" />
                            
                            <Label Text="{Binding CurrentExpiringBatchReport.BatchesExpiringWithin7Days, StringFormat='Urgent (Expiring Within 7 Days): {0}'}" />
                            
                            <Label Text="{Binding CurrentExpiringBatchReport.ExpiredBatches, StringFormat='Expired Batches: {0}'}" />
                            
                            <Label Text="Notes: {Binding CurrentExpiringBatchReport.Notes}"
                                   LineBreakMode="WordWrap"
                                   MaxLines="3" />
                            
                            <!-- Expiring Batch Details -->
                            <Expander Header="Expiring Batch Details">
                                <CollectionView ItemsSource="{Binding CurrentExpiringBatchReport.ExpiringBatchDetails}"
                                                  SelectionMode="None">
                                    <CollectionView.ItemTemplate>
                                        <DataTemplate x:DataType="models:ExpiringBatchDetail">
                                            <Frame Margin="0,4">
                                                <Grid ColumnDefinitions="*, *, *">
                                                    <VerticalStackLayout Grid.Column="0">
                                                        <Label Text="{Binding RawMaterialName}"
                                                               FontAttributes="Bold" />
                                                        <Label Text="{Binding BatchNumber, StringFormat='Batch: {0}'}" />
                                                        <Label Text="{Binding ExpiryDate, StringFormat='Expires: {0:d}'}" />
                                                    </VerticalStackLayout>
                                                    
                                                    <VerticalStackLayout Grid.Column="1">
                                                        <Label Text="{Binding Quantity, StringFormat='Qty: {0:F0}'}" />
                                                        <Label Text="{Binding DaysUntilExpiry, StringFormat='Days Until Expiry: {0}'}" />
                                                        <Label Text="{Binding IsUrgent, StringFormat='Is Urgent: {0}'}" />
                                                    </VerticalStackLayout>
                                                    
                                                    <Label Text="{Binding StorageLocation}"
                                                           Grid.Column="2"
                                                           HorizontalOptions="EndAndExpand"
                                                           VerticalOptions="Center" />
                                                </Grid>
                                            </Frame>
                                        </DataTemplate>
                                    </CollectionView.ItemTemplate>
                                </CollectionView>
                            </Expander>
                            
                            <!-- Expiration Recommendations -->
                            <Expander Header="Expiration Recommendations" IsExpanded="True">
                                <CollectionView ItemsSource="{Binding CurrentExpiringBatchReport.Recommendations}"
                                                  SelectionMode="None">
                                    <CollectionView.ItemTemplate>
                                        <DataTemplate>
                                            <Frame Margin="0,4" BackgroundColor="LightYellow">
                                                <Label Text="{Binding .}"
                                                       TextColor="Black"
                                                       LineBreakMode="WordWrap" />
                                            </Frame>
                                        </DataTemplate>
                                    </CollectionView.ItemTemplate>
                                </CollectionView>
                            </Expander>
                        </VerticalStackLayout>
                    </Frame>
                </VerticalStackLayout>
            </Frame>
            
            <!-- Sales Reports -->
            <Frame Header="Sales Reports">
                <Label Text="Sales reporting functionality will be implemented here." />
            </Frame>
            
            <!-- Financial Reports -->
            <Frame Header="Financial Reports">
                <Label Text="Financial reporting functionality will be implemented here." />
            </Frame>
        </VerticalStackLayout>
    </ScrollView>
</ContentPage>