
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using CakeBistro.Models;
using Microsoft.Maui.ApplicationModel;
using Microsoft.Maui.Graphics;
using Microsoft.Maui.UserControl;

namespace CakeBistro.Services
{
    /// <summary>
    /// Implementation of IThemeService for .NET MAUI UI integration
    /// </summary>
    public class UIThemeService : BaseService<CustomTheme>, IThemeService
    {
        private readonly IThemeService _coreThemeService;
        private readonly CakeBistroContext _context;
        private string _primaryFont;
        private string _secondaryFont;

        /// <summary>
        /// Initializes a new instance of the <see cref="UIThemeService"/> class.
        /// </summary>
        /// <param name="context">The database context to use for theme operations</param>
        /// <param name="coreThemeService">The core theme service implementation</param>
        public UIThemeService(CakeBistroContext context, IThemeService coreThemeService) : base(new Repository<CustomTheme>(context))
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
            _coreThemeService = coreThemeService ?? throw new ArgumentNullException(nameof(coreThemeService));
        }

        /// <inheritdoc />
        public async Task<List<ThemeInfo>> GetAvailableThemesAsync()
        {
            return await _coreThemeService.GetAvailableThemesAsync();
        }

        /// <inheritdoc />
        public async Task<ThemeConfiguration> GetCurrentThemeAsync()
        {
            return await _coreThemeService.GetCurrentThemeAsync();
        }

        /// <inheritdoc />
        public async Task<bool> ApplyThemeAsync(string themeName)
        {
            // Apply theme in core service
            var result = await _coreThemeService.ApplyThemeAsync(themeName);
            
            if (result)
            {
                // Get current theme configuration
                var themeConfig = await _coreThemeService.GetCurrentThemeAsync();
                
                // Apply theme to UI
                ApplyUITheme(themeConfig);
            }
            
            return result;
        }

        /// <inheritdoc />
        public async Task<bool> SaveThemePreferencesAsync(ThemePreferences preferences)
        {
            // Save preferences using core service
            return await _coreThemeService.SaveThemePreferencesAsync(preferences);
        }

        /// <inheritdoc />
        public async Task<ThemePreferences> GetThemePreferencesAsync()
        {
            return await _coreThemeService.GetThemePreferencesAsync();
        }

        /// <inheritdoc />
        public async Task<CustomTheme> CreateCustomThemeAsync(CustomTheme theme)
        {
            // Create custom theme using core service
            var createdTheme = await _coreThemeService.CreateCustomThemeAsync(theme);
            
            // Apply new theme to UI
            if (createdTheme != null)
            {
                await ApplyThemeAsync(createdTheme.Name);
            }
            
            return createdTheme;
        }

        /// <inheritdoc />
        public async Task<CustomTheme> UpdateCustomThemeAsync(CustomTheme theme)
        {
            // Update custom theme using core service
            var updatedTheme = await _coreThemeService.UpdateCustomThemeAsync(theme);
            
            // Apply updated theme to UI
            if (updatedTheme != null)
            {
                await ApplyThemeAsync(updatedTheme.Name);
            }
            
            return updatedTheme;
        }

        /// <inheritdoc />
        public async Task<bool> DeleteCustomThemeAsync(int themeId)
        {
            // Get theme before deletion
            var themeToDelete = await _context.CustomThemes.FindAsync(themeId);
            
            // Delete using core service
            var result = await _coreThemeService.DeleteCustomThemeAsync(themeId);
            
            // If current theme was deleted, apply default theme
            if (result && themeToDelete != null)
            {
                var currentTheme = await _coreThemeService.GetCurrentThemeAsync();
                
                // If current theme was deleted, apply default theme
                if (currentTheme?.CurrentThemeName == themeToDelete.Name)
                {
                    await ApplyThemeAsync("Default");
                }
            }
            
            return result;
        }

        /// <summary>
        /// Gets the primary font family
        /// </summary>
        public string PrimaryFontFamily
        {
            get => _primaryFont;
            set
            {
                if (_primaryFont != value)
                {
                    _primaryFont = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// Gets the secondary font family
        /// </summary>
        public string SecondaryFontFamily
        {
            get => _secondaryFont;
            set
            {
                if (_secondaryFont != value)
                {
                    _secondaryFont = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// Applies the theme to the .NET MAUI application
        /// </summary>
        /// <param name="themeConfig">The theme configuration to apply</param>
        private void ApplyUITheme(ThemeConfiguration themeConfig)
        {
            // Set app theme based on theme configuration
            var currentAppTheme = themeConfig.IsDarkModeEnabled ? AppTheme.Dark : AppTheme.Light;
            Application.Current.UserAppTheme = currentAppTheme;
            
            // Update application resources with theme colors
            UpdateApplicationResources(themeConfig);
            
            // Broadcast theme changed event
            ThemeChanged?.Invoke(this, new ThemeEventArgs { Theme = themeConfig });
        }

        /// <summary>
        /// Updates application resources with current theme colors
        /// </summary>
        /// <param name="themeConfig">The current theme configuration</param>
        private void UpdateApplicationResources(ThemeConfiguration themeConfig)
        {
            // Update primary color resources
            Application.Current.Resources["PrimaryColor"] = Color.Parse(themeConfig.AccentColor);
            Application.Current.Resources["SecondaryColor"] = Colors.White;
            
            // Update background and text colors
            Application.Current.Resources["BackgroundColor"] = Color.Parse(themeConfig.BackgroundColor);
            Application.Current.Resources["TextColor"] = Color.Parse(themeConfig.TextColor);
            
            // Update font families if available
            if (!string.IsNullOrEmpty(themeConfig.PrimaryFontFamily))
            {
                Application.Current.Resources["PrimaryFontFamily"] = themeConfig.PrimaryFontFamily;
            }
            
            if (!string.IsNullOrEmpty(themeConfig.SecondaryFontFamily))
            {
                Application.Current.Resources["SecondaryFontFamily"] = themeConfig.SecondaryFontFamily;
            }
        }

        /// <summary>
        /// Event that is raised when the theme changes
        /// </summary>
        public event EventHandler<ThemeEventArgs> ThemeChanged;
    }

    /// <summary>
    /// Event arguments for theme change events
    /// </summary>
    public class ThemeEventArgs : EventArgs
    {
        /// <summary>
        /// Gets or sets the new theme configuration
        /// </summary>
        public ThemeConfiguration Theme { get; set; }
    }
}