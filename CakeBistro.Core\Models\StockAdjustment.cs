namespace CakeBistro.Core.Models {
    public class StockAdjustment : BaseEntity
    {
        /// <summary>
        /// Gets or sets the ID of the product for which the stock is being adjusted.
        /// </summary>
        public int ProductId { get; set; }
        
        /// <summary>
        /// Gets or sets the quantity to adjust.
        /// Positive values increase inventory (e.g., stock receipt)
        /// Negative values decrease inventory (e.g., stock loss, damage)
        /// </summary>
        public decimal Quantity { get; set; }
        
        /// <summary>
        /// Gets or sets the reason for the stock adjustment.
        /// </summary>
        public string Reason { get; set; }
        
        /// <summary>
        /// Gets or sets the date when the adjustment was made.
        /// Defaults to current time if not specified.
        /// </summary>
        public DateTime AdjustmentDate { get; set; } = DateTime.UtcNow;
        
        /// <summary>
        /// Gets or sets the user who performed the adjustment.
        /// </summary>
        public int UserId { get; set; }
        
        // Navigation property
        public virtual Product Product { get; set; }
    }
}