<?xml version="1.0" encoding="utf-8" ?>
<ContentView xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="CakeBistro.Views.FormTemplate">
    <StackLayout Padding="20" Spacing="16">
        <!-- Place form fields here using BindableProperties for reuse -->
        <ContentPresenter x:Name="FormFields" />
        <Button Text="Save" Command="{Binding SaveCommand}" IsEnabled="{Binding CanSave}"/>
        <Button Text="Cancel" Command="{Binding CancelCommand}"/>
        <ActivityIndicator IsRunning="{Binding IsSaving}" IsVisible="{Binding IsSaving}"/>
        <Label Text="{Binding ErrorMessage}" TextColor="Red" IsVisible="{Binding HasError}"/>
    </StackLayout>
</ContentView>
