using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using CakeBistro.Core.Models;
using CakeBistro.Services;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Maui.Graphics;

namespace CakeBistro.ViewModels
{
    public partial class PackingViewModel : ObservableObject
    {
        private readonly PackingService _packingService;

        [ObservableProperty]
        private ObservableCollection<PackingSection> packingSections = new();
        [ObservableProperty]
        private PackingSection? selectedSection;
        [ObservableProperty]
        private PackingItem? selectedItem;
        [ObservableProperty]
        private string? newSectionName;
        [ObservableProperty]
        private string? newSectionDescription;
        [ObservableProperty]
        private string? newItemName;
        [ObservableProperty]
        private int newItemQuantity;
        private Color _statusColor = Colors.Transparent;

        public Color StatusColor
        {
            get => _statusColor;
            set => SetProperty(ref _statusColor, value);
        }

        public PackingViewModel(PackingService packingService)
        {
            _packingService = packingService;
        }

        [RelayCommand]
        public async Task LoadPackingSectionsAsync()
        {
            var sections = await _packingService.GetAllPackingSectionsAsync();
            PackingSections = new ObservableCollection<PackingSection>(sections);
        }

        [RelayCommand]
        public async Task CreateSectionAsync()
        {
            if (!string.IsNullOrWhiteSpace(NewSectionName))
            {
                var section = new PackingSection { Name = NewSectionName, Description = NewSectionDescription };
                await _packingService.CreatePackingSectionAsync(section);
                await LoadPackingSectionsAsync();
                NewSectionName = null;
                NewSectionDescription = null;
                StatusMessage = "Section created successfully.";
                StatusColor = Color.FromArgb("#388E3C");
            }
        }

        [RelayCommand]
        public async Task AddItemAsync()
        {
            if (SelectedSection != null && !string.IsNullOrWhiteSpace(NewItemName))
            {
                var item = new PackingItem { Name = NewItemName, Quantity = NewItemQuantity };
                await _packingService.AddItemToPackingSectionAsync(SelectedSection.Id, item);
                await LoadPackingSectionsAsync();
                NewItemName = null;
                NewItemQuantity = 0;
                StatusMessage = "Item added successfully.";
                StatusColor = Color.FromArgb("#388E3C");
            }
        }

        [RelayCommand]
        public async Task RemoveItemAsync()
        {
            if (SelectedSection != null && SelectedItem != null)
            {
                await _packingService.RemoveItemFromPackingSectionAsync(SelectedSection.Id, SelectedItem.Id);
                await LoadPackingSectionsAsync();
                StatusMessage = "Item removed successfully.";
                StatusColor = Color.FromArgb("#388E3C");
            }
        }

        [RelayCommand]
        public async Task AdjustQuantityAsync(int newQuantity)
        {
            if (SelectedSection != null && SelectedItem != null)
            {
                await _packingService.AdjustPackingItemQuantityAsync(SelectedSection.Id, SelectedItem.Id, newQuantity);
                await LoadPackingSectionsAsync();
                StatusMessage = "Quantity adjusted successfully.";
                StatusColor = Color.FromArgb("#388E3C");
            }
        }
    }
}
