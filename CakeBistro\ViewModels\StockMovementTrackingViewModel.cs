using System.Collections.ObjectModel;
using System.Windows.Input;
using CakeBistro.Core.Models;
using CakeBistro.Services;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;

namespace CakeBistro.ViewModels
{
    public partial class StockMovementTrackingViewModel : ObservableObject
    {
        private readonly IInventoryService _inventoryService;
        public ObservableCollection<RawMaterial> Materials { get; } = new();
        public ObservableCollection<StockMovement> Movements { get; } = new();
        [ObservableProperty]
        private RawMaterial selectedMaterial;
        [ObservableProperty]
        private int quantity;
        [ObservableProperty]
        private string movementType;

        public StockMovementTrackingViewModel(IInventoryService inventoryService)
        {
            _inventoryService = inventoryService;
            LoadMaterials();
            LoadMovements();
        }

        private async void LoadMaterials()
        {
            var materials = await _inventoryService.GetAllRawMaterialsAsync();
            Materials.Clear();
            foreach (var m in materials)
                Materials.Add(m);
        }

        private async void LoadMovements()
        {
            var movements = await _inventoryService.GetAllStockMovementsAsync();
            Movements.Clear();
            foreach (var m in movements)
                Movements.Add(m);
        }

        [RelayCommand]
        private async Task RecordMovement()
        {
            var movement = new StockMovement
            {
                ProductId = SelectedMaterial?.Id ?? 0,
                Quantity = Quantity,
                MovementType = MovementType == "Incoming" ? StockMovementType.Incoming : StockMovementType.Outgoing
            };
            await _inventoryService.RecordStockMovementAsync(movement);
            Quantity = 0;
            MovementType = null;
            LoadMovements();
        }
    }
}
