using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using CakeBistro.Core.Interfaces;
using CakeBistro.Core.Models;
using Microsoft.EntityFrameworkCore;
using CakeBistro.Data; // For CakeBistroContext

namespace CakeBistro.Services
{
    public class QualityControlService : IQualityControlService
    {
        private readonly CakeBistroContext _context;
        private readonly IProductionService _productionService;
        private readonly INotificationService _notificationService;

        public QualityControlService(
            CakeBistroContext context,
            IProductionService productionService,
            INotificationService notificationService
        )
        {
            _context = context;
            _productionService = productionService;
            _notificationService = notificationService;
        }

        public async Task<QualityCheck> CreateCheckAsync(QualityCheck check)
        {
            var (isValid, message) = await ValidateQualityCheckAsync(check);
            if (!isValid)
                throw new ArgumentException(message);

            _context.QualityChecks.Add(check);
            await _context.SaveChangesAsync();

            // Send notification if the check failed or needs review
            if (check.Status == "Failed" || check.Status == "NeedsReview")
            {
                await _notificationService.SendQualityCheckNotificationAsync(check);
            }

            // Update production batch status
            if (check.Status == "Failed")
            {
                var batch = await _context.ProductionBatches.FindAsync(check.ProductionBatchId);
                if (batch != null)
                {
                    batch.Status = "QualityCheckFailed";
                    await _context.SaveChangesAsync();
                }
            }

            return check;
        }

        public async Task<QualityCheck> UpdateCheckAsync(QualityCheck check)
        {
            var (isValid, message) = await ValidateQualityCheckAsync(check);
            if (!isValid)
                throw new ArgumentException(message);

            _context.Entry(check).State = EntityState.Modified;
            await _context.SaveChangesAsync();

            // Update production batch status based on the check result
            var batch = await _context.ProductionBatches.FindAsync(check.ProductionBatchId);
            if (batch != null)
            {
                if (check.Status == "Failed")
                {
                    batch.Status = "QualityCheckFailed";
                }
                else if (check.Status == "Passed" && batch.Status == "QualityCheckFailed")
                {
                    // If all checks are now passed, update the batch status
                    if (await ValidateBatchQualityAsync(batch.Id))
                    {
                        batch.Status = "InProgress";
                    }
                }
                await _context.SaveChangesAsync();
            }

            return check;
        }

        public async Task<QualityCheck> GetCheckByIdAsync(int id)
        {
            return await _context
                .QualityChecks.Include(q => q.ProductionBatch)
                .FirstOrDefaultAsync(q => q.Id == id);
        }

        public async Task<IEnumerable<QualityCheck>> GetChecksByBatchAsync(int batchId)
        {
            return await _context
                .QualityChecks.Where(q => q.ProductionBatchId == batchId)
                .OrderByDescending(q => q.CheckDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<QualityCheck>> GetChecksByDateRangeAsync(
            DateTime startDate,
            DateTime endDate
        )
        {
            return await _context
                .QualityChecks.Include(q => q.ProductionBatch)
                .Where(q => q.CheckDate >= startDate && q.CheckDate <= endDate)
                .OrderByDescending(q => q.CheckDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<QualityCheck>> GetFailedChecksAsync(DateTime? since = null)
        {
            var query = _context
                .QualityChecks.Include(q => q.ProductionBatch)
                .Where(q => q.Status == "Failed");

            if (since.HasValue)
            {
                query = query.Where(q => q.CheckDate >= since.Value);
            }

            return await query.OrderByDescending(q => q.CheckDate).ToListAsync();
        }

        public async Task<bool> DeleteCheckAsync(int id)
        {
            var check = await _context.QualityChecks.FindAsync(id);
            if (check == null)
                return false;

            _context.QualityChecks.Remove(check);
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<bool> ValidateBatchQualityAsync(int batchId)
        {
            var checks = await GetChecksByBatchAsync(batchId);
            return !checks.Any(c => c.Status == "Failed");
        }

        public async Task<(bool IsValid, string Message)> ValidateQualityCheckAsync(
            QualityCheck check
        )
        {
            if (check == null)
                return (false, "Check cannot be null");

            if (string.IsNullOrWhiteSpace(check.Type))
                return (false, "Check type is required");

            if (string.IsNullOrWhiteSpace(check.CheckedBy))
                return (false, "Checker name is required");

            var batch = await _context.ProductionBatches.FindAsync(check.ProductionBatchId);
            if (batch == null)
                return (false, "Production batch not found");

            if (
                check.MinValue.HasValue
                && check.MaxValue.HasValue
                && check.MinValue > check.MaxValue
            )
                return (false, "Minimum value cannot be greater than maximum value");

            // Validate numeric measurements if provided
            if (check.MeasuredValue.HasValue)
            {
                if (check.MinValue.HasValue && check.MeasuredValue < check.MinValue)
                    return (
                        false,
                        $"Measured value {check.MeasuredValue} is below minimum {check.MinValue}"
                    );

                if (check.MaxValue.HasValue && check.MeasuredValue > check.MaxValue)
                    return (
                        false,
                        $"Measured value {check.MeasuredValue} is above maximum {check.MaxValue}"
                    );
            }

            return (true, string.Empty);
        }

        public async Task<Dictionary<string, int>> GetQualityMetricsAsync(
            DateTime startDate,
            DateTime endDate
        )
        {
            var checks = await GetChecksByDateRangeAsync(startDate, endDate);
            return new Dictionary<string, int>
            {
                ["TotalChecks"] = checks.Count(),
                ["PassedChecks"] = checks.Count(c => c.Status == "Passed"),
                ["FailedChecks"] = checks.Count(c => c.Status == "Failed"),
                ["PendingChecks"] = checks.Count(c => c.Status == "NeedsReview"),
            };
        }

        public async Task<IEnumerable<QualityCheck>> GetPendingChecksAsync()
        {
            return await _context
                .QualityChecks.Include(q => q.ProductionBatch)
                .Where(q => q.Status == "NeedsReview")
                .OrderBy(q => q.CheckDate)
                .ToListAsync();
        }

        public async Task<bool> RequiresQualityCheckAsync(int productionBatchId, string checkType)
        {
            var existingChecks = await GetChecksByBatchAsync(productionBatchId);
            return !existingChecks.Any(c => c.Type == checkType && c.Status == "Passed");
        }
    }
}
