<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:viewmodels="clr-namespace:CakeBistro.ViewModels"
             x:Class="CakeBistro.Views.DriverListPage">
    <ContentPage.BindingContext>
        <viewmodels:DriverListViewModel />
    </ContentPage.BindingContext>
    <ScrollView>
        <VerticalStackLayout Padding="16">
            <Label Text="Drivers" FontSize="24" FontAttributes="Bold" />
            <Entry Placeholder="Name" Text="{Binding NewName}" />
            <Entry Placeholder="Phone" Text="{Binding NewPhone}" />
            <Entry Placeholder="License Number" Text="{Binding NewLicenseNumber}" />
            <Entry Placeholder="Notes" Text="{Binding NewNotes}" />
            <Button Text="Add Driver" Command="{Binding AddDriverCommand}" />
            <CollectionView ItemsSource="{Binding Drivers}" SelectionMode="Single" SelectedItem="{Binding SelectedDriver, Mode=TwoWay}">
                <CollectionView.ItemTemplate>
                    <DataTemplate>
                        <Frame Margin="10" Padding="10" BackgroundColor="White" CornerRadius="8">
                            <StackLayout>
                                <Label Text="{Binding Name}" FontAttributes="Bold" FontSize="18" />
                                <Label Text="{Binding Phone}" />
                                <Label Text="{Binding LicenseNumber}" />
                                <Label Text="{Binding Notes}" />
                                <Button Text="Remove" Command="{Binding BindingContext.RemoveDriverCommand, Source={x:Reference Name=DriverListPage}}" />
                            </StackLayout>
                        </Frame>
                    </DataTemplate>
                </CollectionView.ItemTemplate>
            </CollectionView>
        </VerticalStackLayout>
    </ScrollView>
</ContentPage>
