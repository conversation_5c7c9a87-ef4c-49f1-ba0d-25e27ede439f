<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:vm="clr-namespace:CakeBistro.ViewModels"
             xmlns:model="clr-namespace:CakeBistro.Models"
             x:Class="CakeBistro.Views.ProductionBatchDetailPage"
             x:DataType="vm:ProductionBatchDetailViewModel"
             Title="{Binding IsNewBatch, Converter={StaticResource BoolToStringConverter}, ConverterParameter='New Production Batch|Edit Production Batch'}">

    <ScrollView>
        <VerticalStackLayout Spacing="10" Padding="20">
            <!-- Recipe Selection -->
            <Label Text="Recipe" />
            <Picker ItemsSource="{Binding AvailableRecipes}"
                    ItemDisplayBinding="{Binding Name}"
                    SelectedIndex="{Binding RecipeId, Converter={StaticResource IndexConverter}}"
                    Title="Select Recipe"
                    IsEnabled="{Binding IsNewBatch}"/>

            <!-- Planned Quantity -->
            <Label Text="Planned Quantity" />
            <Entry Text="{Binding PlannedQuantity}"
                   Keyboard="Numeric"
                   Placeholder="Enter planned quantity"/>

            <!-- Planned Start Date -->
            <Label Text="Planned Start Date" />
            <DatePicker Date="{Binding PlannedStartDate}"
                       MinimumDate="{Binding Source={x:Static system:DateTime.Today}}"/>

            <!-- Planned Cost -->
            <Label Text="Planned Cost" />
            <Grid ColumnDefinitions="*,Auto">
                <Entry Grid.Column="0"
                       Text="{Binding PlannedCost, StringFormat='{0:C}'}"
                       IsReadOnly="True"/>
                <Button Grid.Column="1"
                        Text="Calculate"
                        Command="{Binding UpdatePlannedCostCommand}"/>
            </Grid>

            <!-- Notes -->
            <Label Text="Notes" />
            <Editor Text="{Binding Notes}"
                    Placeholder="Enter any notes about this production batch"
                    AutoSize="TextChanges"
                    MaxLength="500"/>

            <!-- Cost Breakdown -->
            <Label Text="Cost Breakdown" FontAttributes="Bold" />
            <CollectionView ItemsSource="{Binding CostBreakdown}">
                <CollectionView.ItemTemplate>
                    <DataTemplate>
                        <HorizontalStackLayout>
                            <Label Text="{Binding Name}" WidthRequest="150" />
                            <Label Text="{Binding Cost, StringFormat='{}{0:C}'}" />
                        </HorizontalStackLayout>
                    </DataTemplate>
                </CollectionView.ItemTemplate>
            </CollectionView>
            <Label Text="Total Cost:" FontAttributes="Bold" />
            <Label Text="{Binding TotalCost, StringFormat='Total: {0:C}'}" />

            <!-- Analytics & Export Section -->
            <Label Text="Analytics & Profitability" FontAttributes="Bold" Margin="0,20,0,0" />
            <Grid ColumnDefinitions="*,*" RowDefinitions="Auto,Auto,Auto,Auto,Auto,Auto,Auto,Auto">
                <Label Grid.Row="0" Grid.Column="0" Text="Expected Revenue:" />
                <Label Grid.Row="0" Grid.Column="1" Text="{Binding ExpectedRevenue, StringFormat='{0:C}'}" />
                <Label Grid.Row="1" Grid.Column="0" Text="Expected Profit:" />
                <Label Grid.Row="1" Grid.Column="1" Text="{Binding ExpectedProfit, StringFormat='{0:C}'}" />
                <Label Grid.Row="2" Grid.Column="0" Text="Profit Margin:" />
                <Label Grid.Row="2" Grid.Column="1" Text="{Binding ProfitMargin, StringFormat='{0:F2}%'}" />
                <Label Grid.Row="3" Grid.Column="0" Text="Actual Quantity:" />
                <Entry Grid.Row="3" Grid.Column="1" Text="{Binding ActualQuantity}" Keyboard="Numeric" />
                <Label Grid.Row="4" Grid.Column="0" Text="Actual Revenue:" />
                <Entry Grid.Row="4" Grid.Column="1" Text="{Binding ActualRevenue}" Keyboard="Numeric" />
                <Label Grid.Row="5" Grid.Column="0" Text="Actual Profit:" />
                <Label Grid.Row="5" Grid.Column="1" Text="{Binding ActualProfit, StringFormat='{0:C}'}" />
                <Label Grid.Row="6" Grid.Column="0" Text="Actual Profit Margin:" />
                <Label Grid.Row="6" Grid.Column="1" Text="{Binding ActualProfitMargin, StringFormat='{0:F2}%'}" />
            </Grid>
            <Button Text="Export Analytics PDF" Command="{Binding ExportAnalyticsReportCommand}" Margin="0,10,0,0" />
            <Button Text="Export Analytics Excel" Command="{Binding ExportAnalyticsReportToExcelCommand}" Margin="0,5,0,0" />
            <Button Text="Export Analytics CSV" Command="{Binding ExportAnalyticsReportToCsvCommand}" Margin="0,5,0,0" />

            <!-- Manual Auto-calculate Button -->
            <Button Text="Auto-calculate Cost Fields"
                    Command="{Binding AutoCalculateCostFieldsCommand}"
                    Margin="0,10,0,0"
                    AutomationId="AutoCalculateCostFieldsButton"/>

            <!-- Advanced Cost Inputs -->
            <Label Text="Overhead Cost" />
            <Entry Text="{Binding OverheadCostPerBatch}" Keyboard="Numeric" Placeholder="Enter overhead cost" />
            <Label Text="Labor Cost" />
            <Entry Text="{Binding LaborCostPerBatch}" Keyboard="Numeric" Placeholder="Enter labor cost" />
            <Label Text="Utilities Cost" />
            <Entry Text="{Binding UtilitiesCostPerBatch}" Keyboard="Numeric" Placeholder="Enter utilities cost" />
            <Label Text="Packaging Cost" />
            <Entry Text="{Binding PackagingCostPerBatch}" Keyboard="Numeric" Placeholder="Enter packaging cost" />
            <Label Text="Wastage Cost" />
            <Entry Text="{Binding WastageCostPerBatch}" Keyboard="Numeric" Placeholder="Enter wastage cost" />

            <!-- Advanced Analytics -->
            <Label Text="Cost Per Unit:" />
            <Label Text="{Binding CostPerUnit, StringFormat='{0:C}'}" />
            <Label Text="Actual Cost Per Unit:" />
            <Label Text="{Binding ActualCostPerUnit, StringFormat='{0:C}'}" />
            <Label Text="Gross Margin:" />
            <Label Text="{Binding GrossMargin, StringFormat='{0:F2}%'}" />
            <Label Text="Net Margin:" />
            <Label Text="{Binding NetMargin, StringFormat='{0:F2}%'}" />
            <Label Text="Batch Efficiency:" />
            <Label Text="{Binding BatchEfficiency, StringFormat='{0:F2}%'}" />
            <Label Text="Wastage Percentage:" />
            <Label Text="{Binding WastagePercentage, StringFormat='{0:F2}%'}" />

            <!-- Action Buttons -->
            <HorizontalStackLayout Spacing="10" Margin="0,20,0,0" HorizontalOptions="Center">
                <Button Text="Save" Command="{Binding SaveBatchCommand}" />
                <Button Text="Cancel" Command="{Binding CancelCommand}" />
                <Button Text="Start Batch" Command="{Binding StartBatchCommand}" />
                <Button Text="Complete Batch" Command="{Binding CompleteBatchCommand}" />
            </HorizontalStackLayout>
        </VerticalStackLayout>
    </ScrollView>
</ContentPage>
