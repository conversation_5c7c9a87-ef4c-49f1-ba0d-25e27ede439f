using System.Collections.Generic;

namespace CakeBistro.Core.Models
{
    public class Recipe : BaseEntity
    {
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public int ProductId { get; set; }
        public Product Product { get; set; }
        public ICollection<RecipeItem> RecipeItems { get; set; } = new List<RecipeItem>();
        public decimal EstimatedCost { get; set; }
    }

    public class RecipeItem : BaseEntity
    {
        public int RecipeId { get; set; }
        public Recipe Recipe { get; set; }
        public int RawMaterialId { get; set; }
        public RawMaterial RawMaterial { get; set; }
        public decimal Quantity { get; set; }
    }
}
