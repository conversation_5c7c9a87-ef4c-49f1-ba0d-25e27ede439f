using System;
using System.Collections.Generic;

namespace CakeBistro.Models
{
    public class AnalysisReport
    {
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public decimal TotalSales { get; set; }
        public decimal TotalProfit { get; set; }
        public List<ProductAnalysis> ProductAnalyses { get; set; } = new();
    }

    public class ProductAnalysis
    {
        public int ProductId { get; set; }
        public string? ProductName { get; set; }
        public decimal TotalSales { get; set; }
        public decimal TotalProfit { get; set; }
    }
}
