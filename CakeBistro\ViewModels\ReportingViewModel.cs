using System;
using System.Collections.ObjectModel;
using System.Threading.Tasks;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using CakeBistro.Services;
using CakeBistro.Models;
using Microsoft.UI.Xaml.Media;

namespace CakeBistro.ViewModels;

public partial class ReportingViewModel : ObservableObject
{
    private readonly ReportingService _reportingService;

    [ObservableProperty]
    private string statusMessage;

    [ObservableProperty]
    private bool isProcessing;

    [ObservableProperty]
    private InventoryValuationReport currentInventoryReport;

    [ObservableProperty]
    private Color statusColor = Colors.Transparent;

    public ObservableCollection<InventoryValuationReport> InventoryReports { get; } = new();

    public IRelayCommand GenerateInventoryReportCommand { get; }
    public IRelayCommand ExportInventoryReportCommand { get; }

    public ReportingViewModel(ReportingService reportingService)
    {
        _reportingService = reportingService;
        GenerateInventoryReportCommand = new AsyncRelayCommand(GenerateInventoryReportAsync);
        ExportInventoryReportCommand = new AsyncRelayCommand(ExportInventoryReportAsync, CanExport);
    }

    private async Task GenerateInventoryReportAsync()
    {
        IsProcessing = true;
        StatusMessage = string.Empty;
        StatusColor = Colors.Transparent;
        try
        {
            var report = await _reportingService.GenerateInventoryValuationReportAsync(DateTime.Now);
            CurrentInventoryReport = report;
            InventoryReports.Add(report);
            StatusMessage = "Inventory valuation report generated.";
            StatusColor = Color.FromArgb("#388E3C"); // Success
        }
        catch (Exception ex)
        {
            StatusMessage = $"Error: {ex.Message}";
            StatusColor = Color.FromArgb("#D32F2F"); // Error
        }
        finally
        {
            IsProcessing = false;
        }
    }

    private bool CanExport() => CurrentInventoryReport != null && !IsProcessing;

    private async Task ExportInventoryReportAsync()
    {
        if (CurrentInventoryReport == null) return;
        IsProcessing = true;
        StatusColor = Colors.Transparent;
        try
        {
            var fileName = $"InventoryValuationReport_{DateTime.Now:yyyyMMdd_HHmmss}.pdf";
            var documents = Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments);
            var filePath = Path.Combine(documents, fileName);
            var resultPath = await _reportingService.ExportInventoryValuationReportToPdfAsync(CurrentInventoryReport, filePath);
            StatusMessage = $"Report exported to PDF: {resultPath}";
            StatusColor = Color.FromArgb("#388E3C"); // Success
        }
        catch (Exception ex)
        {
            StatusMessage = $"Export failed: {ex.Message}";
            StatusColor = Color.FromArgb("#D32F2F"); // Error
        }
        finally
        {
            IsProcessing = false;
        }
    }
}
