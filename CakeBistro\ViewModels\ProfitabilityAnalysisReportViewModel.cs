using System.Collections.ObjectModel;
using System.Windows.Input;
using CakeBistro.Core.Models;
using CakeBistro.Services;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;

namespace CakeBistro.ViewModels
{
    public partial class ProfitabilityAnalysisReportViewModel : ObservableObject
    {
        private readonly IProductionService _productionService;
        public ObservableCollection<Product> Products { get; } = new();
        [ObservableProperty]
        private Product selectedProduct;
        [ObservableProperty]
        private string profitabilityResult;

        public ProfitabilityAnalysisReportViewModel(IProductionService productionService)
        {
            _productionService = productionService;
            LoadProducts();
        }

        private async void LoadProducts()
        {
            var products = await _productionService.GetAllProductsAsync();
            Products.Clear();
            foreach (var p in products)
                Products.Add(p);
        }

        [RelayCommand]
        private async Task AnalyzeProfitability()
        {
            if (SelectedProduct != null)
                ProfitabilityResult = await _productionService.AnalyzeProfitabilityAsync(SelectedProduct.Id);
        }
    }
}
