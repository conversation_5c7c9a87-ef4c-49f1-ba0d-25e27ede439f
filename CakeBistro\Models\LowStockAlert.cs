
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace CakeBistro.Models
{
    /// <summary>
    /// Represents a low stock alert for inventory items
    /// </summary>
    [Table("LowStockAlerts")]
    public class LowStockAlert
    {
        /// <summary>
        /// Gets or sets the unique identifier of the alert
        /// </summary>
        [Key]
        public int Id { get; set; }

        /// <summary>
        /// Gets or sets the ID of the item that triggered the alert
        /// </summary>
        public int ItemId { get; set; }

        /// <summary>
        /// Gets or sets the type of item (RawMaterial or FinishedProduct)
        /// </summary>
        public string ItemType { get; set; }

        /// <summary>
        /// Gets or sets the current stock level at the time of alert
        /// </summary>
        public decimal CurrentStock { get; set; }

        /// <summary>
        /// Gets or sets the minimum stock threshold for the item
        /// </summary>
        public decimal MinimumStock { get; set; }

        /// <summary>
        /// Gets or sets the date and time when the alert was triggered
        /// </summary>
        public DateTime TriggeredDate { get; set; }

        /// <summary>
        /// Gets or sets the date and time when the alert was last updated
        /// </summary>
        public DateTime? LastUpdated { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether this alert has been resolved
        /// </summary>
        public bool IsResolved { get; set; }

        /// <summary>
        /// Gets or sets the date and time when the alert was resolved
        /// </summary>
        public DateTime? ResolvedDate { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the alert has been acknowledged
        /// </summary>
        public bool? AlertAcknowledged { get; set; }

        /// <summary>
        /// Gets or sets the message describing the alert
        /// </summary>
        public string AlertMessage { get; set; }
    }
}