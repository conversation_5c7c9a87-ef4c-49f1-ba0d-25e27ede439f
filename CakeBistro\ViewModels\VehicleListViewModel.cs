using CakeBistro.Models;
using CakeBistro.Services;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.Collections.ObjectModel;
using System.Threading.Tasks;

namespace CakeBistro.ViewModels
{
    public partial class VehicleListViewModel : ObservableObject
    {
        private readonly VehicleService _vehicleService;

        [ObservableProperty]
        private ObservableCollection<Vehicle> vehicles = new();
        [ObservableProperty]
        private string? newRegistrationNumber;
        [ObservableProperty]
        private string? newMake;
        [ObservableProperty]
        private string? newModel;
        [ObservableProperty]
        private string? newColor;
        [ObservableProperty]
        private string? newNotes;
        [ObservableProperty]
        private Vehicle? selectedVehicle;

        public VehicleListViewModel(VehicleService vehicleService)
        {
            _vehicleService = vehicleService;
        }

        [RelayCommand]
        public async Task LoadVehiclesAsync()
        {
            var list = await _vehicleService.GetAllVehiclesAsync();
            Vehicles = new ObservableCollection<Vehicle>(list);
        }

        [RelayCommand]
        public async Task AddVehicleAsync()
        {
            if (!string.IsNullOrWhiteSpace(NewRegistrationNumber))
            {
                var vehicle = new Vehicle
                {
                    RegistrationNumber = NewRegistrationNumber!,
                    Make = NewMake ?? string.Empty,
                    Model = NewModel ?? string.Empty,
                    Color = NewColor ?? string.Empty,
                    Notes = NewNotes
                };
                await _vehicleService.CreateVehicleAsync(vehicle);
                await LoadVehiclesAsync();
                NewRegistrationNumber = null;
                NewMake = null;
                NewModel = null;
                NewColor = null;
                NewNotes = null;
            }
        }

        [RelayCommand]
        public async Task RemoveVehicleAsync()
        {
            if (SelectedVehicle != null)
            {
                await _vehicleService.RemoveVehicleAsync(SelectedVehicle.Id);
                await LoadVehiclesAsync();
            }
        }
    }
}
