using CakeBistro.Core.Models;

namespace CakeBistro.Repositories
{
    // Interface for sales order item repository operations
    public interface ISalesOrderItemRepository : IRepository<SalesOrderItem>
    {
        // Get all sales order items
        Task<IEnumerable<SalesOrderItem>> GetAllAsync();
        
        // Get sales order item by ID
        Task<SalesOrderItem> GetByIdAsync(Guid id);
        
        // Add a new sales order item
        Task AddAsync(SalesOrderItem item);
        
        // Update an existing sales order item
        Task UpdateAsync(SalesOrderItem item);
        
        // Delete a sales order item
        Task DeleteAsync(Guid id);
        
        // Get items for a specific sales order
        Task<IEnumerable<SalesOrderItem>> GetItemsByOrderAsync(Guid orderId);
    }
}
