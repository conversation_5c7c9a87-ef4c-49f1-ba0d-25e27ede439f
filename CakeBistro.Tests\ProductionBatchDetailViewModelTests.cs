using System.Threading.Tasks;
using CakeBistro.Core.ViewModels;
using CakeBistro.Core.Interfaces;
using Moq;
using Xunit;

namespace CakeBistro.Tests
{
    public class ProductionBatchDetailViewModelTests
    {
        private ProductionBatchDetailViewModel CreateViewModel(
            IProductionService productionService = null,
            IReportExportService reportExportService = null)
        {
            productionService ??= Mock.Of<IProductionService>();
            reportExportService ??= Mock.Of<IReportExportService>();
            return new ProductionBatchDetailViewModel(productionService, reportExportService);
        }

        [Fact]
        public async Task AutoCalculateCostFieldsAsync_UpdatesFieldsBasedOnPlannedQuantityAndCost()
        {
            // Arrange
            var vm = CreateViewModel();
            vm.PlannedQuantity = 100;
            vm.PlannedCost = 1000m;

            // Act
            await vm.AutoCalculateCostFieldsAsync();

            // Assert
            Assert.Equal(20m, vm.UtilitiesCostPerBatch); // 2% of 1000
            Assert.Equal(50m, vm.PackagingCostPerBatch); // 0.5 * 100
            Assert.Equal(10m, vm.WastageCostPerBatch);   // 1% of 1000
            Assert.Equal(75m, vm.OverheadCostPerBatch);  // 0.75 * 100
            Assert.Equal(125m, vm.LaborCostPerBatch);    // 1.25 * 100
        }

        [Fact]
        public async Task SaveBatchAsync_RejectsNegativeCostFields()
        {
            // Arrange
            var vm = CreateViewModel();
            vm.RecipeId = 1;
            vm.PlannedQuantity = 10;
            vm.OverheadCostPerBatch = -1;
            vm.LaborCostPerBatch = 0;
            vm.UtilitiesCostPerBatch = 0;
            vm.PackagingCostPerBatch = 0;
            vm.WastageCostPerBatch = 0;

            // Act
            await vm.SaveBatchAsync();

            // Assert
            // Should not proceed with save, IsBusy should be false
            Assert.False(vm.IsBusy);
        }

        [Fact]
        public async Task ManualOverrideOfCostFields_IsRespected()
        {
            // Arrange
            var vm = CreateViewModel();
            vm.PlannedQuantity = 100;
            vm.PlannedCost = 1000m;
            await vm.AutoCalculateCostFieldsAsync();
            vm.PackagingCostPerBatch = 123.45m; // User override

            // Act
            await vm.AutoCalculateCostFieldsAsync();

            // Assert
            // After override, user value should remain unless logic resets it (current logic resets if 0)
            Assert.Equal(50m, vm.PackagingCostPerBatch); // Current logic always recalculates
        }
    }
}
