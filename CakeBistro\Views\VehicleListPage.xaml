<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:viewmodels="clr-namespace:CakeBistro.ViewModels"
             x:Class="CakeBistro.Views.VehicleListPage">
    <ContentPage.BindingContext>
        <viewmodels:VehicleListViewModel />
    </ContentPage.BindingContext>
    <ScrollView>
        <VerticalStackLayout Padding="16">
            <Label Text="Vehicles" FontSize="24" FontAttributes="Bold" />
            <Entry Placeholder="Registration Number" Text="{Binding NewRegistrationNumber}" />
            <Entry Placeholder="Make" Text="{Binding NewMake}" />
            <Entry Placeholder="Model" Text="{Binding NewModel}" />
            <Entry Placeholder="Color" Text="{Binding NewColor}" />
            <Entry Placeholder="Notes" Text="{Binding NewNotes}" />
            <Button Text="Add Vehicle" Command="{Binding AddVehicleCommand}" />
            <CollectionView ItemsSource="{Binding Vehicles}" SelectionMode="Single" SelectedItem="{Binding SelectedVehicle, Mode=TwoWay}">
                <CollectionView.ItemTemplate>
                    <DataTemplate>
                        <Frame Margin="10" Padding="10" BackgroundColor="White" CornerRadius="8">
                            <StackLayout>
                                <Label Text="{Binding RegistrationNumber}" FontAttributes="Bold" FontSize="18" />
                                <Label Text="{Binding Make}" />
                                <Label Text="{Binding Model}" />
                                <Label Text="{Binding Color}" />
                                <Label Text="{Binding Notes}" />
                                <Button Text="Remove" Command="{Binding BindingContext.RemoveVehicleCommand, Source={x:Reference Name=VehicleListPage}}" />
                            </StackLayout>
                        </Frame>
                    </DataTemplate>
                </CollectionView.ItemTemplate>
            </CollectionView>
        </VerticalStackLayout>
    </ScrollView>
</ContentPage>
