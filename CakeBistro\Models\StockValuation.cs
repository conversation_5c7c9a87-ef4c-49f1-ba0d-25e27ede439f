using System;
using System.Collections.Generic;

namespace CakeBistro.Models
{
    public class StockValuation
    {
        public DateTime AsOfDate { get; set; }
        public decimal TotalValue { get; set; }
        public List<StockItemValuation> Items { get; set; } = new();
        public decimal TotalStockValue { get; set; }
        public int LowStockCount { get; set; }
        public DateTime? LastStockTake { get; set; }
        public DateTime? NextReorderDate { get; set; }
        public List<string>? Recommendations { get; set; }
        public string? Error { get; set; }
    }

    public class StockItemValuation
    {
        public int ProductId { get; set; }
        public string? ProductName { get; set; }
        public decimal Quantity { get; set; }
        public decimal UnitCost { get; set; }
        public decimal TotalCost { get; set; }
    }
}
