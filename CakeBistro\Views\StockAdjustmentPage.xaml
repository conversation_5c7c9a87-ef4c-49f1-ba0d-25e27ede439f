<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="CakeBistro.Views.StockAdjustmentPage"
             Title="Stock Adjustment">
    <StackLayout Padding="20">
        <Label Text="Stock Adjustments" FontSize="24" FontAttributes="Bold" HorizontalOptions="Center" />
        <CollectionView x:Name="StockAdjustmentListView" SelectionMode="Single" SelectionChanged="OnStockAdjustmentSelected">
            <CollectionView.ItemTemplate>
                <DataTemplate>
                    <SwipeView>
                        <SwipeView.RightItems>
                            <SwipeItems>
                                <SwipeItem Text="Delete" BackgroundColor="Red" Invoked="OnDeleteStockAdjustment" CommandParameter="{Binding .}"/>
                            </SwipeItems>
                        </SwipeView.RightItems>
                        <Frame Margin="5" Padding="10" BorderColor="#ccc">
                            <StackLayout>
                                <Label Text="{Binding Date, StringFormat='Date: {0:yyyy-MM-dd}'}" FontSize="16" />
                                <Label Text="{Binding Reason}" FontSize="14" TextColor="#512BD4" />
                                <Label Text="{Binding Notes}" FontSize="14" TextColor="Gray" />
                            </StackLayout>
                        </Frame>
                    </SwipeView>
                </DataTemplate>
            </CollectionView.ItemTemplate>
        </CollectionView>
        <Button Text="Add Stock Adjustment" Margin="0,20,0,0" Clicked="OnAddStockAdjustmentClicked" />
    </StackLayout>
</ContentPage>
