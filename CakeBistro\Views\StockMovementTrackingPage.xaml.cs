using CakeBistro.ViewModels;
using Microsoft.Maui.Controls;
using System;

namespace CakeBistro.Views
{
    public partial class StockMovementTrackingPage : ContentPage
    {
        public StockMovementTrackingPage(StockMovementTrackingViewModel vm)
        {
            InitializeComponent();
            BindingContext = vm;
        }

        private void OnRecordMovementClicked(object sender, EventArgs e)
        {
            // TODO: Add stock movement logic and animated feedback
            DisplayAlert("Success", "Stock movement recorded!", "OK");
        }
    }
}
