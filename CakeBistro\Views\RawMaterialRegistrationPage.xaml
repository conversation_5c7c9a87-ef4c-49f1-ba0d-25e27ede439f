<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:controls="clr-namespace:CakeBistro.Controls"
             x:Class="CakeBistro.Views.RawMaterialRegistrationPage"
             Title="Register Raw Material">
    <ScrollView>
        <VerticalStackLayout Padding="20" Spacing="16">
            <controls:CardView>
                <StackLayout>
                    <Label Text="Raw Material Name" FontAttributes="Bold"/>
                    <Entry x:Name="NameEntry" Placeholder="Enter name" />
                    <Label Text="Category" FontAttributes="Bold"/>
                    <Entry x:Name="CategoryEntry" Placeholder="Enter category" />
                    <Label Text="Unit Price" FontAttributes="Bold"/>
                    <Entry x:Name="PriceEntry" Placeholder="Enter price" Keyboard="Numeric" />
                    <Label Text="Supplier" FontAttributes="Bold"/>
                    <Picker x:Name="SupplierPicker" />
                    <Button Text="Register" Clicked="OnRegisterClicked" StyleClass="PrimaryButton AnimatedButton" />
                </StackLayout>
            </controls:CardView>
            <Label Text="{Binding StatusMessage}"
                   IsVisible="{Binding StatusMessage, Converter={StaticResource EmptyToFalseConverter}}"
                   TextColor="{Binding StatusColor}" />
        </VerticalStackLayout>
    </ScrollView>
</ContentPage>
