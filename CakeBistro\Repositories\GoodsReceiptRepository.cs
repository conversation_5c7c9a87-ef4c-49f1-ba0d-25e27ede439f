using CakeBistro.Core.Models;
using CakeBistro.Core.Data;
using Microsoft.EntityFrameworkCore;

namespace CakeBistro.Repositories
{
    public class GoodsReceiptRepository : CakeBistro.Core.Interfaces.IRepository<GoodsReceipt>
    {
        private readonly CakeBistroContext _context;
        public GoodsReceiptRepository(CakeBistroContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<GoodsReceipt>> GetAllAsync()
        {
            return await _context.GoodsReceipts.ToListAsync();
        }

        public async Task<GoodsReceipt> GetByIdAsync(Guid id)
        {
            return await _context.GoodsReceipts.FindAsync(id);
        }

        public async Task<GoodsReceipt> AddAsync(GoodsReceipt receipt)
        {
            var entry = await _context.GoodsReceipts.AddAsync(receipt);
            await _context.SaveChangesAsync();
            return entry.Entity;
        }

        public async Task UpdateAsync(GoodsReceipt receipt)
        {
            _context.GoodsReceipts.Update(receipt);
            await _context.SaveChangesAsync();
        }

        public async Task DeleteAsync(Guid id)
        {
            var receipt = await _context.GoodsReceipts.FindAsync(id);
            if (receipt != null)
            {
                _context.GoodsReceipts.Remove(receipt);
                await _context.SaveChangesAsync();
            }
        }

        public async Task<IEnumerable<GoodsReceipt>> GetReceiptsByPurchaseOrderAsync(Guid purchaseOrderId)
        {
            return await _context.GoodsReceipts
                .Where(r => r.PurchaseOrderId == purchaseOrderId)
                .ToListAsync();
        }

        public async Task<IEnumerable<GoodsReceipt>> GetReceiptsByDateRangeAsync(DateTime startDate, DateTime endDate)
        {
            return await _context.GoodsReceipts
                .Where(r => r.ReceiptDate >= startDate && r.ReceiptDate <= endDate)
                .ToListAsync();
        }
    }
}
