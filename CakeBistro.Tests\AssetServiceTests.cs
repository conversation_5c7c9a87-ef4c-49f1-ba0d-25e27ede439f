// Create AssetServiceTests.cs
using CakeBistro.Services;
using CakeBistro.Core.Models;
using CakeBistro.Repositories;
using Microsoft.EntityFrameworkCore;
using Xunit;

namespace CakeBistro.Tests;

public class AssetServiceTests
{
    private readonly CakeBistroContext _context;
    private readonly IAssetService _assetService;
    private readonly IRepository<FixedAsset> _assetRepository;
    private readonly IRepository<InventoryBatch> _batchRepository;
    private readonly IRepository<DepreciationRecord> _depreciationRepository;
    private readonly IRepository<MaintenanceRecord> _maintenanceRepository;

    public AssetServiceTests()
    {
        // Set up in-memory database
        var options = new DbContextOptionsBuilder<CakeBistroContext>()
            .UseInMemoryDatabase(databaseName: "TestDatabase")
            .Options;

        _context = new CakeBistroContext(options);
        
        // Initialize repositories
        _assetRepository = new AssetRepository(_context);
        _batchRepository = new BaseRepository<InventoryBatch>(_context);
        _depreciationRepository = new DepreciationRepository(_context);
        _maintenanceRepository = new MaintenanceRepository(_context);
        
        // Initialize service with test repositories
        _assetService = new AssetService(
            _assetRepository,
            _batchRepository,
            _depreciationRepository,
            _maintenanceRepository);
    }

    [Fact]
    public async Task AddAssetAsync_ShouldAddAsset()
    {
        // Arrange
        var asset = new FixedAsset
        {
            Name = "Test Asset",
            Description = "Test Description",
            Category = "Equipment",
            AcquisitionDate = DateTime.Now,
            AcquisitionCost = 1000.0m,
            Location = "Warehouse"
        };

        // Act
        var result = await _assetService.AddAssetAsync(asset);
        
        // Assert
        Assert.NotNull(result);
        Assert.Equal("Test Asset", result.Name);
        Assert.True(result.Id > 0);
    }

    [Fact]
    public async Task GetAssetByIdAsync_ShouldReturnAsset()
    {
        // Arrange
        var asset = new FixedAsset
        {
            Name = "Test Asset",
            Description = "Test Description",
            Category = "Equipment",
            AcquisitionDate = DateTime.Now,
            AcquisitionCost = 1000.0m,
            Location = "Warehouse"
        };
        
        var addedAsset = await _assetService.AddAssetAsync(asset);
        
        // Act
        var result = await _assetService.GetAssetByIdAsync(addedAsset.Id);
        
        // Assert
        Assert.NotNull(result);
        Assert.Equal(addedAsset.Id, result.Id);
        Assert.Equal("Test Asset", result.Name);
    }

    [Fact]
    public async Task UpdateAssetAsync_ShouldUpdateAsset()
    {
        // Arrange
        var asset = new FixedAsset
        {
            Name = "Test Asset",
            Description = "Test Description",
            Category = "Equipment",
            AcquisitionDate = DateTime.Now,
            AcquisitionCost = 1000.0m,
            Location = "Warehouse"
        };
        
        var addedAsset = await _assetService.AddAssetAsync(asset);
        
        // Modify asset
        addedAsset.Name = "Updated Asset";
        addedAsset.Location = "Office";
        
        // Act
        await _assetService.UpdateAssetAsync(addedAsset);
        
        // Get updated asset
        var result = await _assetService.GetAssetByIdAsync(addedAsset.Id);
        
        // Assert
        Assert.NotNull(result);
        Assert.Equal("Updated Asset", result.Name);
        Assert.Equal("Office", result.Location);
    }

    [Fact]
    public async Task DeleteAssetAsync_ShouldRemoveAsset()
    {
        // Arrange
        var asset = new FixedAsset
        {
            Name = "Test Asset",
            Description = "Test Description",
            Category = "Equipment",
            AcquisitionDate = DateTime.Now,
            AcquisitionCost = 1000.0m,
            Location = "Warehouse"
        };
        
        var addedAsset = await _assetService.AddAssetAsync(asset);
        
        // Act
        await _assetService.DeleteAssetAsync(addedAsset.Id);
        
        // Try to get deleted asset
        var result = await _assetService.GetAssetByIdAsync(addedAsset.Id);
        
        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task RecordDepreciationAsync_ShouldRecordDepreciation()
    {
        // Arrange
        var asset = new FixedAsset
        {
            Name = "Test Asset",
            Description = "Test Description",
            Category = "Equipment",
            AcquisitionDate = DateTime.Now,
            AcquisitionCost = 1000.0m,
            Location = "Warehouse"
        };
        
        var addedAsset = await _assetService.AddAssetAsync(asset);
        
        var depreciation = new DepreciationRecord
        {
            AssetId = addedAsset.Id,
            Date = DateTime.Now,
            Amount = 200.0m,
            Description = "Annual Depreciation"
        };
        
        // Act
        var result = await _assetService.RecordDepreciationAsync(depreciation);
        
        // Assert
        Assert.NotNull(result);
        Assert.Equal(addedAsset.Id, result.AssetId);
        Assert.Equal(200.0m, result.Amount);
    }

    [Fact]
    public async Task CalculateNetBookValueAsync_ShouldCalculateCorrectly()
    {
        // Arrange
        var asset = new FixedAsset
        {
            Name = "Test Asset",
            Description = "Test Description",
            Category = "Equipment",
            AcquisitionDate = DateTime.Now,
            AcquisitionCost = 1000.0m,
            Location = "Warehouse"
        };
        
        var addedAsset = await _assetService.AddAssetAsync(asset);
        
        var depreciation1 = new DepreciationRecord
        {
            AssetId = addedAsset.Id,
            Date = DateTime.Now,
            Amount = 200.0m,
            Description = "Year 1 Depreciation"
        };
        
        var depreciation2 = new DepreciationRecord
        {
            AssetId = addedAsset.Id,
            Date = DateTime.Now,
            Amount = 200.0m,
            Description = "Year 2 Depreciation"
        };
        
        await _assetService.RecordDepreciationAsync(depreciation1);
        await _assetService.RecordDepreciationAsync(depreciation2);
        
        // Act
        var netBookValue = await _assetService.CalculateNetBookValueAsync(addedAsset.Id);
        
        // Assert
        Assert.Equal(600.0m, netBookValue); // 1000 - 200 - 200 = 600
    }

    [Fact]
    public async Task TransferAssetAsync_ShouldUpdateLocation()
    {
        // Arrange
        var asset = new FixedAsset
        {
            Name = "Test Asset",
            Description = "Test Description",
            Category = "Equipment",
            AcquisitionDate = DateTime.Now,
            AcquisitionCost = 1000.0m,
            Location = "Warehouse"
        };
        
        var addedAsset = await _assetService.AddAssetAsync(asset);
        
        // Act
        var result = await _assetService.TransferAssetAsync(addedAsset.Id, "Office", "Relocating to office");
        
        // Assert
        Assert.True(result.Success);
        Assert.Contains("successfully", result.Message);
        
        // Verify location was updated
        var updatedAsset = await _assetService.GetAssetByIdAsync(addedAsset.Id);
        Assert.Equal("Office", updatedAsset.Location);
    }
}