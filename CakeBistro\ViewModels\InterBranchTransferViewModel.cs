using System;
using System.Collections.ObjectModel;
using System.Threading.Tasks;
using System.Windows.Input;
using CakeBistro.Core.Models;
using CakeBistro.Services;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace CakeBistro.ViewModels
{
    public partial class InterBranchTransferViewModel : ObservableObject
    {
        private readonly InventoryService _inventoryService;
        private readonly ILogger<InterBranchTransferViewModel> _logger;
        
        [ObservableProperty]
        private ObservableCollection<InterBranchTransfer> _transfers = new();
        [ObservableProperty]
        private InterBranchTransfer _selectedTransfer = new();
        [ObservableProperty]
        private string _statusMessage;

        private Color _statusColor = Colors.Transparent;
        public Color StatusColor
        {
            get => _statusColor;
            set => SetProperty(ref _statusColor, value);
        }

        public InterBranchTransferViewModel(InventoryService inventoryService, ILogger<InterBranchTransferViewModel> logger)
        {
            _inventoryService = inventoryService;
            _logger = logger;
            LoadTransfersCommand = new AsyncRelayCommand(LoadTransfersAsync);
            CreateTransferCommand = new AsyncRelayCommand(CreateTransferAsync);
            UpdateStatusCommand = new AsyncRelayCommand<InterBranchTransferStatus>(UpdateStatusAsync);
        }

        public ICommand LoadTransfersCommand { get; }
        public ICommand CreateTransferCommand { get; }
        public ICommand UpdateStatusCommand { get; }

        private async Task LoadTransfersAsync()
        {
            var list = await _inventoryService.GetAllInterBranchTransfersAsync();
            Transfers = new ObservableCollection<InterBranchTransfer>(list);
        }

        private async Task CreateTransferAsync()
        {
            try
            {
                var result = await _inventoryService.CreateInterBranchTransferAsync(SelectedTransfer);
                StatusMessage = "Transfer created successfully.";
                StatusColor = Color.FromArgb("#388E3C");
                await LoadTransfersAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating transfer: {Message}", ex.Message);
                StatusMessage = $"Error: {ex.Message}";
                StatusColor = Color.FromArgb("#D32F2F");
            }
        }

        private async Task UpdateStatusAsync(InterBranchTransferStatus status)
        {
            try
            {
                await _inventoryService.UpdateInterBranchTransferStatusAsync(SelectedTransfer.Id, status);
                StatusMessage = "Status updated.";
                StatusColor = Color.FromArgb("#1976D2");
                await LoadTransfersAsync();
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error: {ex.Message}";
                StatusColor = Color.FromArgb("#D32F2F");
            }
        }
    }
}
