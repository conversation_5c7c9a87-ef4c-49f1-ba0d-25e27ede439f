// Update this using directive to match the actual namespace of MainViewModel
using CakeBistro.ViewModels;

// If MainViewModel is in a different namespace, update the using statement accordingly, for example:
// using CakeBistro.YourActualNamespace;

// If you do not have a MainViewModel class, define a minimal one for now:
namespace CakeBistro.ViewModels
{
    public class MainViewModel
    {
        public Task LoadDashboardDataAsync() => Task.CompletedTask;
        // Add properties as needed for binding
    }
}

namespace CakeBistro
{
    public partial class MainPage : ContentPage
    {
        private readonly MainViewModel _viewModel;

        public MainPage(MainViewModel viewModel)
        {
            // InitializeComponent() is generated from MainPage.xaml
            // Ensure MainPage.xaml exists and has proper build action
            InitializeComponent();
            _viewModel = viewModel;
            BindingContext = _viewModel;
        }

        private void InitializeComponent()
        {
            throw new NotImplementedException();
        }

        protected override async void OnAppearing()
        {
            base.OnAppearing();
            await LoadDashboardDataAsync();
        }

        private async Task LoadDashboardDataAsync()
        {
            try
            {
                await _viewModel.LoadDashboardDataAsync();

                // Update UI labels with actual data
                // TotalRawMaterialsLabel.Text = _viewModel.TotalRawMaterials.ToString(); // Label not found in XAML
                // TotalSuppliersLabel.Text = _viewModel.TotalSuppliers.ToString(); // Label not found in XAML
                // LowStockItemsLabel.Text = _viewModel.LowStockItems.ToString(); // Label not found in XAML
                // RecentOrdersLabel.Text = _viewModel.RecentOrders.ToString(); // Label not found in XAML
                // StockMovementsLabel.Text = _viewModel.StockMovements.ToString(); // Label not found in XAML
                // InterBranchTransfersLabel.Text = _viewModel.InterBranchTransfers.ToString(); // Label not found in XAML
                // MonthlyStockTakeLabel.Text = _viewModel.MonthlyStockTake.ToString(); // Label not found in XAML
                // StockAdjustmentsLabel.Text = _viewModel.StockAdjustments.ToString(); // Label not found in XAML
            }
            catch (Exception ex)
            {
                await DisplayAlert("Error", $"Failed to load dashboard data: {ex.Message}", "OK");
            }
        }

        private async void OnAddRawMaterialClicked(object sender, EventArgs e)
        {
            await Shell.Current.GoToAsync("rawmaterials");
        }

        private async void OnCreatePurchaseOrderClicked(object sender, EventArgs e)
        {
            await Shell.Current.GoToAsync("purchaseorderform");
        }

        private async void OnViewInventoryClicked(object sender, EventArgs e)
        {
            await Shell.Current.GoToAsync("inventory");
        }

        private async void OnGenerateReportsClicked(object sender, EventArgs e)
        {
            await Shell.Current.GoToAsync("reports");
        }

        private async void OnRecordStockMovementClicked(object sender, EventArgs e)
        {
            await Shell.Current.GoToAsync("stockmovements");
        }

        private async void OnInterBranchTransferClicked(object sender, EventArgs e)
        {
            await Shell.Current.GoToAsync("interbranchtransfers");
        }

        private async void OnMonthlyStockTakeClicked(object sender, EventArgs e)
        {
            await Shell.Current.GoToAsync("stocktakeform");
        }

        private async void OnStockAdjustmentClicked(object sender, EventArgs e)
        {
            await Shell.Current.GoToAsync("stockadjustmentform");
        }
    }
}
