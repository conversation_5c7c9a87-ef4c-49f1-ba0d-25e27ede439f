<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:viewmodels="clr-namespace:CakeBistro.ViewModels"
             x:Class="CakeBistro.Views.AssetListPage">
    <ContentPage.BindingContext>
        <viewmodels:AssetListViewModel />
    </ContentPage.BindingContext>
    <ScrollView>
        <VerticalStackLayout Padding="16">
            <Label Text="Assets" FontSize="24" FontAttributes="Bold" />
            <Entry Placeholder="Name" Text="{Binding Name}" />
            <Entry Placeholder="Category" Text="{Binding Category}" />
            <Entry Placeholder="Value" Keyboard="Numeric" Text="{Binding Value}" />
            <Entry Placeholder="Location" Text="{Binding Location}" />
            <Entry Placeholder="Notes" Text="{Binding Notes}" />
            <Button Text="Add Asset" Command="{Binding AddAssetCommand}" />
            <CollectionView ItemsSource="{Binding Assets}" SelectionMode="Single" SelectedItem="{Binding SelectedAsset, Mode=TwoWay}">
                <CollectionView.ItemTemplate>
                    <DataTemplate>
                        <Frame Margin="10" Padding="10" BackgroundColor="White" CornerRadius="8">
                            <StackLayout>
                                <Label Text="{Binding Name}" FontAttributes="Bold" FontSize="18" />
                                <Label Text="{Binding Category}" />
                                <Label Text="{Binding Value, StringFormat='Value: {0:C}'}" />
                                <Label Text="{Binding Location}" />
                                <Label Text="{Binding Notes}" />
                                <Button Text="Remove" Command="{Binding BindingContext.RemoveAssetCommand, Source={x:Reference Name=AssetListPage}}" />
                            </StackLayout>
                        </Frame>
                    </DataTemplate>
                </CollectionView.ItemTemplate>
            </CollectionView>
        </VerticalStackLayout>
    </ScrollView>
</ContentPage>
