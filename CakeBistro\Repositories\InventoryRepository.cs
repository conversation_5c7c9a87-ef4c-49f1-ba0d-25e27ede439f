using CakeBistro.Core.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace CakeBistro.Repositories;

public class InventoryRepository : BaseRepository<CakeBistro.Core.Models.RawMaterial>, IInventoryRepository
{
    private readonly IMemoryCache _cache;
    private readonly ILogger<InventoryRepository> _logger;

    public InventoryRepository(
        CakeBistroContext context, 
        IMemoryCache cache, 
        ILogger<InventoryRepository> logger) : base(context)
    {
        _cache = cache;
        _logger = logger;
    }
    
    public async Task<CakeBistro.Core.Models.RawMaterial> GetByIdAsync(int id)
    {
        try
        {
            var cacheKey = $"rawmaterial_{id}";
            
            if (!_cache.TryGetValue(cacheKey, out CakeBistro.Core.Models.RawMaterial material))
            {
                material = await _context.Set<CakeBistro.Core.Models.RawMaterial>()
                    .Include(r => r.StockMovements)
                    .Include(r => r.InventoryBatches)
                    .Include(r => r.ExpiringBatchAlerts)
                    .FirstOrDefaultAsync(r => r.Id == id);
                
                if (material != null)
                {
                    // Cache for 5 minutes
                    var cacheEntryOptions = new MemoryCacheEntryOptions()
                        .SetSlidingExpiration(TimeSpan.FromMinutes(5));
                    
                    _cache.Set(cacheKey, material, cacheEntryOptions);
                }
            }
            
            return material;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting raw material by ID from repository: {Message}", ex.Message);
            throw;
        }
    }
    
    public async Task<List<CakeBistro.Core.Models.RawMaterial>> GetLowStockProductsAsync(int threshold = 10)
    {
        try
        {
            var cacheKey = $"low_stock_threshold_{threshold}";
            
            if (!_cache.TryGetValue(cacheKey, out List<CakeBistro.Core.Models.RawMaterial> lowStockMaterials))
            {
                // Calculate threshold value
                decimal thresholdPercentage = 1 - (threshold / 100.0m);
                
                lowStockMaterials = await _context.Set<CakeBistro.Core.Models.RawMaterial>()
                    .Where(r => r.CurrentStock < r.MinimumStock * thresholdPercentage)
                    .OrderBy(r => r.Name)
                    .ToListAsync();
                
                // Cache for 5 minutes
                var cacheEntryOptions = new MemoryCacheEntryOptions()
                    .SetSlidingExpiration(TimeSpan.FromMinutes(5));
                
                _cache.Set(cacheKey, lowStockMaterials, cacheEntryOptions);
            }
            
            return lowStockMaterials;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting low stock products from repository: {Message}", ex.Message);
            throw;
        }
    }
    
    public async Task<IEnumerable<InventoryBatch>> GetBatchesByMaterialIdAsync(int materialId)
    {
        return await _context.Set<InventoryBatch>()
            .Where(b => b.RawMaterialId == materialId)
            .OrderBy(b => b.ExpiryDate)
            .ToListAsync();
    }
    
    public async Task<IEnumerable<ExpiringBatchAlert>> GetActiveExpiringBatchAlertsAsync()
    {
        // Get alerts that are pending or acknowledged but not yet resolved
        return await _context.Set<ExpiringBatchAlert>()
            .Where(eba => eba.Status == AlertStatus.Pending || eba.Status == AlertStatus.Acknowledged)
            .Include(eba => eba.InventoryBatch)
            .Include(eba => eba.CakeBistro.Core.Models.RawMaterial)
            .OrderBy(eba => eba.ExpiryDate)
            .ToListAsync();
    }
    
    public async Task<IEnumerable<ExpiringBatchDetail>> GetBatchesExpiringWithinPeriodAsync(int warningPeriod = 30)
    {
        try
        {
            var cacheKey = $"expiring_batches_{warningPeriod}_{DateTime.Today:yyyyMMdd}";
            
            if (!_cache.TryGetValue(cacheKey, out List<ExpiringBatchDetail> expiringBatches))
            {
                var today = DateTime.Today;
                var cutoffDate = today.AddDays(warningPeriod);
                
                expiringBatches = await _context.Set<InventoryBatch>()
                    .Include(b => b.CakeBistro.Core.Models.RawMaterial)
                    .Where(b => b.Quantity > 0 && 
                              (b.ExpiryDate >= today && b.ExpiryDate <= cutoffDate))
                    .Select(b => new ExpiringBatchDetail
                    {
                        RawMaterialId = b.RawMaterialId,
                        RawMaterialName = b.CakeBistro.Core.Models.RawMaterial.Name,
                        BatchNumber = b.BatchNumber,
                        Quantity = b.Quantity,
                        ExpiryDate = b.ExpiryDate,
                        StorageLocation = b.StorageLocation
                    })
                    .ToListAsync();
                
                // Cache for 1 hour
                var cacheEntryOptions = new MemoryCacheEntryOptions()
                    .SetSlidingExpiration(TimeSpan.FromHours(1));
                
                _cache.Set(cacheKey, expiringBatches, cacheEntryOptions);
            }
            
            return expiringBatches;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting expiring batches from repository: {Message}", ex.Message);
            throw;
        }
    }
    
    public async Task<ExpiringBatchAlert> GetExpiringBatchAlertByIdAsync(int id)
    {
        return await _context.Set<ExpiringBatchAlert>().FindAsync(id);
    }
    
    public async Task<ExpiringBatchAlert> CreateExpiringBatchAlertAsync(InventoryBatch batch)
    {
        if (batch == null)
            throw new ArgumentNullException(nameof(batch), "Batch cannot be null");
        
        // Check if there's already an active alert for this batch
        var existingAlert = await _context.Set<ExpiringBatchAlert>()
            .FirstOrDefaultAsync(eba => eba.InventoryBatchId == batch.Id && 
                                      (eba.Status == AlertStatus.Pending || 
                                       eba.Status == AlertStatus.Acknowledged));
        
        if (existingAlert != null)
            return existingAlert; // Alert already exists
        
        // Calculate days until expiry
        var daysUntilExpiry = (batch.ExpiryDate - DateTime.Today).Days;
        
        // Create new alert
        var alert = new ExpiringBatchAlert
        {
            InventoryBatchId = batch.Id,
            RawMaterialId = batch.RawMaterialId,
            RawMaterialName = batch.CakeBistro.Core.Models.RawMaterial?.Name ?? "Unknown",
            ExpiryDate = batch.ExpiryDate,
            Quantity = batch.Quantity,
            StorageLocation = batch.StorageLocation,
            Status = AlertStatus.Pending,
            CreatedDate = DateTime.Now
        };
        
        await _context.Set<ExpiringBatchAlert>().AddAsync(alert);
        await _context.SaveChangesAsync();
        
        return alert;
    }
    
    public async Task<bool> ResolveExpiringBatchAlertAsync(int alertId, string resolutionNotes = null)
    {
        var alert = await _context.Set<ExpiringBatchAlert>().FindAsync(alertId);
        
        if (alert == null)
            return false;
        
        // Update alert status and resolution date
        alert.Status = AlertStatus.Resolved;
        alert.ResolvedDate = DateTime.Now;
        
        await _context.SaveChangesAsync();
        return true;
    }
    
    public async Task CheckForExpiringBatchesAsync(int warningPeriod = 30)
    {
        // Get all batches that will expire within the warning period
        var expiringBatches = await _context.Set<InventoryBatch>()
            .Where(b => b.ExpiryDate >= DateTime.Today && 
                      b.ExpiryDate <= DateTime.Today.AddDays(warningPeriod) && 
                      b.Quantity > 0)
            .Include(b => b.CakeBistro.Core.Models.RawMaterial)
            .ToListAsync();
        
        // For each expiring batch, create an alert if one doesn't exist
        foreach (var batch in expiringBatches)
        {
            // Check if we have an existing alert for this batch
            var existingAlert = await _context.Set<ExpiringBatchAlert>()
                .FirstOrDefaultAsync(eba => eba.InventoryBatchId == batch.Id && 
                                          (eba.Status == AlertStatus.Pending || 
                                           eba.Status == AlertStatus.Acknowledged));
            
            if (existingAlert == null)
            {
                // No existing alert, create a new one
                await CreateExpiringBatchAlertAsync(batch);
            }
        }
        
        // Also check for expired batches (past expiry date)
        var expiredBatches = await _context.Set<InventoryBatch>()
            .Where(b => b.ExpiryDate < DateTime.Today && b.Quantity > 0)
            .Include(b => b.CakeBistro.Core.Models.RawMaterial)
            .ToListAsync();
        
        foreach (var batch in expiredBatches)
        {
            // Check if we have an existing alert for this batch
            var existingAlert = await _context.Set<ExpiringBatchAlert>()
                .FirstOrDefaultAsync(eba => eba.InventoryBatchId == batch.Id && 
                                          (eba.Status == AlertStatus.Pending || 
                                           eba.Status == AlertStatus.Acknowledged));
            
            if (existingAlert == null)
            {
                // No existing alert, create a new one
                await CreateExpiringBatchAlertAsync(batch);
            }
        }
    }
    
    public async Task<InventoryBatch> AddBatchAsync(InventoryBatch batch)
    {
        await _context.Set<InventoryBatch>().AddAsync(batch);
        await _context.SaveChangesAsync();
        
        // After adding a new batch, check if it needs an alert
        var daysUntilExpiry = (batch.ExpiryDate - DateTime.Today).Days;
        
        // If the batch expires within our default warning period (30 days), create an alert
        if (daysUntilExpiry <= 30)
        {
            await CreateExpiringBatchAlertAsync(batch);
        }
        
        return batch;
    }
    
    public async Task<InventoryBatch> GetBatchByIdAsync(int id)
    {
        return await _context.Set<InventoryBatch>().FindAsync(id);
    }
    
    public async Task<IEnumerable<InventoryBatch>> GetBatchesByMaterialIdAsync(int materialId)
    {
        return await _context.Set<InventoryBatch>()
            .Where(b => b.RawMaterialId == materialId)
            .OrderBy(b => b.ExpiryDate)
            .ToListAsync();
    }
    
    public async Task<bool> DeleteBatchAsync(int id)
    {
        var batch = await _context.Set<InventoryBatch>().FindAsync(id);
        if (batch == null)
            return false;
        
        _context.Set<InventoryBatch>().Remove(batch);
        await _context.SaveChangesAsync();
        return true;
    }
    
    public async Task<(bool Success, string Message)> RecordStockMovementAsync(StockMovement movement)
    {
        // Validate input
        if (movement == null)
            throw new ArgumentNullException(nameof(movement), "Movement cannot be null");

        if (movement.ProductId <= 0)
            throw new ArgumentException("Invalid product ID", nameof(movement.ProductId));

        if (movement.Quantity <= 0)
            throw new ArgumentException("Quantity must be greater than zero", nameof(movement.Quantity));

        if (movement.MovementType != MovementType.Incoming && movement.MovementType != MovementType.Outgoing)
            throw new ArgumentException("Movement type must be Incoming or Outgoing", nameof(movement.MovementType));

        try
        {
            // Get the product
            var material = await _context.Set<CakeBistro.Core.Models.RawMaterial>()
                .Include(m => m.InventoryBatches)
                .FirstOrDefaultAsync(m => m.Id == movement.ProductId);
            
            if (material == null)
                throw new ArgumentException("Product not found", nameof(movement.ProductId));

            // Set default values if not provided
            movement.MovementDate ??= DateTime.UtcNow;
            movement.Status ??= StockMovementStatus.Completed;
            
            // Handle incoming movement
            if (movement.MovementType == MovementType.Incoming)
            {
                material.CurrentStock += movement.Quantity;
                
                // If this is a batch with expiry date, create inventory batch
                if (movement.ExpiryDate.HasValue)
                {
                    var batch = new InventoryBatch
                    {
                        RawMaterialId = material.Id,
                        BatchNumber = $"BATCH-{DateTime.Now:yyyyMMdd}-{movement.ReferenceNumber}",
                        ProductionDate = movement.MovementDate.Value,
                        ExpiryDate = movement.ExpiryDate.Value,
                        Quantity = movement.Quantity,
                        Cost = movement.Quantity * material.PricePerUnit,
                        StorageLocation = "Warehouse A"
                    };
                    
                    await _context.Set<InventoryBatch>().AddAsync(batch);
                    
                    // After adding the batch, check if it needs an alert
                    var daysUntilExpiry = (batch.ExpiryDate - DateTime.Today).Days;
                    
                    // If the batch expires within our default warning period (30 days), create an alert
                    if (daysUntilExpiry <= 30)
                    {
                        await CreateExpiringBatchAlertAsync(batch);
                    }
                }
            }
            // Handle outgoing movement
            else if (movement.MovementType == MovementType.Outgoing)
            {
                // Check if we have enough stock
                if (material.CurrentStock < movement.Quantity)
                {
                    throw new InvalidOperationException(
                        $"Insufficient stock for {material.Name}. Available: {material.CurrentStock}, Requested: {movement.Quantity}");
                }
                
                material.CurrentStock -= movement.Quantity;
                
                // Deduct from oldest batches first
                await DeductFromInventoryBatchesAsync(movement);
            }
            
            // Save the movement
            await _context.Set<StockMovement>().AddAsync(movement);
            
            // Update product stock
            await _context.SaveChangesAsync();
            
            return (true, $"Stock movement recorded successfully. New stock level: {material.CurrentStock}");
        }
        catch (Exception ex)
        {
            // Log the error (in a real application)
            // For now, just return the error message
            return (false, $"Error recording stock movement: {ex.Message}");
        }
    }
    
    private async Task DeductFromInventoryBatchesAsync(StockMovement movement)
    {
        if (movement == null || movement.ProductId <= 0)
            return;
        
        // Get the material with its batches
        var material = await _context.Set<CakeBistro.Core.Models.RawMaterial>()
            .Include(m => m.InventoryBatches)
            .ThenInclude(m => m.CakeBistro.Core.Models.RawMaterial)
            .FirstOrDefaultAsync(m => m.Id == movement.ProductId);
        
        if (material == null || !material.InventoryBatches.Any())
            return;
        
        int quantityToDeduct = movement.Quantity;
        
        // Sort batches by expiry date (oldest first)
        var sortedBatches = material.InventoryBatches
            .OrderBy(b => b.ExpiryDate)
            .ToList();
        
        foreach (var batch in sortedBatches)
        {
            if (quantityToDeduct <= 0)
                break;
            
            if (batch.Quantity > quantityToDeduct)
            {
                // Deduct from this batch and exit
                batch.Quantity -= quantityToDeduct;
                quantityToDeduct = 0;
            }
            else if (batch.Quantity > 0)
            {
                // Deduct all from this batch
                quantityToDeduct -= batch.Quantity;
                batch.Quantity = 0;
            }
            
            // Remove empty batches
            if (batch.Quantity <= 0)
            {
                _context.Set<InventoryBatch>().Remove(batch);
                
                // Find any active alert for this batch and resolve it
                var activeAlert = await _context.Set<ExpiringBatchAlert>()
                    .FirstOrDefaultAsync(eba => eba.InventoryBatchId == batch.Id && 
                                              (eba.Status == AlertStatus.Pending || 
                                               eba.Status == AlertStatus.Acknowledged));
                
                if (activeAlert != null)
                {
                    activeAlert.Status = AlertStatus.Resolved;
                    activeAlert.ResolvedDate = DateTime.Now;
                }
            }
        }
        
        // Save changes
        await _context.SaveChangesAsync();
    }
}
