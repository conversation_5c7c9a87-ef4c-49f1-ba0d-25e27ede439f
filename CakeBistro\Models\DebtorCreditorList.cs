using System.Collections.Generic;

namespace CakeBistro.Models
{
    public class DebtorList
    {
        public List<Debtor> Debtors { get; set; } = new();
    }

    public class Debtor
    {
        public int CustomerId { get; set; }
        public string? CustomerName { get; set; }
        public decimal OutstandingAmount { get; set; }
    }

    public class CreditorList
    {
        public List<Creditor> Creditors { get; set; } = new();
    }

    public class Creditor
    {
        public int SupplierId { get; set; }
        public string? SupplierName { get; set; }
        public decimal OutstandingAmount { get; set; }
    }
}
