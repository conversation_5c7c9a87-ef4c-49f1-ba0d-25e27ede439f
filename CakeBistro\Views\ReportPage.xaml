<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="MCakeBistro.Views.ReportPage"
             Title="Reports">
    <VerticalStackLayout>
        <!-- Date range selection -->
        <Grid Padding="10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            
            <VerticalStackLayout>
                <Label Text="Start Date" FontAttributes="Bold"/>
                <DatePicker Date="{Binding StartDate}"
                            Format="D"/>
            </VerticalStackLayout>
            
            <VerticalStackLayout Grid.Column="1">
                <Label Text="End Date" FontAttributes="Bold"/>
                <DatePicker Date="{Binding EndDate}"
                            Format="D"/>
            </VerticalStackLayout>
        </Grid>
        
        <!-- Report generation buttons -->
        <HorizontalStackLayout Spacing="10" Padding="10">
            <Button Text="Sales Report"
                    Command="{Binding GenerateSalesReportCommand}"
                    HorizontalOptions="FillAndExpand"/>
            <Button Text="Inventory Report"
                    Command="{Binding GenerateInventoryReportCommand}"
                    HorizontalOptions="FillAndExpand"/>
            <Button Text="Purchase Report"
                    Command="{Binding GeneratePurchaseReportCommand}"
                    HorizontalOptions="FillAndExpand"/>
        </HorizontalStackLayout>
        
        <!-- Historical reports -->
        <Label Text="Historical Reports" FontSize="Large" FontAttributes="Bold" Padding="10"/>
        
        <CollectionView ItemsSource="{Binding HistoricalReports}"
                        SelectionMode="Single"
                        SelectedItem="{Binding SelectedReport}">
            <CollectionView.ItemTemplate>
                <DataTemplate>
                    <Grid Padding="10">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        
                        <VerticalStackLayout>
                            <Label Text="{Binding ReportName}"
                                   FontAttributes="Bold"/>
                            <Label Text="{Binding GeneratedDate, StringFormat='{0:MMM d, yyyy}'}"
                                   TextColor="Gray"/>
                        </VerticalStackLayout>
                        
                        <Label Grid.Column="1"
                               Text="{Binding ReportType}"
                               HorizontalOptions="End"
                               VerticalOptions="Center"
                               FontAttributes="Bold"/>
                    </Grid>
                </DataTemplate>
            </CollectionView.ItemTemplate>
        </CollectionView>
        
        <!-- Action buttons -->
        <HorizontalStackLayout Spacing="10" Padding="10">
            <Button Text="View Details"
                    Command="{Binding ViewHistoricalReportsCommand}"
                    HorizontalOptions="FillAndExpand"/>
            <Button Text="Refresh"
                    Command="{Binding RefreshCommand}"
                    HorizontalOptions="FillAndExpand"/>
        </HorizontalStackLayout>
    </VerticalStackLayout>
</ContentPage>