using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using CakeBistro.Core.Models;
using CakeBistro.Repositories;
using CakeBistro.Core.Interfaces;

namespace CakeBistro.Repositories
{
    // Implementation of inventory batch repository with inventory-specific data operations
    public class InventoryBatchRepository : IInventoryBatchRepository
    {
        private readonly InventoryContext _context;
        
        public InventoryBatchRepository(InventoryContext context)
        {
            _context = context;
        }
        
        public async Task<IEnumerable<InventoryBatch>> GetAllAsync()
        {
            return await _context.InventoryBatches.ToListAsync();
        }
        
        public async Task<InventoryBatch> GetByIdAsync(Guid id)
        {
            return await _context.InventoryBatches.FindAsync(id);
        }
        
        public async Task AddAsync(InventoryBatch batch)
        {
            await _context.InventoryBatches.AddAsync(batch);
            await _context.SaveChangesAsync();
        }
        
        public async Task UpdateAsync(InventoryBatch batch)
        {
            _context.InventoryBatches.Update(batch);
            await _context.SaveChangesAsync();
        }
        
        public async Task DeleteAsync(Guid id)
        {
            var batch = await _context.InventoryBatches.FindAsync(id);
            if (batch != null)
            {
                _context.InventoryBatches.Remove(batch);
                await _context.SaveChangesAsync();
            }
        }
        
        public async Task<IEnumerable<InventoryBatch>> GetBatchesByMaterialAsync(Guid materialId)
        {
            return await _context.InventoryBatches
                .Where(b => b.RawMaterialId == materialId)
                .ToListAsync();
        }
        
        public async Task<IEnumerable<InventoryBatch>> GetActiveBatchesAsync()
        {
            return await _context.InventoryBatches
                .Where(b => b.Status == BatchStatus.Active)
                .ToListAsync();
        }
        
        public async Task<IEnumerable<InventoryBatch>> GetExpiringBatchesAsync(DateTime thresholdDate)
        {
            return await _context.InventoryBatches
                .Where(b => b.ExpiryDate <= thresholdDate && b.Status == BatchStatus.Active)
                .ToListAsync();
        }
        
        public async Task<decimal> GetTotalStockQuantityAsync(Guid materialId)
        {
            var batches = await _context.InventoryBatches
                .Where(b => b.RawMaterialId == materialId && b.Status == BatchStatus.Active)
                .ToListAsync();
            
            return batches.Sum(b => b.Quantity);
        }
    }
}
