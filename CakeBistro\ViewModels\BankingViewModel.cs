using System;
using System.Collections.ObjectModel;
using System.Threading.Tasks;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using CakeBistro.Services;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace CakeBistro.ViewModels
{
    public partial class BankingViewModel : ObservableObject
    {
        private readonly FinanceService _financeService;
        private readonly ILogger<BankingViewModel> _logger;
        
        [ObservableProperty]
        private ObservableCollection<BankAccount> accounts = new();

        [ObservableProperty]
        private BankAccount selectedAccount;

        [ObservableProperty]
        private string statusMessage;

        private Color _statusColor = Colors.Transparent;
        public Color StatusColor
        {
            get => _statusColor;
            set => SetProperty(ref _statusColor, value);
        }

        public BankingViewModel(FinanceService financeService, ILogger<BankingViewModel> logger)
        {
            _financeService = financeService;
            _logger = logger;
            LoadAccountsCommand = new AsyncRelayCommand(LoadAccountsAsync);
            AddAccountCommand = new AsyncRelayCommand(AddAccountAsync);
            UpdateAccountCommand = new AsyncRelayCommand(UpdateAccountAsync);
        }

        private async Task LoadAccountsAsync()
        {
            StatusMessage = string.Empty;
            try
            {
                var result = await _financeService.GetAllAccountsAsync();
                Accounts = new ObservableCollection<BankAccount>(result);
                StatusMessage = "Accounts loaded.";
                StatusColor = Color.FromArgb("#1976D2"); // Info color
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error: {ex.Message}";
                StatusColor = Color.FromArgb("#D32F2F"); // Error color
            }
        }

        private async Task AddAccountAsync()
        {
            try
            {
                await _financeService.AddBankAccountAsync(SelectedAccount);
                StatusMessage = "Account added successfully.";
                StatusColor = Color.FromArgb("#388E3C");
                await LoadAccountsAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding account: {Message}", ex.Message);
                StatusMessage = $"Error: {ex.Message}";
                StatusColor = Color.FromArgb("#D32F2F");
            }
        }

        private async Task UpdateAccountAsync()
        {
            if (SelectedAccount == null) return;
            StatusMessage = string.Empty;
            try
            {
                await _financeService.UpdateAccountAsync(SelectedAccount);
                await LoadAccountsAsync();
                StatusMessage = "Account updated.";
                StatusColor = Color.FromArgb("#388E3C"); // Success color
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error: {ex.Message}";
                StatusColor = Color.FromArgb("#D32F2F"); // Error color
            }
        }
    }
}
