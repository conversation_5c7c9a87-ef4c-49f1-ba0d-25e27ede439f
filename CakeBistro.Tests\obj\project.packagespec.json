﻿"restore":{"projectUniqueName":"C:\\Users\\<USER>\\Desktop\\CakeBistro\\CakeBistro.Tests\\CakeBistro.Tests.csproj","projectName":"CakeBistro.Tests","projectPath":"C:\\Users\\<USER>\\Desktop\\CakeBistro\\CakeBistro.Tests\\CakeBistro.Tests.csproj","outputPath":"C:\\Users\\<USER>\\Desktop\\CakeBistro\\CakeBistro.Tests\\obj\\","projectStyle":"PackageReference","UsingMicrosoftNETSdk":false,"fallbackFolders":["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"],"originalTargetFrameworks":["net8.0"],"sources":{"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\":{},"C:\\Program Files\\dotnet\\library-packs":{},"https://api.nuget.org/v3/index.json":{}},"frameworks":{"net8.0":{"targetAlias":"net8.0","projectReferences":{"C:\\Users\\<USER>\\Desktop\\CakeBistro\\CakeBistro\\CakeBistro.csproj":{"projectPath":"C:\\Users\\<USER>\\Desktop\\CakeBistro\\CakeBistro\\CakeBistro.csproj"}}}},"warningProperties":{"warnAsError":["NU1605"]},"restoreAuditProperties":{"enableAudit":"true","auditLevel":"low","auditMode":"direct"}}"frameworks":{"net8.0":{"targetAlias":"net8.0","dependencies":{"Microsoft.EntityFrameworkCore":{"target":"Package","version":"[8.0.3, )"},"Microsoft.EntityFrameworkCore.InMemory":{"target":"Package","version":"[8.0.3, )"},"Microsoft.Extensions.Configuration.Json":{"target":"Package","version":"[8.0.1, )"},"Microsoft.NET.Test.Sdk":{"target":"Package","version":"[17.12.0, )"},"coverlet.collector":{"target":"Package","version":"[6.0.2, )"},"xunit":{"target":"Package","version":"[2.9.2, )"},"xunit.runner.visualstudio":{"target":"Package","version":"[2.8.2, )"}},"imports":["net461","net462","net47","net471","net472","net48","net481"],"assetTargetFallback":true,"warn":true,"frameworkReferences":{"Microsoft.NETCore.App":{"privateAssets":"all"}},"runtimeIdentifierGraphPath":"C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}