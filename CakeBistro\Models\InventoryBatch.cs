
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace CakeBistro.Models
{
    /// <summary>
    /// Represents a batch of finished products in inventory
    /// Implements PRD 4.2 requirement for batch production tracking with full traceability
    /// </summary>
    [Table("InventoryBatches")]
    public class InventoryBatch
    {
        /// <summary>
        /// Gets or sets the unique identifier of the batch
        /// </summary>
        [Key]
        public int Id { get; set; }

        /// <summary>
        /// Gets or sets the ID of the product this batch belongs to
        /// </summary>
        public int ProductId { get; set; }

        /// <summary>
        /// Gets or sets the quantity in this batch
        /// </summary>
        public int Quantity { get; set; }

        /// <summary>
        /// Gets or sets the batch number for identification
        /// </summary>
        public string BatchNumber { get; set; }

        /// <summary>
        /// Gets or sets the expiry date of this batch
        /// </summary>
        public DateTime ExpiryDate { get; set; }

        /// <summary>
        /// Gets or sets the status of this batch
        /// </summary>
        public BatchStatus Status { get; set; }

        /// <summary>
        /// Gets or sets the date when the batch was created
        /// </summary>
        public DateTime CreatedDate { get; set; }

        /// <summary>
        /// Gets or sets the date when the batch was last updated
        /// </summary>
        public DateTime UpdatedDate { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether this batch has been processed
        /// </summary>
        public bool IsProcessed { get; set; }

        /// <summary>
        /// Gets or sets the date when this batch was processed
        /// </summary>
        public DateTime? ProcessedDate { get; set; }

        // Navigation properties
        /// <summary>
        /// Gets or sets the product associated with this batch
        /// </summary>
        [ForeignKey("ProductId")]
        public virtual FinishedProduct Product { get; set; }
    }

    /// <summary>
    /// Represents the status of an inventory batch
    /// </summary>
    public enum BatchStatus
    {
        /// <summary>
        /// The batch is active and available for use
        /// </summary>
        Active,

        /// <summary>
        /// The batch is pending transfer to another location
        /// </summary>
        PendingTransfer,

        /// <summary>
        /// The batch has expired and should not be used
        /// </summary>
        Expired,

        /// <summary>
        /// The batch has been quarantined for quality issues
        /// </summary>
        Quarantined
    }
}