
using System;
using System.IO;
using System.Threading.Tasks;
using CakeBistro.Models;
using CakeBistro.Services;
using Microsoft.EntityFrameworkCore;
using Xunit;

namespace CakeBistro.Tests
{
    public class DocumentServiceTests
    {
        private readonly CakeBistroContext _context;
        private readonly IDocumentService _documentService;

        public DocumentServiceTests()
        {
            // Set up in-memory database for testing
            var options = new DbContextOptionsBuilder<CakeBistroContext>()
                .UseInMemoryDatabase(databaseName: $"DocumentTestDb_{Guid.NewGuid()}")
                .Options;

            _context = new CakeBistroContext(options);
            _documentService = new DocumentService(_context);
        }

        [Fact]
        public async Task UploadDocumentAsync_ValidDocument_CreatesDocumentRecord()
        {
            // Arrange
            var product = new FinishedProduct
            {
                Name = "Chocolate Cake",
                SalePrice = 25.0m
            };
            
            await _context.FinishedProducts.AddAsync(product);
            await _context.SaveChangesAsync();

            // Create a mock file stream
            var content = "This is a test document";
            var bytes = System.Text.Encoding.UTF8.GetBytes(content);
            using (var stream = new MemoryStream(bytes))
            {
                var documentUpload = new DocumentUpload
                {
                    ProductId = product.Id,
                    FileStream = stream,
                    Name = "TestDocument.txt",
                    Description = "Test document description",
                    DocumentType = "ProductSpecification",
                    ContentType = "text/plain"
                };

                // Act
                var result = await _documentService.UploadDocumentAsync(documentUpload);

                // Assert
                Assert.NotNull(result);
                Assert.Equal(documentUpload.ProductId, result.ProductId);
                Assert.Equal(documentUpload.Name, result.Name);
                Assert.Equal(documentUpload.Description, result.Description);
                Assert.Equal(documentUpload.DocumentType, result.DocumentType);
                Assert.Equal(documentUpload.ContentType, result.ContentType);
                Assert.NotEmpty(result.FileName);
                Assert.Equal(bytes.Length, result.Size);
                Assert.False(result.IsDeleted);
                Assert.NotNull(result.CreatedDate);
            }
        }

        [Fact]
        public async Task GetDocumentsByProductAsync_ValidProduct_ReturnsDocuments()
        {
            // Arrange
            var product = new FinishedProduct
            {
                Name = "Vanilla Cake",
                SalePrice = 20.0m
            };
            
            await _context.FinishedProducts.AddAsync(product);
            
            var document1 = new Document
            {
                ProductId = product.Id,
                Name = "Spec1.pdf",
                DocumentType = "ProductSpecification",
                FileName = "doc1.txt",
                Size = 100,
                CreatedDate = DateTime.UtcNow
            };
            
            var document2 = new Document
            {
                ProductId = product.Id,
                Name = "Cert1.pdf",
                DocumentType = "QualityCertificate",
                FileName = "doc2.txt",
                Size = 200,
                CreatedDate = DateTime.UtcNow
            };
            
            await _context.Documents.AddRangeAsync(document1, document2);
            await _context.SaveChangesAsync();

            // Act
            var result = await _documentService.GetDocumentsByProductAsync(product.Id);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(2, result.Count);
            Assert.Contains(result, d => d.Id == document1.Id);
            Assert.Contains(result, d => d.Id == document2.Id);
        }

        [Fact]
        public async Task DeleteDocumentAsync_ValidDocument_SoftDeletesDocument()
        {
            // Arrange
            var product = new FinishedProduct
            {
                Name = "Strawberry Cake",
                SalePrice = 22.0m
            };
            
            await _context.FinishedProducts.AddAsync(product);
            
            var document = new Document
            {
                ProductId = product.Id,
                Name = "TestDoc.pdf",
                DocumentType = "ProductSpecification",
                FileName = "doc3.txt",
                Size = 150,
                CreatedDate = DateTime.UtcNow
            };
            
            await _context.Documents.AddAsync(document);
            await _context.SaveChangesAsync();

            // Act
            var result = await _documentService.DeleteDocumentAsync(document.Id);

            // Assert
            Assert.True(result);
            
            // Verify document was soft deleted
            var updatedDocument = await _context.Documents.FindAsync(document.Id);
            Assert.NotNull(updatedDocument);
            Assert.True(updatedDocument.IsDeleted);
            Assert.NotNull(updatedDocument.DeletedDate);
        }

        [Fact]
        public async Task RestoreDocumentAsync_ValidDocument_RestoresDocument()
        {
            // Arrange
            var product = new FinishedProduct
            {
                Name = "Cheesecake",
                SalePrice = 28.0m
            };
            
            await _context.FinishedProducts.AddAsync(product);
            
            var document = new Document
            {
                ProductId = product.Id,
                Name = "TestDoc.pdf",
                DocumentType = "ProductSpecification",
                FileName = "doc4.txt",
                Size = 150,
                CreatedDate = DateTime.UtcNow,
                IsDeleted = true,
                DeletedDate = DateTime.UtcNow
            };
            
            await _context.Documents.AddAsync(document);
            await _context.SaveChangesAsync();

            // Act
            var result = await _documentService.RestoreDocumentAsync(document.Id);

            // Assert
            Assert.True(result);
            
            // Verify document was restored
            var updatedDocument = await _context.Documents.FindAsync(document.Id);
            Assert.NotNull(updatedDocument);
            Assert.False(updatedDocument.IsDeleted);
            Assert.NotNull(updatedDocument.RestoredDate);
        }

        [Fact]
        public async Task DownloadDocumentAsync_ValidDocument_ReturnsFileStream()
        {
            // Arrange
            var product = new FinishedProduct
            {
                Name = "Red Velvet Cake",
                SalePrice = 24.0m
            };
            
            await _context.FinishedProducts.AddAsync(product);
            
            var content = "This is the content of the test document.";
            var bytes = System.Text.Encoding.UTF8.GetBytes(content);
            
            var document = new Document
            {
                ProductId = product.Id,
                Name = "TestDownload.txt",
                DocumentType = "ProductSpecification",
                FileName = "test_download.txt",
                ContentType = "text/plain",
                Size = bytes.Length,
                CreatedDate = DateTime.UtcNow
            };
            
            await _context.Documents.AddAsync(document);
            await _context.SaveChangesAsync();

            // Save the file to the storage location
            var filePath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "documents", document.FileName);
            var directory = Path.GetDirectoryName(filePath);
            
            if (!Directory.Exists(directory))
                Directory.CreateDirectory(directory);

            File.WriteAllBytes(filePath, bytes);

            // Act
            using (var resultStream = await _documentService.DownloadDocumentAsync(document.Id))
            using (var reader = new StreamReader(resultStream))
            {
                var resultContent = await reader.ReadToEndAsync();

                // Assert
                Assert.Equal(content, resultContent);
            }
        }
    }
}