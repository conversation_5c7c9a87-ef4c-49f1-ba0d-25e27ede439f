using System;
using System.Collections.Generic;

namespace CakeBistro.Repositories;

public interface IReadRepository<T> where T : class
{
    Task<IEnumerable<T>> GetAllAsync();
    Task<T?> GetByIdAsync(Guid id);
    Task<T?> GetByIdAsync(int id);
}

public interface IWriteRepository<T> where T : class
{
    Task<T> AddAsync(T entity);
    Task UpdateAsync(T entity);
    Task DeleteAsync(Guid id);
    Task DeleteAsync(int id);
}

// Combined interface for when both read and write are needed
public interface IRepository<T> : IReadRepository<T>, IWriteRepository<T> where T : class
{
}
