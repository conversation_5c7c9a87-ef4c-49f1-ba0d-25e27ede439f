
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace CakeBistro.Models
{
    /// <summary>
    /// Represents a document associated with a finished product
    /// Implements PRD requirement for document management and organization
    /// </summary>
    [Table("Documents")]
    public class Document
    {
        /// <summary>
        /// Gets or sets the unique identifier of the document
        /// </summary>
        [Key]
        public int Id { get; set; }

        /// <summary>
        /// Gets or sets the ID of the product this document belongs to
        /// </summary>
        public int ProductId { get; set; }

        /// <summary>
        /// Gets or sets the name of the document
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// Gets or sets the description of the document
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// Gets or sets the type/category of the document
        /// </summary>
        public string DocumentType { get; set; }

        /// <summary>
        /// Gets or sets the content type of the document (e.g., application/pdf)
        /// </summary>
        public string ContentType { get; set; }

        /// <summary>
        /// Gets or sets the file name used for storing the document
        /// </summary>
        public string FileName { get; set; }

        /// <summary>
        /// Gets or sets the size of the document in bytes
        /// </summary>
        public long Size { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether this document has been deleted (soft delete)
        /// </summary>
        public bool IsDeleted { get; set; }

        /// <summary>
        /// Gets or sets the date when the document was created
        /// </summary>
        public DateTime CreatedDate { get; set; }

        /// <summary>
        /// Gets or sets the date when the document was deleted
        /// </summary>
        public DateTime? DeletedDate { get; set; }

        /// <summary>
        /// Gets or sets the date when the document was restored
        /// </summary>
        public DateTime? RestoredDate { get; set; }

        // Navigation properties
        /// <summary>
        /// Gets or sets the product associated with this document
        /// </summary>
        [ForeignKey("ProductId")]
        public virtual FinishedProduct Product { get; set; }
    }
}