is_global = true
build_property.TargetFramework = net8.0-windows10.0.19041.0
build_property.TargetPlatformMinVersion = 10.0.17763.0
build_property.UsingMicrosoftNETSdkWeb = 
build_property.ProjectTypeGuids = 9A19103F-16F7-4668-BE54-9A1E7A4F7556
build_property.InvariantGlobalization = 
build_property.PlatformNeutralAssembly = 
build_property.EnforceExtendedAnalyzerRules = 
build_property._SupportedPlatformList = Linux,macOS,Windows
build_property.RootNamespace = CakeBistro
build_property.ProjectDir = C:\Users\<USER>\Desktop\CakeBistro\CakeBistro\
build_property.EnableComHosting = 
build_property.EnableGeneratedComInterfaceComImportInterop = 
build_property.CsWinRTAotOptimizerEnabled = true
build_property.CsWinRTAotExportsEnabled = 
build_property.CsWinRTRcwFactoryFallbackGeneratorForceOptIn = 
build_property.CsWinRTRcwFactoryFallbackGeneratorForceOptOut = 
build_property.CsWinRTCcwLookupTableGeneratorEnabled = true
build_property.CsWinRTMergeReferencedActivationFactories = 
build_property.CsWinRTAotWarningLevel = 
build_property.CsWinRTUseWindowsUIXamlProjections = false
build_property.EffectiveAnalysisLevelStyle = 8.0
build_property.EnableCodeStyleSeverity = 

[C:/Users/<USER>/Desktop/CakeBistro/CakeBistro/App.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.App.xaml
build_metadata.AdditionalFiles.TargetPath = App.xaml
build_metadata.AdditionalFiles.RelativePath = App.xaml

[C:/Users/<USER>/Desktop/CakeBistro/CakeBistro/AppShell.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.AppShell.xaml
build_metadata.AdditionalFiles.TargetPath = AppShell.xaml
build_metadata.AdditionalFiles.RelativePath = AppShell.xaml

[C:/Users/<USER>/Desktop/CakeBistro/CakeBistro/Controls/CardView.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Controls.CardView.xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Controls.CardView.xaml
build_metadata.AdditionalFiles.TargetPath = Controls\CardView.xaml
build_metadata.AdditionalFiles.TargetPath = Controls\CardView.xaml
build_metadata.AdditionalFiles.RelativePath = Controls\CardView.xaml
build_metadata.AdditionalFiles.RelativePath = Controls\CardView.xaml

[C:/Users/<USER>/Desktop/CakeBistro/CakeBistro/MainPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.MainPage.xaml
build_metadata.AdditionalFiles.TargetPath = MainPage.xaml
build_metadata.AdditionalFiles.RelativePath = MainPage.xaml

[C:/Users/<USER>/Desktop/CakeBistro/CakeBistro/Resources/Animations/ButtonAnimations.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Resources.Animations.ButtonAnimations.xaml
build_metadata.AdditionalFiles.TargetPath = Resources\Animations\ButtonAnimations.xaml
build_metadata.AdditionalFiles.RelativePath = Resources\Animations\ButtonAnimations.xaml

[C:/Users/<USER>/Desktop/CakeBistro/CakeBistro/Resources/Styles/Accessibility.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Resources.Styles.Accessibility.xaml
build_metadata.AdditionalFiles.TargetPath = Resources\Styles\Accessibility.xaml
build_metadata.AdditionalFiles.RelativePath = Resources\Styles\Accessibility.xaml

[C:/Users/<USER>/Desktop/CakeBistro/CakeBistro/Resources/Styles/AppTheme.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Resources.Styles.AppTheme.xaml
build_metadata.AdditionalFiles.TargetPath = Resources\Styles\AppTheme.xaml
build_metadata.AdditionalFiles.RelativePath = Resources\Styles\AppTheme.xaml

[C:/Users/<USER>/Desktop/CakeBistro/CakeBistro/Resources/Styles/DarkTheme.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Resources.Styles.DarkTheme.xaml
build_metadata.AdditionalFiles.TargetPath = Resources\Styles\DarkTheme.xaml
build_metadata.AdditionalFiles.RelativePath = Resources\Styles\DarkTheme.xaml

[C:/Users/<USER>/Desktop/CakeBistro/CakeBistro/Views/AccountStatementPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.AccountStatementPage.xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.AccountStatementPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\AccountStatementPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\AccountStatementPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\AccountStatementPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\AccountStatementPage.xaml

[C:/Users/<USER>/Desktop/CakeBistro/CakeBistro/Views/Admin/RoleManagementPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.Admin.RoleManagementPage.xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.Admin.RoleManagementPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\Admin\RoleManagementPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\Admin\RoleManagementPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\Admin\RoleManagementPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\Admin\RoleManagementPage.xaml

[C:/Users/<USER>/Desktop/CakeBistro/CakeBistro/Views/Admin/UserManagementPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.Admin.UserManagementPage.xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.Admin.UserManagementPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\Admin\UserManagementPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\Admin\UserManagementPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\Admin\UserManagementPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\Admin\UserManagementPage.xaml

[C:/Users/<USER>/Desktop/CakeBistro/CakeBistro/Views/AssetListPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.AssetListPage.xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.AssetListPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\AssetListPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\AssetListPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\AssetListPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\AssetListPage.xaml

[C:/Users/<USER>/Desktop/CakeBistro/CakeBistro/Views/AssetPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.AssetPage.xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.AssetPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\AssetPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\AssetPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\AssetPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\AssetPage.xaml

[C:/Users/<USER>/Desktop/CakeBistro/CakeBistro/Views/AuthenticationPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.AuthenticationPage.xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.AuthenticationPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\AuthenticationPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\AuthenticationPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\AuthenticationPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\AuthenticationPage.xaml

[C:/Users/<USER>/Desktop/CakeBistro/CakeBistro/Views/BankingPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.BankingPage.xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.BankingPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\BankingPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\BankingPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\BankingPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\BankingPage.xaml

[C:/Users/<USER>/Desktop/CakeBistro/CakeBistro/Views/BarcodeScannerPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.BarcodeScannerPage.xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.BarcodeScannerPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\BarcodeScannerPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\BarcodeScannerPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\BarcodeScannerPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\BarcodeScannerPage.xaml

[C:/Users/<USER>/Desktop/CakeBistro/CakeBistro/Views/BasePage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.BasePage.xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.BasePage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\BasePage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\BasePage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\BasePage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\BasePage.xaml

[C:/Users/<USER>/Desktop/CakeBistro/CakeBistro/Views/CostCalculationDashboard.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.CostCalculationDashboard.xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.CostCalculationDashboard.xaml
build_metadata.AdditionalFiles.TargetPath = Views\CostCalculationDashboard.xaml
build_metadata.AdditionalFiles.TargetPath = Views\CostCalculationDashboard.xaml
build_metadata.AdditionalFiles.RelativePath = Views\CostCalculationDashboard.xaml
build_metadata.AdditionalFiles.RelativePath = Views\CostCalculationDashboard.xaml

[C:/Users/<USER>/Desktop/CakeBistro/CakeBistro/Views/DamageTrackingPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.DamageTrackingPage.xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.DamageTrackingPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\DamageTrackingPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\DamageTrackingPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\DamageTrackingPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\DamageTrackingPage.xaml

[C:/Users/<USER>/Desktop/CakeBistro/CakeBistro/Views/DebtorCreditorReportPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.DebtorCreditorReportPage.xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.DebtorCreditorReportPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\DebtorCreditorReportPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\DebtorCreditorReportPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\DebtorCreditorReportPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\DebtorCreditorReportPage.xaml

[C:/Users/<USER>/Desktop/CakeBistro/CakeBistro/Views/DeliveryManifestDetailPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.DeliveryManifestDetailPage.xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.DeliveryManifestDetailPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\DeliveryManifestDetailPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\DeliveryManifestDetailPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\DeliveryManifestDetailPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\DeliveryManifestDetailPage.xaml

[C:/Users/<USER>/Desktop/CakeBistro/CakeBistro/Views/DeliveryManifestListPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.DeliveryManifestListPage.xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.DeliveryManifestListPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\DeliveryManifestListPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\DeliveryManifestListPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\DeliveryManifestListPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\DeliveryManifestListPage.xaml

[C:/Users/<USER>/Desktop/CakeBistro/CakeBistro/Views/DriverDetailPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.DriverDetailPage.xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.DriverDetailPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\DriverDetailPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\DriverDetailPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\DriverDetailPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\DriverDetailPage.xaml

[C:/Users/<USER>/Desktop/CakeBistro/CakeBistro/Views/DriverListPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.DriverListPage.xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.DriverListPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\DriverListPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\DriverListPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\DriverListPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\DriverListPage.xaml

[C:/Users/<USER>/Desktop/CakeBistro/CakeBistro/Views/FinancePage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.FinancePage.xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.FinancePage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\FinancePage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\FinancePage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\FinancePage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\FinancePage.xaml

[C:/Users/<USER>/Desktop/CakeBistro/CakeBistro/Views/FinancialReportsPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.FinancialReportsPage.xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.FinancialReportsPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\FinancialReportsPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\FinancialReportsPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\FinancialReportsPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\FinancialReportsPage.xaml

[C:/Users/<USER>/Desktop/CakeBistro/CakeBistro/Views/FormTemplate.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.FormTemplate.xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.FormTemplate.xaml
build_metadata.AdditionalFiles.TargetPath = Views\FormTemplate.xaml
build_metadata.AdditionalFiles.TargetPath = Views\FormTemplate.xaml
build_metadata.AdditionalFiles.RelativePath = Views\FormTemplate.xaml
build_metadata.AdditionalFiles.RelativePath = Views\FormTemplate.xaml

[C:/Users/<USER>/Desktop/CakeBistro/CakeBistro/Views/GoodsReceiptDetailPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.GoodsReceiptDetailPage.xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.GoodsReceiptDetailPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\GoodsReceiptDetailPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\GoodsReceiptDetailPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\GoodsReceiptDetailPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\GoodsReceiptDetailPage.xaml

[C:/Users/<USER>/Desktop/CakeBistro/CakeBistro/Views/GoodsReceiptPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.GoodsReceiptPage.xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.GoodsReceiptPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\GoodsReceiptPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\GoodsReceiptPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\GoodsReceiptPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\GoodsReceiptPage.xaml

[C:/Users/<USER>/Desktop/CakeBistro/CakeBistro/Views/InterBranchTransferFormFields.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.InterBranchTransferFormFields.xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.InterBranchTransferFormFields.xaml
build_metadata.AdditionalFiles.TargetPath = Views\InterBranchTransferFormFields.xaml
build_metadata.AdditionalFiles.TargetPath = Views\InterBranchTransferFormFields.xaml
build_metadata.AdditionalFiles.RelativePath = Views\InterBranchTransferFormFields.xaml
build_metadata.AdditionalFiles.RelativePath = Views\InterBranchTransferFormFields.xaml

[C:/Users/<USER>/Desktop/CakeBistro/CakeBistro/Views/InterBranchTransferFormPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.InterBranchTransferFormPage.xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.InterBranchTransferFormPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\InterBranchTransferFormPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\InterBranchTransferFormPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\InterBranchTransferFormPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\InterBranchTransferFormPage.xaml

[C:/Users/<USER>/Desktop/CakeBistro/CakeBistro/Views/InterBranchTransferPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.InterBranchTransferPage.xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.InterBranchTransferPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\InterBranchTransferPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\InterBranchTransferPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\InterBranchTransferPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\InterBranchTransferPage.xaml

[C:/Users/<USER>/Desktop/CakeBistro/CakeBistro/Views/InterDepartmentTransferPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.InterDepartmentTransferPage.xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.InterDepartmentTransferPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\InterDepartmentTransferPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\InterDepartmentTransferPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\InterDepartmentTransferPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\InterDepartmentTransferPage.xaml

[C:/Users/<USER>/Desktop/CakeBistro/CakeBistro/Views/InventoryFormFields.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.InventoryFormFields.xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.InventoryFormFields.xaml
build_metadata.AdditionalFiles.TargetPath = Views\InventoryFormFields.xaml
build_metadata.AdditionalFiles.TargetPath = Views\InventoryFormFields.xaml
build_metadata.AdditionalFiles.RelativePath = Views\InventoryFormFields.xaml
build_metadata.AdditionalFiles.RelativePath = Views\InventoryFormFields.xaml

[C:/Users/<USER>/Desktop/CakeBistro/CakeBistro/Views/InventoryFormPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.InventoryFormPage.xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.InventoryFormPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\InventoryFormPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\InventoryFormPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\InventoryFormPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\InventoryFormPage.xaml

[C:/Users/<USER>/Desktop/CakeBistro/CakeBistro/Views/InventoryPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.InventoryPage.xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.InventoryPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\InventoryPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\InventoryPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\InventoryPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\InventoryPage.xaml

[C:/Users/<USER>/Desktop/CakeBistro/CakeBistro/Views/LoadingPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.LoadingPage.xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.LoadingPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\LoadingPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\LoadingPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\LoadingPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\LoadingPage.xaml

[C:/Users/<USER>/Desktop/CakeBistro/CakeBistro/Views/MainPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.MainPage.xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.MainPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\MainPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\MainPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\MainPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\MainPage.xaml

[C:/Users/<USER>/Desktop/CakeBistro/CakeBistro/Views/NotificationSettingsPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.NotificationSettingsPage.xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.NotificationSettingsPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\NotificationSettingsPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\NotificationSettingsPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\NotificationSettingsPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\NotificationSettingsPage.xaml

[C:/Users/<USER>/Desktop/CakeBistro/CakeBistro/Views/NotificationsPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.NotificationsPage.xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.NotificationsPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\NotificationsPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\NotificationsPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\NotificationsPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\NotificationsPage.xaml

[C:/Users/<USER>/Desktop/CakeBistro/CakeBistro/Views/PackingPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.PackingPage.xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.PackingPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\PackingPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\PackingPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\PackingPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\PackingPage.xaml

[C:/Users/<USER>/Desktop/CakeBistro/CakeBistro/Views/ProductionBatchDetailPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.ProductionBatchDetailPage.xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.ProductionBatchDetailPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\ProductionBatchDetailPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\ProductionBatchDetailPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\ProductionBatchDetailPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\ProductionBatchDetailPage.xaml

[C:/Users/<USER>/Desktop/CakeBistro/CakeBistro/Views/ProductionBatchPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.ProductionBatchPage.xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.ProductionBatchPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\ProductionBatchPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\ProductionBatchPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\ProductionBatchPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\ProductionBatchPage.xaml

[C:/Users/<USER>/Desktop/CakeBistro/CakeBistro/Views/ProductionFormFields.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.ProductionFormFields.xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.ProductionFormFields.xaml
build_metadata.AdditionalFiles.TargetPath = Views\ProductionFormFields.xaml
build_metadata.AdditionalFiles.TargetPath = Views\ProductionFormFields.xaml
build_metadata.AdditionalFiles.RelativePath = Views\ProductionFormFields.xaml
build_metadata.AdditionalFiles.RelativePath = Views\ProductionFormFields.xaml

[C:/Users/<USER>/Desktop/CakeBistro/CakeBistro/Views/ProductionFormPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.ProductionFormPage.xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.ProductionFormPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\ProductionFormPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\ProductionFormPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\ProductionFormPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\ProductionFormPage.xaml

[C:/Users/<USER>/Desktop/CakeBistro/CakeBistro/Views/ProductionPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.ProductionPage.xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.ProductionPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\ProductionPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\ProductionPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\ProductionPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\ProductionPage.xaml

[C:/Users/<USER>/Desktop/CakeBistro/CakeBistro/Views/ProductPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.ProductPage.xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.ProductPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\ProductPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\ProductPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\ProductPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\ProductPage.xaml

[C:/Users/<USER>/Desktop/CakeBistro/CakeBistro/Views/ProfitabilityAnalysisPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.ProfitabilityAnalysisPage.xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.ProfitabilityAnalysisPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\ProfitabilityAnalysisPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\ProfitabilityAnalysisPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\ProfitabilityAnalysisPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\ProfitabilityAnalysisPage.xaml

[C:/Users/<USER>/Desktop/CakeBistro/CakeBistro/Views/ProfitabilityAnalysisReport.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.ProfitabilityAnalysisReport.xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.ProfitabilityAnalysisReport.xaml
build_metadata.AdditionalFiles.TargetPath = Views\ProfitabilityAnalysisReport.xaml
build_metadata.AdditionalFiles.TargetPath = Views\ProfitabilityAnalysisReport.xaml
build_metadata.AdditionalFiles.RelativePath = Views\ProfitabilityAnalysisReport.xaml
build_metadata.AdditionalFiles.RelativePath = Views\ProfitabilityAnalysisReport.xaml

[C:/Users/<USER>/Desktop/CakeBistro/CakeBistro/Views/PurchaseOrderDetailPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.PurchaseOrderDetailPage.xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.PurchaseOrderDetailPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\PurchaseOrderDetailPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\PurchaseOrderDetailPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\PurchaseOrderDetailPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\PurchaseOrderDetailPage.xaml

[C:/Users/<USER>/Desktop/CakeBistro/CakeBistro/Views/PurchaseOrderFormFields.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.PurchaseOrderFormFields.xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.PurchaseOrderFormFields.xaml
build_metadata.AdditionalFiles.TargetPath = Views\PurchaseOrderFormFields.xaml
build_metadata.AdditionalFiles.TargetPath = Views\PurchaseOrderFormFields.xaml
build_metadata.AdditionalFiles.RelativePath = Views\PurchaseOrderFormFields.xaml
build_metadata.AdditionalFiles.RelativePath = Views\PurchaseOrderFormFields.xaml

[C:/Users/<USER>/Desktop/CakeBistro/CakeBistro/Views/PurchaseOrderFormPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.PurchaseOrderFormPage.xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.PurchaseOrderFormPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\PurchaseOrderFormPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\PurchaseOrderFormPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\PurchaseOrderFormPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\PurchaseOrderFormPage.xaml

[C:/Users/<USER>/Desktop/CakeBistro/CakeBistro/Views/PurchaseOrderPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.PurchaseOrderPage.xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.PurchaseOrderPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\PurchaseOrderPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\PurchaseOrderPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\PurchaseOrderPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\PurchaseOrderPage.xaml

[C:/Users/<USER>/Desktop/CakeBistro/CakeBistro/Views/QualityCheckDetailPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.QualityCheckDetailPage.xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.QualityCheckDetailPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\QualityCheckDetailPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\QualityCheckDetailPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\QualityCheckDetailPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\QualityCheckDetailPage.xaml

[C:/Users/<USER>/Desktop/CakeBistro/CakeBistro/Views/QualityCheckFormPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.QualityCheckFormPage.xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.QualityCheckFormPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\QualityCheckFormPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\QualityCheckFormPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\QualityCheckFormPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\QualityCheckFormPage.xaml

[C:/Users/<USER>/Desktop/CakeBistro/CakeBistro/Views/QualityCheckReportPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.QualityCheckReportPage.xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.QualityCheckReportPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\QualityCheckReportPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\QualityCheckReportPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\QualityCheckReportPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\QualityCheckReportPage.xaml

[C:/Users/<USER>/Desktop/CakeBistro/CakeBistro/Views/QualityControlPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.QualityControlPage.xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.QualityControlPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\QualityControlPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\QualityControlPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\QualityControlPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\QualityControlPage.xaml

[C:/Users/<USER>/Desktop/CakeBistro/CakeBistro/Views/RawMaterialDetailPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.RawMaterialDetailPage.xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.RawMaterialDetailPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\RawMaterialDetailPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\RawMaterialDetailPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\RawMaterialDetailPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\RawMaterialDetailPage.xaml

[C:/Users/<USER>/Desktop/CakeBistro/CakeBistro/Views/RawMaterialFormFields.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.RawMaterialFormFields.xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.RawMaterialFormFields.xaml
build_metadata.AdditionalFiles.TargetPath = Views\RawMaterialFormFields.xaml
build_metadata.AdditionalFiles.TargetPath = Views\RawMaterialFormFields.xaml
build_metadata.AdditionalFiles.RelativePath = Views\RawMaterialFormFields.xaml
build_metadata.AdditionalFiles.RelativePath = Views\RawMaterialFormFields.xaml

[C:/Users/<USER>/Desktop/CakeBistro/CakeBistro/Views/RawMaterialFormPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.RawMaterialFormPage.xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.RawMaterialFormPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\RawMaterialFormPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\RawMaterialFormPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\RawMaterialFormPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\RawMaterialFormPage.xaml

[C:/Users/<USER>/Desktop/CakeBistro/CakeBistro/Views/RawMaterialRegistrationPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.RawMaterialRegistrationPage.xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.RawMaterialRegistrationPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\RawMaterialRegistrationPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\RawMaterialRegistrationPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\RawMaterialRegistrationPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\RawMaterialRegistrationPage.xaml

[C:/Users/<USER>/Desktop/CakeBistro/CakeBistro/Views/RawMaterialsPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.RawMaterialsPage.xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.RawMaterialsPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\RawMaterialsPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\RawMaterialsPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\RawMaterialsPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\RawMaterialsPage.xaml

[C:/Users/<USER>/Desktop/CakeBistro/CakeBistro/Views/RecipeDetailPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.RecipeDetailPage.xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.RecipeDetailPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\RecipeDetailPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\RecipeDetailPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\RecipeDetailPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\RecipeDetailPage.xaml

[C:/Users/<USER>/Desktop/CakeBistro/CakeBistro/Views/RecipeManagementPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.RecipeManagementPage.xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.RecipeManagementPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\RecipeManagementPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\RecipeManagementPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\RecipeManagementPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\RecipeManagementPage.xaml

[C:/Users/<USER>/Desktop/CakeBistro/CakeBistro/Views/RecipesPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.RecipesPage.xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.RecipesPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\RecipesPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\RecipesPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\RecipesPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\RecipesPage.xaml

[C:/Users/<USER>/Desktop/CakeBistro/CakeBistro/Views/ReconciliationPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.ReconciliationPage.xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.ReconciliationPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\ReconciliationPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\ReconciliationPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\ReconciliationPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\ReconciliationPage.xaml

[C:/Users/<USER>/Desktop/CakeBistro/CakeBistro/Views/ReportDetailPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.ReportDetailPage.xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.ReportDetailPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\ReportDetailPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\ReportDetailPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\ReportDetailPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\ReportDetailPage.xaml

[C:/Users/<USER>/Desktop/CakeBistro/CakeBistro/Views/ReportingDashboardPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.ReportingDashboardPage.xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.ReportingDashboardPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\ReportingDashboardPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\ReportingDashboardPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\ReportingDashboardPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\ReportingDashboardPage.xaml

[C:/Users/<USER>/Desktop/CakeBistro/CakeBistro/Views/ReportingPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.ReportingPage.xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.ReportingPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\ReportingPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\ReportingPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\ReportingPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\ReportingPage.xaml

[C:/Users/<USER>/Desktop/CakeBistro/CakeBistro/Views/ReportPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.ReportPage.xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.ReportPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\ReportPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\ReportPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\ReportPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\ReportPage.xaml

[C:/Users/<USER>/Desktop/CakeBistro/CakeBistro/Views/ReportSchedulePage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.ReportSchedulePage.xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.ReportSchedulePage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\ReportSchedulePage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\ReportSchedulePage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\ReportSchedulePage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\ReportSchedulePage.xaml

[C:/Users/<USER>/Desktop/CakeBistro/CakeBistro/Views/ReportsPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.ReportsPage.xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.ReportsPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\ReportsPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\ReportsPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\ReportsPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\ReportsPage.xaml

[C:/Users/<USER>/Desktop/CakeBistro/CakeBistro/Views/RoleManagementPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.RoleManagementPage.xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.RoleManagementPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\RoleManagementPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\RoleManagementPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\RoleManagementPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\RoleManagementPage.xaml

[C:/Users/<USER>/Desktop/CakeBistro/CakeBistro/Views/SalesAnalysisPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.SalesAnalysisPage.xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.SalesAnalysisPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\SalesAnalysisPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\SalesAnalysisPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\SalesAnalysisPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\SalesAnalysisPage.xaml

[C:/Users/<USER>/Desktop/CakeBistro/CakeBistro/Views/SalesOrderDetailPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.SalesOrderDetailPage.xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.SalesOrderDetailPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\SalesOrderDetailPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\SalesOrderDetailPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\SalesOrderDetailPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\SalesOrderDetailPage.xaml

[C:/Users/<USER>/Desktop/CakeBistro/CakeBistro/Views/SalesOrderFormFields.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.SalesOrderFormFields.xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.SalesOrderFormFields.xaml
build_metadata.AdditionalFiles.TargetPath = Views\SalesOrderFormFields.xaml
build_metadata.AdditionalFiles.TargetPath = Views\SalesOrderFormFields.xaml
build_metadata.AdditionalFiles.RelativePath = Views\SalesOrderFormFields.xaml
build_metadata.AdditionalFiles.RelativePath = Views\SalesOrderFormFields.xaml

[C:/Users/<USER>/Desktop/CakeBistro/CakeBistro/Views/SalesOrderFormPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.SalesOrderFormPage.xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.SalesOrderFormPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\SalesOrderFormPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\SalesOrderFormPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\SalesOrderFormPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\SalesOrderFormPage.xaml

[C:/Users/<USER>/Desktop/CakeBistro/CakeBistro/Views/SalesOrderPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.SalesOrderPage.xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.SalesOrderPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\SalesOrderPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\SalesOrderPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\SalesOrderPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\SalesOrderPage.xaml

[C:/Users/<USER>/Desktop/CakeBistro/CakeBistro/Views/SalesPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.SalesPage.xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.SalesPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\SalesPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\SalesPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\SalesPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\SalesPage.xaml

[C:/Users/<USER>/Desktop/CakeBistro/CakeBistro/Views/SalesTransactionListPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.SalesTransactionListPage.xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.SalesTransactionListPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\SalesTransactionListPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\SalesTransactionListPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\SalesTransactionListPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\SalesTransactionListPage.xaml

[C:/Users/<USER>/Desktop/CakeBistro/CakeBistro/Views/StockAdjustmentFormPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.StockAdjustmentFormPage.xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.StockAdjustmentFormPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\StockAdjustmentFormPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\StockAdjustmentFormPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\StockAdjustmentFormPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\StockAdjustmentFormPage.xaml

[C:/Users/<USER>/Desktop/CakeBistro/CakeBistro/Views/StockAdjustmentPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.StockAdjustmentPage.xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.StockAdjustmentPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\StockAdjustmentPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\StockAdjustmentPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\StockAdjustmentPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\StockAdjustmentPage.xaml

[C:/Users/<USER>/Desktop/CakeBistro/CakeBistro/Views/StockMovementTrackingPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.StockMovementTrackingPage.xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.StockMovementTrackingPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\StockMovementTrackingPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\StockMovementTrackingPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\StockMovementTrackingPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\StockMovementTrackingPage.xaml

[C:/Users/<USER>/Desktop/CakeBistro/CakeBistro/Views/StockTakeFormPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.StockTakeFormPage.xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.StockTakeFormPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\StockTakeFormPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\StockTakeFormPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\StockTakeFormPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\StockTakeFormPage.xaml

[C:/Users/<USER>/Desktop/CakeBistro/CakeBistro/Views/StockTakePage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.StockTakePage.xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.StockTakePage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\StockTakePage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\StockTakePage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\StockTakePage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\StockTakePage.xaml

[C:/Users/<USER>/Desktop/CakeBistro/CakeBistro/Views/SupplierDetailPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.SupplierDetailPage.xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.SupplierDetailPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\SupplierDetailPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\SupplierDetailPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\SupplierDetailPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\SupplierDetailPage.xaml

[C:/Users/<USER>/Desktop/CakeBistro/CakeBistro/Views/SupplierFormFields.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.SupplierFormFields.xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.SupplierFormFields.xaml
build_metadata.AdditionalFiles.TargetPath = Views\SupplierFormFields.xaml
build_metadata.AdditionalFiles.TargetPath = Views\SupplierFormFields.xaml
build_metadata.AdditionalFiles.RelativePath = Views\SupplierFormFields.xaml
build_metadata.AdditionalFiles.RelativePath = Views\SupplierFormFields.xaml

[C:/Users/<USER>/Desktop/CakeBistro/CakeBistro/Views/SupplierFormPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.SupplierFormPage.xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.SupplierFormPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\SupplierFormPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\SupplierFormPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\SupplierFormPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\SupplierFormPage.xaml

[C:/Users/<USER>/Desktop/CakeBistro/CakeBistro/Views/SupplierManagementPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.SupplierManagementPage.xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.SupplierManagementPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\SupplierManagementPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\SupplierManagementPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\SupplierManagementPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\SupplierManagementPage.xaml

[C:/Users/<USER>/Desktop/CakeBistro/CakeBistro/Views/SupplierPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.SupplierPage.xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.SupplierPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\SupplierPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\SupplierPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\SupplierPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\SupplierPage.xaml

[C:/Users/<USER>/Desktop/CakeBistro/CakeBistro/Views/SupplierPaymentPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.SupplierPaymentPage.xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.SupplierPaymentPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\SupplierPaymentPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\SupplierPaymentPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\SupplierPaymentPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\SupplierPaymentPage.xaml

[C:/Users/<USER>/Desktop/CakeBistro/CakeBistro/Views/ThemePreferencesPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.ThemePreferencesPage.xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.ThemePreferencesPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\ThemePreferencesPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\ThemePreferencesPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\ThemePreferencesPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\ThemePreferencesPage.xaml

[C:/Users/<USER>/Desktop/CakeBistro/CakeBistro/Views/UserManagementPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.UserManagementPage.xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.UserManagementPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\UserManagementPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\UserManagementPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\UserManagementPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\UserManagementPage.xaml

[C:/Users/<USER>/Desktop/CakeBistro/CakeBistro/Views/VehicleDetailPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.VehicleDetailPage.xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.VehicleDetailPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\VehicleDetailPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\VehicleDetailPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\VehicleDetailPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\VehicleDetailPage.xaml

[C:/Users/<USER>/Desktop/CakeBistro/CakeBistro/Views/VehicleListPage.xaml]
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.GenKind = Xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.VehicleListPage.xaml
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.Views.VehicleListPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\VehicleListPage.xaml
build_metadata.AdditionalFiles.TargetPath = Views\VehicleListPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\VehicleListPage.xaml
build_metadata.AdditionalFiles.RelativePath = Views\VehicleListPage.xaml

[C:/Users/<USER>/Desktop/CakeBistro/CakeBistro/wwwroot/css/site.css]
build_metadata.AdditionalFiles.GenKind = Css
build_metadata.AdditionalFiles.ManifestResourceName = CakeBistro.wwwroot.css.site.css
build_metadata.AdditionalFiles.TargetPath = wwwroot\css\site.css
build_metadata.AdditionalFiles.RelativePath = wwwroot\css\site.css
