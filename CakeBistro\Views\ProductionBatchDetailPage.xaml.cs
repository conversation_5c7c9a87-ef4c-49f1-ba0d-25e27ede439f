namespace CakeBistro.Views;

public partial class ProductionBatchDetailPage : ContentPage
{
    public ProductionBatchDetailPage(ProductionBatchDetailViewModel viewModel)
    {
        InitializeComponent();
        BindingContext = viewModel;
    }

    protected override async void OnAppearing()
    {
        base.OnAppearing();
        await ((ProductionBatchDetailViewModel)BindingContext).Initialize();
    }
}
