using CakeBistro.ViewModels;
using Microsoft.Maui.Controls;

namespace CakeBistro.Views
{
    public partial class AuthenticationPage : ContentPage
    {
        private readonly AuthenticationViewModel _viewModel;
        
        public AuthenticationViewModel ViewModel
        {
            get => _viewModel;
        }
        
        public AuthenticationPage(AuthenticationViewModel viewModel)
        {
            InitializeComponent();
            _viewModel = viewModel;
            BindingContext = _viewModel;
            
            // Subscribe to messaging center events
            MessagingCenter.Subscribe<AuthenticationViewModel, string>(this, "NavigateTo", (sender, args) =>
            {
                // Handle navigation requests from view model
                if (args == "MainPage")
                {
                    // Navigate to main page
                    Shell.Current.GoToAsync("//MainPage");
                }
            });
        }
        
        protected override void OnAppearing()
        {
            base.OnAppearing();
            // Reset form when page appears
            _viewModel.ClearForm();
        }
        
        protected override void OnDisappearing()
        {
            base.OnDisappearing();
            // Unsubscribe from events when page is not visible
            MessagingCenter.Unsubscribe<AuthenticationViewModel, string>(this, "NavigateTo");
        }
    }
}
