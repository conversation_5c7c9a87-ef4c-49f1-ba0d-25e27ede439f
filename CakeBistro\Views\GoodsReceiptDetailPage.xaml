<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="MCakeBistro.Views.GoodsReceiptDetailPage"
             Title="Goods Receipt Details">
    <VerticalStackLayout>
        <!-- Receipt header -->
        <Grid Padding="10" BackgroundColor="#f5f5f5">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>
            
            <VerticalStackLayout>
                <Label Text="Receipt Number:" TextColor="Gray"/>
                <Label Text="{Binding Receipt.ReceiptNumber}"
                       FontAttributes="Bold"/>
                
                <Label Text="Purchase Order:" TextColor="Gray" Margin="0,10,0,0"/>
                <Label Text="{Binding Receipt.PurchaseOrder.OrderNumber}"
                       FontAttributes="Bold"/>
            </VerticalStackLayout>
            
            <VerticalStackLayout Grid.Column="1" HorizontalOptions="End">
                <Label Text="Date:" TextColor="Gray"/>
                <Label Text="{Binding Receipt.ReceiptDate, StringFormat='{0:MMM d, yyyy}'}"
                       FontAttributes="Bold"/>
                
                <Label Text="Status:" TextColor="Gray" Margin="0,10,0,0"/>
                <Label Text="{Binding Receipt.Status}"
                       FontAttributes="Bold"/>
            </VerticalStackLayout>
        </Grid>
        
        <!-- Supplier info -->
        <Grid Padding="10" Margin="0,10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            
            <VerticalStackLayout>
                <Label Text="Supplier" FontSize="Medium" FontAttributes="Bold"/>
                <Label Text="{Binding Receipt.PurchaseOrder.Supplier.Name}"
                       FontAttributes="Bold"/>
                <Label Text="{Binding Receipt.PurchaseOrder.Supplier.Address}"
                       LineBreakMode="WordWrap"/>
                <Label Text="{Binding Receipt.PurchaseOrder.Supplier.City}, {Binding Receipt.PurchaseOrder.Supplier.State} {Binding Receipt.PurchaseOrder.Supplier.PostalCode}"
                       TextColor="Gray"/>
                <Label Text="{Binding Receipt.PurchaseOrder.Supplier.Country}"
                       TextColor="Gray"/>
            </VerticalStackLayout>
            
            <VerticalStackLayout Grid.Column="1">
                <Label Text="Contact" FontSize="Medium" FontAttributes="Bold"/>
                <Label Text="{Binding Receipt.PurchaseOrder.Supplier.ContactName}"
                       FontAttributes="Bold"/>
                <Label Text="{Binding Receipt.PurchaseOrder.Supplier.Phone}"
                       TextColor="Gray"/>
                <Label Text="{Binding Receipt.PurchaseOrder.Supplier.Email}"
                       TextColor="Gray"/>
            </VerticalStackLayout>
        </Grid>
        
        <!-- Items list -->
        <ScrollView>
            <VerticalStackLayout Padding="10">
                <Label Text="Items" FontSize="Large" FontAttributes="Bold"/>
                
                <CollectionView ItemsSource="{Binding Items}"
                                SelectionMode="Single"
                                SelectedItem="{Binding SelectedItem}">
                    <CollectionView.ItemTemplate>
                        <DataTemplate>
                            <Grid Padding="5">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                
                                <VerticalStackLayout>
                                    <Label Text="{Binding RawMaterial.Name}"
                                           FontAttributes="Bold"/>
                                    <Label Text="{Binding RawMaterial.Description}"
                                           TextColor="Gray"/>
                                </VerticalStackLayout>
                                
                                <Label Grid.Column="1" Text="{Binding ReceivedQuantity}" HorizontalOptions="Center" VerticalOptions="Center"/>
                                <Label Grid.Column="2" Text="{Binding RawMaterial.UnitOfMeasure}"
                                       HorizontalOptions="End" VerticalOptions="Center"/>
                            </Grid>
                        </DataTemplate>
                    </CollectionView.ItemTemplate>
                </CollectionView>
                
                <!-- Item actions -->
                <HorizontalStackLayout Spacing="10" Margin="0,10">
                    <Button Text="Add Item"
                            Command="{Binding AddItemCommand}"
                            HorizontalOptions="FillAndExpand"/>
                    <Button Text="Remove Item"
                            Command="{Binding RemoveItemCommand}"
                            HorizontalOptions="FillAndExpand"/>
                </HorizontalStackLayout>
                
                <!-- Action buttons -->
                <HorizontalStackLayout Spacing="10" Margin="0,20">
                    <Button Text="Save"
                            Command="{Binding SaveCommand}"
                            HorizontalOptions="FillAndExpand"/>
                    <Button Text="Cancel"
                            Command="{Binding CancelCommand}"
                            HorizontalOptions="FillAndExpand"/>
                    
                    <Button Text="Complete"
                            Command="{Binding SubmitCommand}"
                            HorizontalOptions="FillAndExpand"/>
                </HorizontalStackLayout>
            </VerticalStackLayout>
        </ScrollView>
    </VerticalStackLayout>
</ContentPage>