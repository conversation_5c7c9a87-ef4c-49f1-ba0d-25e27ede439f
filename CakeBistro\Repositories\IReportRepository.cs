using CakeBistro.Core.Models;

namespace CakeBistro.Repositories
{
    /// <summary>
    /// Interface for report data access operations
    /// </summary>
    public interface IReportRepository
    {
        /// <summary>
        /// Gets all reports from storage
        /// </summary>
        Task<List<FinancialReport>> GetAllAsync();
    
        /// <summary>
        /// Gets a specific report by ID
        /// </summary>
        Task<FinancialReport> GetByIdAsync(int id);
    
        /// <summary>
        /// Adds a new report to storage
        /// </summary>
        Task AddAsync(FinancialReport report);
    
        /// <summary>
        /// Updates an existing report
        /// </summary>
        Task UpdateAsync(FinancialReport report);
    
        /// <summary>
        /// Deletes a report
        /// </summary>
        Task DeleteAsync(int id);
    
        /// <summary>
        /// Gets reports within a date range
        /// </summary>
        Task<List<FinancialReport>> GetByDateRangeAsync(DateTime startDate, DateTime endDate);
    
        /// <summary>
        /// Gets reports by type
        /// </summary>
        Task<List<FinancialReport>> GetByTypeAsync(string reportType);
    }
}
