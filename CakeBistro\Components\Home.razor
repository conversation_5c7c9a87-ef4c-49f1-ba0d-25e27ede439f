@page "/"

<h1>Welcome to CakeBistro!</h1>

<p>This is a Blazor Hybrid app running in .NET MAUI.</p>

<div class="counter-section">
    <h3>Counter Example</h3>
    <p>Current count: @currentCount</p>
    <button class="btn btn-primary" @onclick="IncrementCount">Click me!</button>
</div>

<div class="features-section">
    <h3>Bakery Management Features</h3>
    <ul>
        <li>Inventory Management</li>
        <li>Sales Tracking</li>
        <li>Purchase Orders</li>
        <li>Financial Reporting</li>
        <li>Asset Management</li>
    </ul>
</div>

@code {
    private int currentCount = 0;

    private void IncrementCount()
    {
        currentCount++;
    }
}

<style>
    .counter-section {
        margin: 20px 0;
        padding: 20px;
        background-color: white;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .features-section {
        margin: 20px 0;
        padding: 20px;
        background-color: white;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .btn {
        padding: 10px 20px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
    }

    .btn-primary {
        background-color: #D2691E;
        color: white;
    }

    .btn-primary:hover {
        background-color: #8B4513;
    }
</style>
