using System.Globalization;

namespace CakeBistro.Converters
{
    public class NotificationTypeColorConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is string type)
            {
                return type.ToLower() switch
                {
                    "error" or "failed" => Color.FromArgb("#F44336"),  // Red
                    "warning" or "needsreview" => Color.FromArgb("#FF9800"),  // Orange
                    "success" or "passed" => Color.FromArgb("#4CAF50"),  // Green
                    _ => Color.FromArgb("#2196F3")  // Blue (default/info)
                };
            }
            return Color.FromArgb("#2196F3");  // Default blue
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            // Since this converter is likely used for one-way binding,
            // we can safely return a default notification type of "info".
            return "info";
        }
    }
}
