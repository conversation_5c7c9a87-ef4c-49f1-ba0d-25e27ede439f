using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using CakeBistro.Core.Models;

namespace CakeBistro.Repositories
{
    public class RecipeRepository : BaseRepository<Recipe>, IRecipeRepository
    {
        private readonly CakeBistroContext _context;
        private readonly IInventoryRepository _inventoryRepository;

        public RecipeRepository(
            CakeBistroContext context,
            IInventoryRepository inventoryRepository) : base(context)
        {
            _context = context;
            _inventoryRepository = inventoryRepository;
        }

        public async Task<IEnumerable<Recipe>> GetRecipesByProductAsync(int productId)
        {
            return await _context.Recipes
                .Include(r => r.Product)
                .Include(r => r.RecipeItems)
                    .ThenInclude(ri => ri.CakeBistro.Core.Models.RawMaterial)
                .Where(r => r.ProductId == productId)
                .ToListAsync();
        }

        public async Task<decimal> CalculateRecipeCostAsync(int recipeId)
        {
            var recipe = await _context.Recipes
                .Include(r => r.RecipeItems)
                    .ThenInclude(ri => ri.CakeBistro.Core.Models.RawMaterial)
                .FirstOrDefaultAsync(r => r.Id == recipeId);

            if (recipe == null)
                throw new ArgumentException("Recipe not found", nameof(recipeId));

            decimal totalCost = 0;
            foreach (var item in recipe.RecipeItems)
            {
                var latestBatch = await _inventoryRepository.GetLatestBatchAsync(item.RawMaterialId);
                if (latestBatch != null)
                {
                    totalCost += latestBatch.UnitCost * item.Quantity;
                }
            }

            return totalCost;
        }
    }
}
