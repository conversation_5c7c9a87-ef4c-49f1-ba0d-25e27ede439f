using CakeBistro.Models;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq;

namespace CakeBistro.Services
{
    public class DriverService
    {
        private readonly List<Driver> _drivers = new();

        public Task<Driver> CreateDriverAsync(Driver driver)
        {
            driver.Id = _drivers.Count > 0 ? _drivers.Max(d => d.Id) + 1 : 1;
            _drivers.Add(driver);
            return Task.FromResult(driver);
        }

        public Task<List<Driver>> GetAllDriversAsync()
        {
            return Task.FromResult(_drivers.ToList());
        }

        public Task<bool> RemoveDriverAsync(int id)
        {
            var driver = _drivers.FirstOrDefault(d => d.Id == id);
            if (driver == null) return Task.FromResult(false);
            _drivers.Remove(driver);
            return Task.FromResult(true);
        }
    }
}
