<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2022/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="CakeBistro.Views.AssetPage"
             Title="Asset Management">
    <ContentPage.Content>
        <ScrollView>
            <VerticalStackLayout Spacing="25" Padding="30">
                <!-- Asset Registration -->
                <Label Text="Fixed Assets" FontSize="24" HorizontalOptions="Start"/>
                
                <HorizontalStackLayout>
                    <Entry Placeholder="Search assets..." WidthRequest="200"/>
                    <Button Text="Add Asset" Clicked="OnAddAssetClicked"/>
                </HorizontalStackLayout>
                
                <CollectionView ItemsSource="{Binding FixedAssets}"
                                  SelectedItem="{Binding SelectedAsset, Mode=TwoWay}"
                                  SelectionMode="Single">
                    <CollectionView.ItemsLayout>
                        <LinearItemsLayout Orientation="Vertical"/>
                    </CollectionView.ItemsLayout>
                    <CollectionView.ItemTemplate>
                        <DataTemplate>
                            <Frame Padding="10" Margin="5">
                                <Grid ColumnDefinitions="*,*,*" RowDefinitions="Auto,Auto">
                                    <Label Grid.Row="0" Grid.Column="0" Text="{Binding Name}"/>
                                    <Label Grid.Row="0" Grid.Column="1" Text="{Binding Category}"/>
                                    <Label Grid.Row="0" Grid.Column="2" Text="{Binding CurrentValue, StringFormat='{0:C2}'}"/>
                                    
                                    <Label Grid.Row="1" Grid.Column="0" Text="{Binding Location}"/>
                                    <Label Grid.Row="1" Grid.Column="1" Text="{Binding Status}"/>
                                    <Label Grid.Row="1" Grid.Column="2" Text="{Binding PurchaseDate, StringFormat='Purchased: {0:yyyy-MM-dd}'}"/>
                                </Grid>
                            </Frame>
                        </DataTemplate>
                    </CollectionView.ItemTemplate>
                </CollectionView>
                
                <!-- Asset Details -->
                <Label Text="Selected Asset Details" FontSize="18" HorizontalOptions="Start"/>
                
                <Grid ColumnDefinitions="*,*" RowDefinitions="Auto,Auto,Auto,Auto,Auto,Auto,Auto">
                    <Label Grid.Row="0" Grid.Column="0" Text="Name:"/>
                    <Entry Grid.Row="0" Grid.Column="1" Text="{Binding SelectedAsset.Name, Mode=TwoWay}"/>
                    
                    <Label Grid.Row="1" Grid.Column="0" Text="Category:"/>
                    <Picker Grid.Row="1" Grid.Column="1" ItemsSource="{Binding AssetCategories}" SelectedItem="{Binding SelectedCategory, Mode=TwoWay}"/>
                    
                    <Label Grid.Row="2" Grid.Column="0" Text="Location:"/>
                    <Entry Grid.Row="2" Grid.Column="1" Text="{Binding SelectedAsset.Location, Mode=TwoWay}"/>
                    
                    <Label Grid.Row="3" Grid.Column="0" Text="Status:"/>
                    <Picker Grid.Row="3" Grid.Column="1" ItemsSource="{Binding AssetStatuses}" SelectedItem="{Binding SelectedAsset.Status, Mode=TwoWay}"/>
                    
                    <Label Grid.Row="4" Grid.Column="0" Text="Purchase Date:"/>
                    <DatePicker Grid.Row="4" Grid.Column="1" Date="{Binding SelectedAsset.PurchaseDate, Mode=TwoWay, StringFormat={0:yyyy-MM-dd}}"/>
                    
                    <Label Grid.Row="5" Grid.Column="0" Text="Purchase Price:"/>
                    <Entry Grid.Row="5" Grid.Column="1" Text="{Binding SelectedAsset.PurchasePrice, Mode=TwoWay, StringFormat={}{0:C2}}"/>
                    
                    <Label Grid.Row="6" Grid.Column="0" Text="Notes:"/>
                    <Editor Grid.Row="6" Grid.Column="1" Text="{Binding SelectedAsset.Notes, Mode=TwoWay}"/>
                </Grid>
                
                <HorizontalStackLayout Spacing="10" HorizontalOptions="Center">
                    <Button Text="Save Changes" Clicked="OnSaveAssetChanges" IsEnabled="{Binding SelectedAsset != null}"/>
                    <Button Text="Delete Asset" Clicked="OnDeleteAsset" IsEnabled="{Binding SelectedAsset != null}"/>
                    <Button Text="View Depreciation Schedule" Clicked="OnViewDepreciationSchedule" IsEnabled="{Binding SelectedAsset != null}"/>
                </HorizontalStackLayout>
                
                <!-- Depreciation Tracking -->
                <Label Text="Depreciation Schedule" FontSize="24" HorizontalOptions="Start"/>
                
                <Grid ColumnDefinitions="*,*,*" RowDefinitions="Auto,Auto,Auto">
                    <Label Grid.Row="0" Grid.Column="0" Text="Depreciation Method:"/>
                    <Picker Grid.Row="0" Grid.Column="1" ItemsSource="{Binding DepreciationMethods}" SelectedItem="{Binding SelectedDepreciationMethod, Mode=TwoWay}" WidthRequest="120"/>
                    <Button Grid.Row="0" Grid.Column="2" Text="Calculate Depreciation" Clicked="OnCalculateDepreciation" IsEnabled="{Binding SelectedAsset != null}"/>
                    
                    <Label Grid.Row="1" Grid.Column="0" Text="Annual Depreciation Rate:"/>
                    <Label Grid.Row="1" Grid.Column="1" Text="{Binding AnnualDepreciationRate, StringFormat='{0:P2}'}"/>
                    
                    <Label Grid.Row="2" Grid.Column="0" Text="Net Book Value:"/>
                    <Label Grid.Row="2" Grid.Column="1" Text="{Binding NetBookValue, StringFormat='{0:C2}'}"/>
                </Grid>
                
                <!-- Location History -->
                <Label Text="Location History" FontSize="24" HorizontalOptions="Start"/>
                
                <CollectionView ItemsSource="{Binding LocationHistory}"
                                  SelectionMode="None">
                    <CollectionView.ItemsLayout>
                        <LinearItemsLayout Orientation="Vertical"/>
                    </CollectionView.ItemsLayout>
                    <CollectionView.ItemTemplate>
                        <DataTemplate>
                            <Frame Padding="10" Margin="5">
                                <Grid ColumnDefinitions="*,*,*" RowDefinitions="Auto,Auto">
                                    <Label Grid.Row="0" Grid.Column="0" Text="{Binding NewLocation}"/>
                                    <Label Grid.Row="0" Grid.Column="1" Text="{Binding ChangeDate, StringFormat='Date: {0:yyyy-MM-dd}'}"/>
                                    <Label Grid.Row="0" Grid.Column="2" Text="{Binding Notes}"/>
                                    
                                    <Label Grid.Row="1" Grid.Column="0" Text="Old Location:"/>
                                    <Label Grid.Row="1" Grid.Column="1" Text="{Binding OldLocation}"/>
                                    <Label Grid.Row="1" Grid.Column="2" Text="{Binding Id, StringFormat='ID: {0}'}"/>
                                </Grid>
                            </Frame>
                        </DataTemplate>
                    </CollectionView.ItemTemplate>
                </CollectionView>
                
                <!-- Maintenance Scheduling -->
                <Label Text="Maintenance Scheduling" FontSize="24" HorizontalOptions="Start"/>
                
                <Grid ColumnDefinitions="*,*,*" RowDefinitions="Auto,Auto,Auto,Auto">
                    <Label Grid.Row="0" Grid.Column="0" Text="Last Maintenance:"/>
                    <Label Grid.Row="0" Grid.Column="1" Text="{Binding LastMaintenanceDate, StringFormat='Date: {0:yyyy-MM-dd}'}"/>
                    <Button Grid.Row="0" Grid.Column="2" Text="Record Maintenance" Clicked="OnRecordMaintenance" IsEnabled="{Binding SelectedAsset != null}"/>
                    
                    <Label Grid.Row="1" Grid.Column="0" Text="Next Scheduled Maintenance:"/>
                    <DatePicker Grid.Row="1" Grid.Column="1" Date="{Binding NextScheduledDate, Mode=TwoWay, StringFormat={0:yyyy-MM-dd}}"/>
                    <Button Grid.Row="1" Grid.Column="2" Text="Schedule Maintenance" Clicked="OnScheduleMaintenance" IsEnabled="{Binding SelectedAsset != null}"/>
                    
                    <Label Grid.Row="2" Grid.Column="0" Text="Maintenance Provider:"/>
                    <Entry Grid.Row="2" Grid.Column="1" Text="{Binding MaintenanceProvider, Mode=TwoWay}"/>
                    <Button Grid.Row="2" Grid.Column="2" Text="View Maintenance History" Clicked="OnViewMaintenanceHistory" IsEnabled="{Binding SelectedAsset != null}"/>
                    
                    <Label Grid.Row="3" Grid.Column="0" Text="Maintenance Cost:"/>
                    <Entry Grid.Row="3" Grid.Column="1" Text="{Binding MaintenanceCost, Mode=TwoWay, StringFormat={}{0:C2}}"/>
                    <Button Grid.Row="3" Grid.Column="2" Text="Record Cost" Clicked="OnRecordMaintenanceCost" IsEnabled="{Binding SelectedAsset != null}"/>
                </Grid>
                
                <!-- Asset Reports -->
                <Label Text="Asset Reports" FontSize="24" HorizontalOptions="Start"/>
                
                <HorizontalStackLayout Spacing="10" HorizontalOptions="Center">
                    <Button Text="Generate Asset Register" Clicked="OnGenerateAssetRegister"/>
                    <Button Text="Generate Location Report" Clicked="OnGenerateLocationReport"/>
                    <Button Text="Generate Maintenance Schedule" Clicked="OnGenerateMaintenanceSchedule"/>
                </HorizontalStackLayout>
                
                <!-- Navigation -->
                <HorizontalStackLayout Spacing="10" HorizontalOptions="Center">
                    <Button Text="Back to Dashboard" Clicked="OnBackToDashboard"/>
                    <Button Text="Export to PDF" Clicked="OnExportToPDF"/>
                    <Button Text="Export to Excel" Clicked="OnExportToExcel"/>
                    <Button Text="Print Report" Clicked="OnPrintReport"/>
                </HorizontalStackLayout>
            </VerticalStackLayout>
        </ScrollView>
    </ContentPage.Content>
</ContentPage>