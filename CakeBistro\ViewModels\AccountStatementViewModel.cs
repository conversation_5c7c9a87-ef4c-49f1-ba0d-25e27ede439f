using CakeBistro.Models;
using CakeBistro.Services;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.Collections.ObjectModel;
using System.Threading.Tasks;
using System;
using Microsoft.Maui.Graphics;

namespace CakeBistro.ViewModels
{
    public partial class AccountStatementViewModel : ObservableObject
    {
        private readonly IFinanceService _financeService;

        [ObservableProperty] private int accountId;
        [ObservableProperty] private DateTime startDate = DateTime.Today.AddMonths(-1);
        [ObservableProperty] private DateTime endDate = DateTime.Today;
        [ObservableProperty] private ObservableCollection<Transaction> transactions = new();
        [ObservableProperty] private decimal openingBalance;
        [ObservableProperty] private decimal closingBalance;
        [ObservableProperty] private string? exportResultMessage;

        private Color _statusColor = Colors.Transparent;
        public Color StatusColor
        {
            get => _statusColor;
            set => SetProperty(ref _statusColor, value);
        }

        public AccountStatementViewModel(IFinanceService financeService)
        {
            _financeService = financeService;
        }

        [RelayCommand]
        public async Task LoadStatementAsync()
        {
            var statement = await _financeService.GetAccountStatementAsync(AccountId, StartDate, EndDate);
            Transactions = new ObservableCollection<Transaction>(statement.Transactions);
            OpeningBalance = statement.OpeningBalance;
            ClosingBalance = statement.ClosingBalance;

            // Example for success:
            // StatusMessage = "Statement generated successfully.";
            // StatusColor = Color.FromArgb("#388E3C");
            // Example for error:
            // StatusMessage = $"Error: {ex.Message}";
            // StatusColor = Color.FromArgb("#D32F2F");
            // Example for info:
            // StatusMessage = "Please select an account.";
            // StatusColor = Color.FromArgb("#1976D2");
        }

        [RelayCommand]
        public async Task ExportStatementAsync(string format)
        {
            var filePath = await _financeService.ExportAccountStatementAsync(AccountId, StartDate, EndDate, format);
            ExportResultMessage = $"Statement exported: {filePath}";

            // Example for success:
            // StatusMessage = "Statement exported successfully.";
            // StatusColor = Color.FromArgb("#388E3C");
            // Example for error:
            // StatusMessage = $"Error: {ex.Message}";
            // StatusColor = Color.FromArgb("#D32F2F");
            // Example for info:
            // StatusMessage = "Please select an account.";
            // StatusColor = Color.FromArgb("#1976D2");
        }
    }
}
