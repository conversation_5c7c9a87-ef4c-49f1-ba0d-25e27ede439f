namespace CakeBistro.Views;

public partial class QualityCheckDetailPage : ContentPage
{
    public QualityCheckDetailPage(QualityCheckDetailViewModel viewModel)
    {
        InitializeComponent();
        BindingContext = viewModel;
    }

    protected override async void OnAppearing()
    {
        base.OnAppearing();
        await ((QualityCheckDetailViewModel)BindingContext).Initialize();
    }
}
