<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="CakeBistro.Views.InventoryPage"
             xmlns:viewModels="clr-namespace:CakeBistro.ViewModels"
             xmlns:models="clr-namespace:CakeBistro.Models"
             Title="Inventory">
    <ContentPage.BindingContext>
        <viewModels:InventoryViewModel />
    </ContentPage.BindingContext>
    
    <ScrollView>
        <VerticalStackLayout Spacing="16" Padding="16">
            <!-- Status Message -->
            <Label Text="{Binding StatusMessage}"
                   IsVisible="{Binding StatusMessage, Converter={StaticResource EmptyToFalseConverter}}"
                   TextColor="{Binding StatusColor}" />
            
            <!-- Low Stock Alerts -->
            <Frame Header="Low Stock Alerts"
                   IsVisible="{Binding LowStockRecommendations, Converter={StaticResource NullToFalseConverter}}">
                <VerticalStackLayout Spacing="8">
                    <CollectionView ItemsSource="{Binding LowStockRecommendations}"
                                      SelectionMode="None">
                        <CollectionView.ItemTemplate>
                            <DataTemplate x:DataType="models:RawMaterial">
                                <Frame Margin="0,4"
                                       BackgroundColor="{Binding Source={x:Reference This}, Converter={StaticResource StockLevelColorConverter}}">
                                    <Grid ColumnDefinitions="*, Auto">
                                        <VerticalStackLayout Grid.Column="0">
                                            <Label Text="{Binding Name}"
                                                   FontAttributes="Bold" />
                                            <Label Text="{Binding Unit, StringFormat='Unit: {0}'}" />
                                            <Label Text="{Binding CurrentStock, StringFormat='Current Stock: {0:F0}'}" />
                                            <Label Text="{Binding MinimumStock, StringFormat='Minimum Stock: {0:F0}'}" />
                                        </VerticalStackLayout>
                                        
                                        <Label Text="{Binding Source={x:Reference This}, Converter={StaticResource StockLevelTextConverter}}"
                                               Grid.Column="1"
                                               HorizontalOptions="End"
                                               VerticalOptions="Center"
                                               TextColor="White"
                                               FontAttributes="Bold" />
                                    </Grid>
                                </Frame>
                            </DataTemplate>
                        </CollectionView.ItemTemplate>
                    </CollectionView>
                    
                    <Button Text="View Reorder Recommendations"
                            Command="{Binding ViewReorderRecommendationsCommand}"
                            IsEnabled="{Binding LowStockRecommendations.Count, Converter={StaticResource GreaterThanZeroConverter}}"
                            BackgroundColor="Blue"
                            TextColor="White" />
                </VerticalStackLayout>
            </Frame>
            
            <!-- Expiring Batch Alerts -->
            <Frame Header="Expiring Batches"
                   IsVisible="{Binding ActiveExpiringBatchAlerts, Converter={StaticResource NullToFalseConverter}}"
                   BackgroundColor="LightYellow">
                <VerticalStackLayout Spacing="8">
                    <Button Text="Check for Expiring Batches"
                            Command="{Binding ViewExpiringBatchesCommand}"
                            IsEnabled="{Binding ViewExpiringBatchesCommand.IsExecuting, Converter={StaticResource InvertBooleanConverter}}"
                            BackgroundColor="Blue"
                            TextColor="White" />
                    
                    <ActivityIndicator IsRunning="{Binding IsProcessing}"
                                       IsVisible="{Binding IsProcessing}" />
                    
                    <CollectionView ItemsSource="{Binding ActiveExpiringBatchAlerts}"
                                      SelectionMode="Single"
                                      SelectedItem="{Binding SelectedExpiringBatchAlert}">
                        <CollectionView.ItemTemplate>
                            <DataTemplate x:DataType="models:ExpiringBatchAlert">
                                <Frame Margin="0,4">
                                    <Grid ColumnDefinitions="*, *, *">
                                        <VerticalStackLayout Grid.Column="0">
                                            <Label Text="{Binding RawMaterialName}"
                                                   FontAttributes="Bold" />
                                            <Label Text="{Binding Quantity, StringFormat='Quantity: {0:F0}'}" />
                                            <Label Text="{Binding ExpiryDate, StringFormat='Expires: {0:d}'}" />
                                        </VerticalStackLayout>
                                        
                                        <VerticalStackLayout Grid.Column="1">
                                            <Label Text="{Binding StorageLocation, StringFormat='Location: {0}'}" />
                                            <Label Text="{Binding CreatedDate, StringFormat='Alert Date: {0:d}'}" />
                                            <Label Text="{Binding DaysUntilExpiry, StringFormat='Days Until Expiry: {0}'}"
                                                   IsVisible="{Binding DaysUntilExpiry, Converter={StaticResource NullToFalseConverter}}" />
                                        </VerticalStackLayout>
                                        
                                        <VerticalStackLayout Grid.Column="2"
                                                           HorizontalOptions="EndAndExpand">
                                            <Label Text="{Binding Status}"
                                                   FontAttributes="Bold"
                                                   TextColor="{Binding Status, Converter={StaticResource AlertStatusToColorConverter}}" />
                                            <Button Text="{Binding Status, Converter={StaticResource AlertActionTextConverter}}"
                                                    Command="{Binding Source={RelativeSource AncestorType={x:Type viewModels:InventoryViewModel}}, Path=ResolveAlertCommand}"
                                                    CommandParameter="{Binding .}"
                                                    IsEnabled="{Binding Source={RelativeSource AncestorType={x:Type viewModels:InventoryViewModel}}, Path=IsProcessing, Converter={StaticResource InvertBooleanConverter}}"
                                                    BackgroundColor="{Binding Status, Converter={StaticResource AlertActionButtonColorConverter}}"
                                                    TextColor="White" />
                                        </VerticalStackLayout>
                                    </Grid>
                                </Frame>
                            </DataTemplate>
                        </CollectionView.ItemTemplate>
                    </CollectionView>
                    
                    <Label Text="Legend: "
                           FontAttributes="Bold" />
                    <HorizontalStackLayout Spacing="8">
                        <BoxView WidthRequest="20" HeightRequest="20" Color="Orange" />
                        <Label Text="Pending - Less than 30 days until expiry" />
                    </HorizontalStackLayout>
                    <HorizontalStackLayout Spacing="8">
                        <BoxView WidthRequest="20" HeightRequest="20" Color="Red" />
                        <Label Text="Urgent - Expired or less than 7 days until expiry" />
                    </HorizontalStackLayout>
                    <HorizontalStackLayout Spacing="8">
                        <BoxView WidthRequest="20" HeightRequest="20" Color="Gray" />
                        <Label Text="Acknowledged - Alert has been acknowledged" />
                    </HorizontalStackLayout>
                </VerticalStackLayout>
            </Frame>
            
            <!-- Raw Materials List -->
            <Frame Header="Raw Materials">
                <VerticalStackLayout Spacing="8">
                    <SearchBar Placeholder="Search materials..." />
                    
                    <CollectionView ItemsSource="{Binding RawMaterials}"
                                      SelectionMode="Single"
                                      SelectedItem="{Binding SelectedMaterial}">
                        <CollectionView.ItemTemplate>
                            <DataTemplate x:DataType="models:RawMaterial">
                                <Frame Margin="0,4">
                                    <Grid ColumnDefinitions="*, Auto">
                                        <VerticalStackLayout Grid.Column="0">
                                            <Label Text="{Binding Name}"
                                                   FontAttributes="Bold" />
                                            <Label Text="{Binding Unit, StringFormat='Unit: {0}'}" />
                                            <Label Text="{Binding CurrentStock, StringFormat='Current Stock: {0:F0}'}" />
                                            <Label Text="{Binding MinimumStock, StringFormat='Minimum Stock: {0:F0}'}" />
                                            <Label Text="{Binding LastStockTake, StringFormat='Last Stock Take: {0:d}'}" />
                                        </VerticalStackLayout>
                                        
                                        <Button Text="Details"
                                                Command="{Binding Source={RelativeSource AncestorType={x:Type viewModels:InventoryViewModel}}, Path=DetailCommand}"
                                                CommandParameter="{Binding Id}"
                                                Grid.Column="1"
                                                HorizontalOptions="End"
                                                VerticalOptions="Center" />
                                    </Grid>
                                </Frame>
                            </DataTemplate>
                        </CollectionView.ItemTemplate>
                    </CollectionView>
                    
                    <ActivityIndicator IsRunning="{Binding IsProcessing}"
                                       IsVisible="{Binding IsProcessing}" />
                </VerticalStackLayout>
            </Frame>
            
            <!-- Material Details -->
            <Frame Header="Material Details"
                   IsVisible="{Binding SelectedMaterial, Converter={StaticResource NullToFalseConverter}}">
                <VerticalStackLayout Spacing="8">
                    <Label Text="{Binding SelectedMaterial.Name}"
                           FontSize="Large"
                           FontAttributes="Bold" />
                    
                    <Label Text="{Binding SelectedMaterial.Unit, StringFormat='Unit: {0}'}" />
                    
                    <Label Text="{Binding SelectedMaterial.CurrentStock, StringFormat='Current Stock: {0:F0}'}" />
                    
                    <Label Text="{Binding SelectedMaterial.MinimumStock, StringFormat='Minimum Stock: {0:F0}'}" />
                    
                    <Label Text="{Binding SelectedMaterial.LastStockTake, StringFormat='Last Stock Take: {0:d}'}" />
                    
                    <HorizontalStackLayout Spacing="8">
                        <Button Text="Record Movement"
                                Command="{Binding RecordStockMovementCommand}"
                                IsEnabled="{Binding RecordStockMovementCommand.IsExecuting, Converter={StaticResource InvertBooleanConverter}}" />
                        <Button Text="+"
                                Command="{Binding AddStockCommand}"
                                IsEnabled="{Binding AddStockCommand.CanExecute, Mode=OneWay}"
                                BackgroundColor="Green"
                                TextColor="White" />
                        <Label Text="{Binding SelectedMaterial.CurrentStock, StringFormat='{0:F0}'}"
                               FontSize="Medium"
                               HorizontalOptions="CenterAndExpand"
                               VerticalOptions="Center" />
                        <Button Text="-"
                                Command="{Binding RemoveStockCommand}"
                                IsEnabled="{Binding RemoveStockCommand.CanExecute, Mode=OneWay}"
                                BackgroundColor="Red"
                                TextColor="White" />
                    </HorizontalStackLayout>
                    
                    <HorizontalStackLayout Spacing="8">
                        <Button Text="Save Changes"
                                Command="{Binding SaveChangesCommand}"
                                IsEnabled="{Binding SaveChangesCommand.CanExecute, Mode=OneWay}"
                                BackgroundColor="Blue"
                                TextColor="White" />
                        <Button Text="Cancel"
                                Command="{Binding CancelEditCommand}"
                                IsEnabled="{Binding CancelEditCommand.CanExecute, Mode=OneWay}"
                                BackgroundColor="Gray"
                                TextColor="White" />
                    </HorizontalStackLayout>
                </VerticalStackLayout>
            </Frame>
            
            <!-- Batch Information -->
            <Frame Header="Inventory Batches"
                   IsVisible="{Binding SelectedMaterialBatches, Converter={StaticResource NullToFalseConverter}}">
                <VerticalStackLayout Spacing="8">
                    <!-- Add Inventory Batch Button -->
                    <Button Text="Add Batch"
                            Command="{Binding AddInventoryBatchCommand}"
                            IsEnabled="{Binding SelectedMaterial, Converter={StaticResource NullToFalseConverter}}"
                            BackgroundColor="Green"
                            TextColor="White" />
                    <CollectionView ItemsSource="{Binding SelectedMaterialBatches}"
                                      SelectionMode="None">
                        <CollectionView.ItemTemplate>
                            <DataTemplate x:DataType="models:InventoryBatch">
                                <Frame Margin="0,4">
                                    <Grid ColumnDefinitions="*, *, *">
                                        <VerticalStackLayout Grid.Column="0">
                                            <Label Text="{Binding BatchNumber, StringFormat='Batch: {0}'}" />
                                            <Label Text="{Binding ProductionDate, StringFormat='Produced: {0:d}'}" />
                                            <Label Text="{Binding ExpiryDate, StringFormat='Expires: {0:d}'}" />
                                        </VerticalStackLayout>
                                        
                                        <VerticalStackLayout Grid.Column="1">
                                            <Label Text="{Binding Quantity, StringFormat='Qty: {0:F0}'}" />
                                            <Label Text="{Binding Cost, StringFormat='Cost: {0:C}'}" />
                                        </VerticalStackLayout>
                                        
                                        <VerticalStackLayout Grid.Column="2"
                                                           HorizontalOptions="EndAndExpand"
                                                           VerticalOptions="Center"
                                                           Spacing="4">
                                            <Label Text="{Binding StorageLocation}" HorizontalOptions="End" />
                                            <Button Text="Edit"
                                                    Command="{Binding Source={RelativeSource AncestorType={x:Type viewModels:InventoryViewModel}}, Path=EditInventoryBatchCommand}"
                                                    CommandParameter="{Binding .}"
                                                    BackgroundColor="Blue"
                                                    TextColor="White"
                                                    HorizontalOptions="End" />
                                        </VerticalStackLayout>
                                    </Grid>
                                </Frame>
                            </DataTemplate>
                        </CollectionView.ItemTemplate>
                    </CollectionView>
                    
                    <Button Text="View All Batches"
                            Command="{Binding ViewBatchesCommand}"
                            CommandParameter="{Binding Source={x:Reference This}}"
                            IsEnabled="{Binding SelectedMaterial, Converter={StaticResource NullToFalseConverter}}"
                            BackgroundColor="Blue"
                            TextColor="White" />
                </VerticalStackLayout>
            </Frame>
            
            <!-- New Material Form -->
            <Frame Header="New Raw Material">
                <VerticalStackLayout Spacing="8">
                    <Entry Placeholder="Name"
                           Text="{Binding NewRawMaterial.Name}"
                           TextColor="Black" />
                    
                    <Picker ItemsSource="{Binding UnitsOfMeasure}"
                          SelectedItem="{Binding NewRawMaterial.Unit}"
                          Title="Select Unit" />
                    
                    <Entry Placeholder="Current Stock"
                           Text="{Binding NewRawMaterial.CurrentStock, StringFormat='{0:F0}'}"
                           Keyboard="Numeric"
                           TextColor="Black" />
                    
                    <Entry Placeholder="Minimum Stock"
                           Text="{Binding NewRawMaterial.MinimumStock, StringFormat='{0:F0}'}"
                           Keyboard="Numeric"
                           TextColor="Black" />
                    
                    <Button Text="Add Material"
                            Command="{Binding AddMaterialCommand}"
                            IsEnabled="{Binding AddMaterialCommand.IsExecuting, Converter={StaticResource InvertBooleanConverter}}" />
                </VerticalStackLayout>
            </Frame>
            
            <!-- Monthly Stock Take -->
            <Button Text="Perform Monthly Stock Take"
                    Command="{Binding PerformMonthlyStockTakeCommand}"
                    IsEnabled="{Binding IsProcessing, Converter={StaticResource NullToFalseConverter}}"
                    BackgroundColor="DarkGreen"
                    TextColor="White" />

            <!-- Inter-Branch Transfer -->
            <Button Text="Transfer Stock to Branch"
                    Command="{Binding TransferStockCommand}"
                    IsEnabled="{Binding SelectedMaterial, Converter={StaticResource NullToFalseConverter}}"
                    BackgroundColor="DarkOrange"
                    TextColor="White" />

            <!-- Document Upload -->
            <Button Text="Upload Raw Material Document"
                    Command="{Binding UploadDocumentCommand}"
                    IsEnabled="{Binding SelectedMaterial, Converter={StaticResource NullToFalseConverter}}"
                    BackgroundColor="DarkSlateBlue"
                    TextColor="White" />

            <!-- Advanced Stock Report -->
            <Button Text="Generate Advanced Stock Report"
                    Command="{Binding LoadDataCommand}"
                    BackgroundColor="DarkCyan"
                    TextColor="White" />
        </VerticalStackLayout>
    </ScrollView>
</ContentPage>