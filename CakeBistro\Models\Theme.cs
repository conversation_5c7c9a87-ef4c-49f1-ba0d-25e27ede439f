
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace CakeBistro.Models
{
    /// <summary>
    /// Represents a custom theme definition
    /// </summary>
    [Table("CustomThemes")]
    public class CustomTheme
    {
        /// <summary>
        /// Gets or sets the unique identifier of the theme
        /// </summary>
        [Key]
        public int Id { get; set; }

        /// <summary>
        /// Gets or sets the name of the theme
        /// </summary>
        [Required]
        [StringLength(100)]
        public string Name { get; set; }

        /// <summary>
        /// Gets or sets the description of the theme
        /// </summary>
        [StringLength(500)]
        public string Description { get; set; }

        /// <summary>
        /// Gets or sets the version of the theme
        /// </summary>
        [StringLength(20)]
        public string Version { get; set; }

        /// <summary>
        /// Gets or sets the author of the theme
        /// </summary>
        [Required]
        [StringLength(100)]
        public string Author { get; set; }

        /// <summary>
        /// Gets or sets the base theme that this theme inherits from
        /// </summary>
        [StringLength(100)]
        public string BaseTheme { get; set; }

        /// <summary>
        /// Gets or sets the accent color in hexadecimal format
        /// </summary>
        [Required]
        [StringLength(7)]
        public string AccentColor { get; set; }

        /// <summary>
        /// Gets or sets the background color in hexadecimal format
        /// </summary>
        [Required]
        [StringLength(7)]
        public string BackgroundColor { get; set; }

        /// <summary>
        /// Gets or sets the text color in hexadecimal format
        /// </summary>
        [Required]
        [StringLength(7)]
        public string TextColor { get; set; }

        /// <summary>
        /// Gets or sets the primary font family
        /// </summary>
        [StringLength(100)]
        public string PrimaryFontFamily { get; set; }

        /// <summary>
        /// Gets or sets the secondary font family
        /// </summary>
        [StringLength(100)]
        public string SecondaryFontFamily { get; set; }

        /// <summary>
        /// Gets or sets the date and time when the theme was created
        /// </summary>
        public DateTime CreatedDate { get; set; }

        /// <summary>
        /// Gets or sets the date and time when the theme was last modified
        /// </summary>
        public DateTime? LastModifiedDate { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether this theme has been deleted (soft delete)
        /// </summary>
        public bool IsDeleted { get; set; }

        /// <summary>
        /// Gets or sets the date and time when the theme was deleted (soft delete)
        /// </summary>
        public DateTime? DeletedDate { get; set; }
    }

    /// <summary>
    /// Represents user preferences for theme settings
    /// </summary>
    [Table("ThemePreferences")]
    public class ThemePreferences
    {
        /// <summary>
        /// Gets or sets the unique identifier of the preferences
        /// </summary>
        [Key]
        public int Id { get; set; }

        /// <summary>
        /// Gets or sets the preferred theme name
        /// </summary>
        [StringLength(100)]
        public string PreferredTheme { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether to use dark mode by default
        /// </summary>
        public bool UseDarkMode { get; set; }

        /// <summary>
        /// Gets or sets the preferred accent color in hexadecimal format
        /// </summary>
        [StringLength(7)]
        public string PreferredAccentColor { get; set; }

        /// <summary>
        /// Gets or sets the date and time when the preferences were last updated
        /// </summary>
        public DateTime LastUpdated { get; set; }
    }

    /// <summary>
    /// Represents information about an available theme
    /// </summary>
    public class ThemeInfo
    {
        /// <summary>
        /// Gets or sets the unique identifier of the theme
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// Gets or sets the name of the theme
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// Gets or sets the description of the theme
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// Gets or sets the version of the theme
        /// </summary>
        public string Version { get; set; }

        /// <summary>
        /// Gets or sets the author of the theme
        /// </summary>
        public string Author { get; set; }
    }

    /// <summary>
    /// Represents the current theme configuration
    /// </summary>
    public class ThemeConfiguration
    {
        /// <summary>
        /// Gets or sets the name of the current theme
        /// </summary>
        public string CurrentThemeName { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether dark mode is enabled
        /// </summary>
        public bool IsDarkModeEnabled { get; set; }

        /// <summary>
        /// Gets or sets the accent color in hexadecimal format
        /// </summary>
        public string AccentColor { get; set; }

        /// <summary>
        /// Gets or sets the background color in hexadecimal format
        /// </summary>
        public string BackgroundColor { get; set; }

        /// <summary>
        /// Gets or sets the text color in hexadecimal format
        /// </summary>
        public string TextColor { get; set; }

        /// <summary>
        /// Gets or sets the date and time when the theme was last changed
        /// </summary>
        public DateTime LastChangedDate { get; set; }
    }
}