using System;
using System.ComponentModel.DataAnnotations;

namespace CakeBistro.Core.Models
{
    public class UserActivity : BaseEntity
    {
        [Required]
        public Guid UserId { get; set; }
        [Required]
        public string ActivityType { get; set; } = string.Empty;
        public string? Description { get; set; }
        public string? IpAddress { get; set; }
        public User? User { get; set; }
    }
}
