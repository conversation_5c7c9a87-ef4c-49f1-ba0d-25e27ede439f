<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:viewModels="clr-namespace:CakeBistro.ViewModels"
             xmlns:models="clr-namespace:CakeBistro.Core.Models"
             x:Class="CakeBistro.Views.InterBranchTransferPage"
             Title="Inter-branch Transfers">
    <ContentPage.BindingContext>
        <viewModels:InterBranchTransferViewModel />
    </ContentPage.BindingContext>
    <VerticalStackLayout Padding="16" Spacing="8">
        <Button Text="Add Transfer"
                Command="{Binding CreateTransferCommand}"
                BackgroundColor="Green"
                TextColor="White"/>
        <Label Text="{Binding StatusMessage}" TextColor="{Binding StatusColor}"/>
        <CollectionView ItemsSource="{Binding Transfers}"
                        SelectionMode="Single"
                        SelectedItem="{Binding SelectedTransfer}">
            <CollectionView.ItemTemplate>
                <DataTemplate x:DataType="models:InterBranchTransfer">
                    <Frame Margin="0,4">
                        <HorizontalStackLayout Spacing="8">
                            <Label Text="{Binding TransferDate, StringFormat='Date: {0:d}'}" />
                            <Label Text="{Binding ReferenceNumber, StringFormat='Ref: {0}'}" />
                            <Label Text="{Binding Status}" />
                            <Button Text="Complete"
                                    Command="{Binding Source={RelativeSource AncestorType={x:Type viewModels:InterBranchTransferViewModel}}, Path=UpdateStatusCommand}"
                                    CommandParameter="{x:Static models:InterBranchTransferStatus.Completed}"
                                    BackgroundColor="Blue"
                                    TextColor="White" />
                        </HorizontalStackLayout>
                    </Frame>
                </DataTemplate>
            </CollectionView.ItemTemplate>
        </CollectionView>
    </VerticalStackLayout>
</ContentPage>
