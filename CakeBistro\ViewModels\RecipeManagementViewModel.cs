using System.Collections.ObjectModel;
using System.Windows.Input;
using CakeBistro.Core.Models;
using CakeBistro.Services;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;

namespace CakeBistro.ViewModels
{
    public partial class RecipeManagementViewModel : ObservableObject
    {
        private readonly IProductionService _productionService;
        public ObservableCollection<Product> Products { get; } = new();
        public ObservableCollection<RawMaterial> Ingredients { get; } = new();
        [ObservableProperty]
        private string recipeName;
        [ObservableProperty]
        private Product selectedProduct;
        [ObservableProperty]
        private RawMaterial selectedIngredient;

        public RecipeManagementViewModel() {}

        public RecipeManagementViewModel(IProductionService productionService)
        {
            _productionService = productionService;
            LoadProducts();
        }

        private async void LoadProducts()
        {
            var products = await _productionService.GetAllProductsAsync();
            Products.Clear();
            foreach (var p in products)
                Products.Add(p);
        }

        [RelayCommand]
        private void AddIngredient()
        {
            if (SelectedIngredient != null && !Ingredients.Contains(SelectedIngredient))
                Ingredients.Add(SelectedIngredient);
        }

        [RelayCommand]
        private async Task SaveRecipe()
        {
            var recipe = new Recipe
            {
                Name = RecipeName,
                ProductId = SelectedProduct?.Id ?? 0,
                Ingredients = Ingredients.ToList()
            };
            await _productionService.CreateRecipeAsync(recipe);
            RecipeName = string.Empty;
            SelectedProduct = null;
            Ingredients.Clear();
        }
    }
}
