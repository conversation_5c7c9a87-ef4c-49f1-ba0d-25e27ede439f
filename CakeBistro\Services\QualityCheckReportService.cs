using System.Text;
using System.Globalization;
using System.IO;
using System.Threading.Tasks;
using System.Collections.Generic;
using QuestPDF.Fluent;
using QuestPDF.Helpers;
using QuestPDF.Infrastructure;
using CakeBistro.Core.Models;
using CakeBistro.Core.Interfaces;

namespace CakeBistro.Services
{
    public interface IQualityCheckReportService
    {
        Task<string> GenerateCsvReportAsync(IEnumerable<QualityCheck> checks, string filePath);
        Task<string> GeneratePdfReportAsync(IEnumerable<QualityCheck> checks, string filePath);
    }

    public class QualityCheckReportService : IQualityCheckReportService
    {
        public async Task<string> GenerateCsvReportAsync(IEnumerable<QualityCheck> checks, string filePath)
        {
            var sb = new StringBuilder();
            sb.AppendLine("Batch,Type,Status,Parameter,Measured,Min,Max,CheckedBy,Date,Notes");
            foreach (var check in checks)
            {
                sb.AppendLine($"{check.ProductionBatchId},{check.Type},{check.Status},{check.Parameter},{check.MeasuredValue},{check.MinValue},{check.MaxValue},{check.CheckedBy},{check.CheckDate.ToString("s", CultureInfo.InvariantCulture)},{check.Notes?.Replace(",",";")}");
            }
            await File.WriteAllTextAsync(filePath, sb.ToString());
            return filePath;
        }

        public async Task<string> GeneratePdfReportAsync(IEnumerable<QualityCheck> checks, string filePath)
        {
            var document = Document.Create(container =>
            {
                container.Page(page =>
                {
                    page.Margin(20);
                    page.Header().Text("Quality Check Report").FontSize(20).Bold();
                    page.Content().Table(table =>
                    {
                        table.ColumnsDefinition(columns =>
                        {
                            columns.RelativeColumn(); // Batch
                            columns.RelativeColumn(); // Type
                            columns.RelativeColumn(); // Status
                            columns.RelativeColumn(); // Parameter
                            columns.RelativeColumn(); // Measured
                            columns.RelativeColumn(); // CheckedBy
                            columns.RelativeColumn(); // Date
                        });
                        table.Header(header =>
                        {
                            header.Cell().Element(CellStyle).Text("Batch");
                            header.Cell().Element(CellStyle).Text("Type");
                            header.Cell().Element(CellStyle).Text("Status");
                            header.Cell().Element(CellStyle).Text("Parameter");
                            header.Cell().Element(CellStyle).Text("Measured");
                            header.Cell().Element(CellStyle).Text("CheckedBy");
                            header.Cell().Element(CellStyle).Text("Date");
                        });
                        foreach (var check in checks)
                        {
                            table.Cell().Element(CellStyle).Text(check.ProductionBatchId.ToString());
                            table.Cell().Element(CellStyle).Text(check.Type);
                            table.Cell().Element(CellStyle).Text(check.Status);
                            table.Cell().Element(CellStyle).Text(check.Parameter);
                            table.Cell().Element(CellStyle).Text(check.MeasuredValue?.ToString() ?? "");
                            table.Cell().Element(CellStyle).Text(check.CheckedBy);
                            table.Cell().Element(CellStyle).Text(check.CheckDate.ToString("g"));
                        }
                    });
                });
            });
            document.GeneratePdf(filePath);
            await Task.CompletedTask;
            return filePath;
        }

        private IContainer CellStyle(IContainer container)
        {
            return container.Padding(2).Border(1).BorderColor(Colors.Grey.Lighten2);
        }
    }
}
