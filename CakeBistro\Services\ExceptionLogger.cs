using Microsoft.Extensions.Logging;
using System;

namespace CakeBistro.Services
{
    public class ExceptionLogger
    {
        private readonly ILogger _logger;

        public ExceptionLogger(ILoggerFactory loggerFactory)
        {
            _logger = loggerFactory.CreateLogger<ExceptionLogger>();
        }

        public void LogException(Exception ex, string methodName)
        {
            _logger.LogError(ex, "An error occurred in method: {MethodName}", methodName);
        }
    }
}