using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using CakeBistro.Core.Models;
using CakeBistro.Data;

namespace CakeBistro.Repositories
{
    // Implementation of raw material document repository with inventory-specific data operations
    public class RawMaterialDocumentRepository : BaseRepository<RawMaterialDocument>, IRawMaterialDocumentRepository
    {
        // Use _context from BaseRepository

        public RawMaterialDocumentRepository(CakeBistro.Data.CakeBistroContext context) : base(context)
        {
            // No need to assign _context, handled by base
        }

        public override async Task<IEnumerable<RawMaterialDocument>> GetAllAsync()
        {
            return await _context.RawMaterialDocuments.ToListAsync();
        }

        public override async Task<RawMaterialDocument?> GetByIdAsync(Guid id)
        {
            return await _context.RawMaterialDocuments.FindAsync(id);
        }

        public override async Task<RawMaterialDocument> AddAsync(RawMaterialDocument document)
        {
            await _context.RawMaterialDocuments.AddAsync(document);
            await _context.SaveChangesAsync();
            return document;
        }

        public override async Task<RawMaterialDocument> UpdateAsync(RawMaterialDocument document)
        {
            _context.RawMaterialDocuments.Update(document);
            await _context.SaveChangesAsync();
            return document;
        }

        public override async Task DeleteAsync(Guid id)
        {
            var document = await _context.RawMaterialDocuments.FindAsync(id);
            if (document != null)
            {
                _context.RawMaterialDocuments.Remove(document);
                await _context.SaveChangesAsync();
            }
        }

        public async Task<IEnumerable<RawMaterialDocument>> GetDocumentsByMaterialAsync(Guid materialId)
        {
            // TODO: Ensure RawMaterialId is of type Guid in RawMaterialDocument model
            return await _context.RawMaterialDocuments
                .Where(d => d.RawMaterialId == materialId)
                .ToListAsync();
        }

        public async Task<IEnumerable<RawMaterialDocument>> GetDocumentsByTypeAsync(string documentType)
        {
            if (string.IsNullOrWhiteSpace(documentType))
            {
                return await _context.RawMaterialDocuments.ToListAsync();
            }

            // ERROR: RawMaterialDocument does not have DocumentType property. Uncomment and fix if you add it to the model.
            // return await _context.RawMaterialDocuments
            //     .Where(d => d.DocumentType.Contains(documentType))
            //     .ToListAsync();
            return new List<RawMaterialDocument>();
        }

        public Task<IEnumerable<RawMaterialDocument>> GetRecentDocumentsAsync(int count)
        {
            // ERROR: RawMaterialDocument does not have UploadDate property. Uncomment and fix if you add it to the model.
            // return await _context.RawMaterialDocuments
            //     .OrderByDescending(d => d.UploadDate)
            //     .Take(count)
            //     .ToListAsync();
            return Task.FromResult<IEnumerable<RawMaterialDocument>>(new List<RawMaterialDocument>());
        }
    }
}
