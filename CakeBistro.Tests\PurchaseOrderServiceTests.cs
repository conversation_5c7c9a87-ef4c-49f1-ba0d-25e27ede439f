using CakeBistro.Core.Models;
using CakeBistro.Services;
using CakeBistro.Repositories;
using Microsoft.EntityFrameworkCore;
using Xunit;

namespace CakeBistro.Tests;

public class PurchaseOrderServiceTests
{
    private readonly CakeBistroContext _context;
    private readonly IPurchaseOrderService _purchaseOrderService;
    private readonly ISupplierRepository _supplierRepository;
    private readonly IRawMaterialRepository _rawMaterialRepository;
    private readonly IInventoryRepository _inventoryRepository;

    public PurchaseOrderServiceTests()
    {
        // Set up in-memory database
        var options = new DbContextOptionsBuilder<CakeBistroContext>()
            .UseInMemoryDatabase(databaseName: "TestDatabase")
            .Options;

        _context = new CakeBistroContext(options);
        
        // Initialize repositories
        _supplierRepository = new SupplierRepository(_context);
        _rawMaterialRepository = new RawMaterialRepository(_context);
        _inventoryRepository = new InventoryRepository(_context);
        
        // Initialize service with test repositories
        _purchaseOrderService = new PurchaseOrderService(
            new BaseRepository<PurchaseOrder>(_context),
            _supplierRepository,
            _rawMaterialRepository);
    }

    [Fact]
    public async Task AddSupplierAsync_ShouldAddSupplier()
    {
        // Arrange
        var supplier = new Supplier
        {
            Name = "Test Supplier",
            ContactPerson = "John Doe",
            Email = "<EMAIL>",
            Phone = "************",
            Address = "123 Test Street"
        };

        // Act
        var result = await _supplierRepository.AddAsync(supplier);
        
        // Assert
        Assert.NotNull(result);
        Assert.Equal("Test Supplier", result.Name);
        Assert.True(result.Id > 0);
    }

    [Fact]
    public async Task AddRawMaterialAsync_ShouldAddMaterial()
    {
        // Arrange
        var material = new RawMaterial
        {
            Name = "Test Material",
            Unit = "kg",
            PricePerUnit = 10.0m,
            CurrentStock = 100,
            MinimumStock = 20
        };

        // Act
        var result = await _rawMaterialRepository.AddAsync(material);
        
        // Assert
        Assert.NotNull(result);
        Assert.Equal("Test Material", result.Name);
        Assert.True(result.Id > 0);
    }

    [Fact]
    public async Task CreatePurchaseOrderAsync_ValidOrder_ShouldCreateOrder()
    {
        // Arrange
        // First create a supplier and raw materials
        var supplier = new Supplier
        {
            Name = "Test Supplier",
            ContactPerson = "John Doe",
            Email = "<EMAIL>",
            Phone = "************",
            Address = "123 Test Street"
        };
        
        var supplierResult = await _supplierRepository.AddAsync(supplier);
        
        var material1 = new RawMaterial
        {
            Name = "Flour",
            Unit = "kg",
            PricePerUnit = 1.0m,
            CurrentStock = 100,
            MinimumStock = 20
        };
        
        var material2 = new RawMaterial
        {
            Name = "Sugar",
            Unit = "kg",
            PricePerUnit = 2.0m,
            CurrentStock = 50,
            MinimumStock = 10
        };
        
        var materialResult1 = await _rawMaterialRepository.AddAsync(material1);
        var materialResult2 = await _rawMaterialRepository.AddAsync(material2);
        
        // Now create the purchase order
        var order = new PurchaseOrder
        {
            SupplierId = supplierResult.Id,
            OrderDate = DateTime.Now,
            Status = "Pending",
            TaxRate = 0.1m,
            Discount = 0.05m,
            FuelCost = 5.0m,
            Items = new List<PurchaseOrderItem>
            {
                new PurchaseOrderItem { RawMaterialId = materialResult1.Id, Quantity = 2, UnitPrice = 1.0m },
                new PurchaseOrderItem { RawMaterialId = materialResult2.Id, Quantity = 1, UnitPrice = 2.0m }
            }
        };

        // Act
        var result = await _purchaseOrderService.CreatePurchaseOrderAsync(order);
        
        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.Items.Count);
        Assert.Equal(39.0m, result.TotalAmount); // (2*1 + 1*2) * 1.1 (tax) - 5% discount + 5 fuel cost = 39.0
        Assert.Equal("Pending", result.Status);
        
        // Verify that items were saved correctly
        Assert.Equal(2, result.Items[0].Quantity);
        Assert.Equal(1.0m, result.Items[0].UnitPrice);
        Assert.Equal(1, result.Items[1].Quantity);
        Assert.Equal(2.0m, result.Items[1].UnitPrice);
    }

    [Fact]
    public async Task CreatePurchaseOrderAsync_WithInvalidData_ShouldThrowException()
    {
        // Arrange
        var order = new PurchaseOrder(); // No supplier ID or items
        
        // Act & Assert
        var exception = await Assert.ThrowsAsync<ArgumentException>(async () => 
            await _purchaseOrderService.CreatePurchaseOrderAsync(order));
        
        Assert.Contains("Valid supplier ID is required", exception.Message);
    }

    [Fact]
    public async Task GetPurchaseOrderByIdAsync_ShouldReturnOrder()
    {
        // Arrange
        // First create a supplier and raw materials
        var supplier = new Supplier
        {
            Name = "Test Supplier",
            ContactPerson = "John Doe",
            Email = "<EMAIL>",
            Phone = "************",
            Address = "123 Test Street"
        };
        
        var supplierResult = await _supplierRepository.AddAsync(supplier);
        
        var material1 = new RawMaterial
        {
            Name = "Flour",
            Unit = "kg",
            PricePerUnit = 1.0m,
            CurrentStock = 100,
            MinimumStock = 20
        };
        
        var materialResult1 = await _rawMaterialRepository.AddAsync(material1);
        
        // Create an order
        var order = new PurchaseOrder
        {
            SupplierId = supplierResult.Id,
            OrderDate = DateTime.Now,
            Status = "Pending",
            TaxRate = 0.1m,
            Discount = 0.05m,
            FuelCost = 5.0m,
            Items = new List<PurchaseOrderItem>
            {
                new PurchaseOrderItem { RawMaterialId = materialResult1.Id, Quantity = 2, UnitPrice = 1.0m }
            }
        };
        
        var createdOrder = await _purchaseOrderService.CreatePurchaseOrderAsync(order);
        
        // Act
        var result = await _purchaseOrderService.GetPurchaseOrderByIdAsync(createdOrder.Id);
        
        // Assert
        Assert.NotNull(result);
        Assert.Equal(createdOrder.Id, result.Id);
        Assert.Equal(supplierResult.Id, result.SupplierId);
        Assert.Equal(2, result.Items.Count());
        Assert.Equal(1.0m, result.Items.First().UnitPrice);
    }

    [Fact]
    public async Task SubmitPurchaseOrderAsync_ShouldUpdateStatus()
    {
        // Arrange
        // First create a supplier and raw materials
        var supplier = new Supplier
        {
            Name = "Test Supplier",
            ContactPerson = "John Doe",
            Email = "<EMAIL>",
            Phone = "************",
            Address = "123 Test Street"
        };
        
        var supplierResult = await _supplierRepository.AddAsync(supplier);
        
        var material1 = new RawMaterial
        {
            Name = "Flour",
            Unit = "kg",
            PricePerUnit = 1.0m,
            CurrentStock = 100,
            MinimumStock = 20
        };
        
        var materialResult1 = await _rawMaterialRepository.AddAsync(material1);
        
        // Create an order
        var order = new PurchaseOrder
        {
            SupplierId = supplierResult.Id,
            OrderDate = DateTime.Now,
            Status = "Pending",
            TaxRate = 0.1m,
            Discount = 0.05m,
            FuelCost = 5.0m,
            Items = new List<PurchaseOrderItem>
            {
                new PurchaseOrderItem { RawMaterialId = materialResult1.Id, Quantity = 2, UnitPrice = 1.0m }
            }
        };
        
        var createdOrder = await _purchaseOrderService.CreatePurchaseOrderAsync(order);
        
        // Act
        var updatedOrder = await _purchaseOrderService.SubmitPurchaseOrderAsync(createdOrder.Id);
        
        // Assert
        Assert.NotNull(updatedOrder);
        Assert.Equal(createdOrder.Id, updatedOrder.Id);
        Assert.Equal("Submitted", updatedOrder.Status);
        Assert.NotNull(updatedOrder.SubmittedDate);
    }

    [Fact]
    public async Task ReceivePurchaseOrderAsync_ShouldUpdateStatusAndInventory()
    {
        // Arrange
        // First create a supplier and raw materials
        var supplier = new Supplier
        {
            Name = "Test Supplier",
            ContactPerson = "John Doe",
            Email = "<EMAIL>",
            Phone = "************",
            Address = "123 Test Street"
        };
        
        var supplierResult = await _supplierRepository.AddAsync(supplier);
        
        var material1 = new RawMaterial
        {
            Name = "Flour",
            Unit = "kg",
            PricePerUnit = 1.0m,
            CurrentStock = 100,
            MinimumStock = 20
        };
        
        var materialResult1 = await _rawMaterialRepository.AddAsync(material1);
        
        // Create an order
        var order = new PurchaseOrder
        {
            SupplierId = supplierResult.Id,
            OrderDate = DateTime.Now,
            Status = "Pending",
            TaxRate = 0.1m,
            Discount = 0.05m,
            FuelCost = 5.0m,
            Items = new List<PurchaseOrderItem>
            {
                new PurchaseOrderItem { RawMaterialId = materialResult1.Id, Quantity = 2, UnitPrice = 1.0m }
            }
        };
        
        var createdOrder = await _purchaseOrderService.CreatePurchaseOrderAsync(order);
        
        // Submit the order first
        var submittedOrder = await _purchaseOrderService.SubmitPurchaseOrderAsync(createdOrder.Id);
        
        // Act
        var receivedOrder = await _purchaseOrderService.ReceivePurchaseOrderAsync(createdOrder.Id);
        
        // Assert
        Assert.NotNull(receivedOrder);
        Assert.Equal(createdOrder.Id, receivedOrder.Id);
        Assert.Equal("Received", receivedOrder.Status);
        Assert.NotNull(receivedOrder.ReceivedDate);
        
        // Verify inventory was updated
        var updatedMaterial = await _rawMaterialRepository.GetByIdAsync(materialResult1.Id);
        Assert.Equal(102, updatedMaterial.CurrentStock); // 100 + 2 from the order
    }

    [Fact]
    public async Task DeletePurchaseOrderAsync_ShouldRemoveOrder()
    {
        // Arrange
        // First create a supplier and raw materials
        var supplier = new Supplier
        {
            Name = "Test Supplier",
            ContactPerson = "John Doe",
            Email = "<EMAIL>",
            Phone = "************",
            Address = "123 Test Street"
        };
        
        var supplierResult = await _supplierRepository.AddAsync(supplier);
        
        var material1 = new RawMaterial
        {
            Name = "Flour",
            Unit = "kg",
            PricePerUnit = 1.0m,
            CurrentStock = 100,
            MinimumStock = 20
        };
        
        var materialResult1 = await _rawMaterialRepository.AddAsync(material1);
        
        // Create an order
        var order = new PurchaseOrder
        {
            SupplierId = supplierResult.Id,
            OrderDate = DateTime.Now,
            Status = "Pending",
            TaxRate = 0.1m,
            Discount = 0.05m,
            FuelCost = 5.0m,
            Items = new List<PurchaseOrderItem>
            {
                new PurchaseOrderItem { RawMaterialId = materialResult1.Id, Quantity = 2, UnitPrice = 1.0m }
            }
        };
        
        var createdOrder = await _purchaseOrderService.CreatePurchaseOrderAsync(order);
        
        // Act
        var deleteResult = await _purchaseOrderService.DeletePurchaseOrderAsync(createdOrder.Id);
        
        // Assert
        Assert.True(deleteResult);
        
        // Try to get deleted order
        var result = await ((IPurchaseOrderRepository)_purchaseOrderService).GetByIdAsync(createdOrder.Id);
        Assert.Null(result);
    }

    [Fact]
    public async Task ReceivePurchaseOrderAsync_WithInsufficientStock_ShouldThrowException()
    {
        // Arrange
        // First create a supplier and raw materials
        var supplier = new Supplier
        {
            Name = "Test Supplier",
            ContactPerson = "John Doe",
            Email = "<EMAIL>",
            Phone = "************",
            Address = "123 Test Street"
        };
        
        var supplierResult = await _supplierRepository.AddAsync(supplier);
        
        var material1 = new RawMaterial
        {
            Name = "Flour",
            Unit = "kg",
            PricePerUnit = 1.0m,
            CurrentStock = 100,
            MinimumStock = 20
        };
        
        var materialResult1 = await _rawMaterialRepository.AddAsync(material1);
        
        // Create an order
        var order = new PurchaseOrder
        {
            SupplierId = supplierResult.Id,
            OrderDate = DateTime.Now,
            Status = "Pending",
            TaxRate = 0.1m,
            Discount = 0.05m,
            FuelCost = 5.0m,
            Items = new List<PurchaseOrderItem>
            {
                new PurchaseOrderItem { RawMaterialId = materialResult1.Id, Quantity = 2, UnitPrice = 1.0m }
            }
        };
        
        var createdOrder = await _purchaseOrderService.CreatePurchaseOrderAsync(order);
        
        // Submit the order first
        var submittedOrder = await _purchaseOrderService.SubmitPurchaseOrderAsync(createdOrder.Id);
        
        // Act & Assert
        // Should fail because we haven't implemented the actual receiving logic yet
        var exception = await Assert.ThrowsAsync<ApplicationException>(async () => 
            await _purchaseOrderService.ReceivePurchaseOrderAsync(createdOrder.Id));
        
        Assert.Contains("NotImplementedException", exception.Message);
    }

    [Fact]
    public async Task CreatePurchaseOrderAsync_WithInvalidSupplier_ShouldThrowException()
    {
        // Arrange
        // Use an invalid supplier ID that doesn't exist
        var order = new PurchaseOrder
        {
            SupplierId = 999, // Non-existent supplier
            OrderDate = DateTime.Now,
            Status = "Pending",
            TaxRate = 0.1m,
            Discount = 0.05m,
            FuelCost = 5.0m,
            Items = new List<PurchaseOrderItem>
            {
                new PurchaseOrderItem { RawMaterialId = 1, Quantity = 2, UnitPrice = 1.0m }
            }
        };
        
        // Act & Assert
        var exception = await Assert.ThrowsAsync<ArgumentException>(async () => 
            await _purchaseOrderService.CreatePurchaseOrderAsync(order));
        
        Assert.Contains("Supplier with ID 999 not found", exception.Message);
    }

    [Fact]
    public async Task CreatePurchaseOrderAsync_WithInvalidRawMaterial_ShouldThrowException()
    {
        // Arrange
        // First create a supplier
        var supplier = new Supplier
        {
            Name = "Test Supplier",
            ContactPerson = "John Doe",
            Email = "<EMAIL>",
            Phone = "************",
            Address = "123 Test Street"
        };
        
        var supplierResult = await _supplierRepository.AddAsync(supplier);
        
        // Create an order with invalid raw material
        var order = new PurchaseOrder
        {
            SupplierId = supplierResult.Id,
            OrderDate = DateTime.Now,
            Status = "Pending",
            TaxRate = 0.1m,
            Discount = 0.05m,
            FuelCost = 5.0m,
            Items = new List<PurchaseOrderItem>
            {
                new PurchaseOrderItem { RawMaterialId = 999, Quantity = 2, UnitPrice = 1.0m } // Invalid material ID
            }
        };
        
        // Act & Assert
        var exception = await Assert.ThrowsAsync<ArgumentException>(async () => 
            await _purchaseOrderService.CreatePurchaseOrderAsync(order));
        
        Assert.Contains("Raw material with ID 999 not found", exception.Message);
    }

    [Fact]
    public async Task CreatePurchaseOrderAsync_WithNegativeQuantity_ShouldThrowException()
    {
        // Arrange
        // First create a supplier and raw materials
        var supplier = new Supplier
        {
            Name = "Test Supplier",
            ContactPerson = "John Doe",
            Email = "<EMAIL>",
            Phone = "************",
            Address = "123 Test Street"
        };
        
        var supplierResult = await _supplierRepository.AddAsync(supplier);
        
        var material1 = new RawMaterial
        {
            Name = "Flour",
            Unit = "kg",
            PricePerUnit = 1.0m,
            CurrentStock = 100,
            MinimumStock = 20
        };
        
        var materialResult1 = await _rawMaterialRepository.AddAsync(material1);
        
        // Create an order with negative quantity
        var order = new PurchaseOrder
        {
            SupplierId = supplierResult.Id,
            OrderDate = DateTime.Now,
            Status = "Pending",
            TaxRate = 0.1m,
            Discount = 0.05m,
            FuelCost = 5.0m,
            Items = new List<PurchaseOrderItem>
            {
                new PurchaseOrderItem { RawMaterialId = materialResult1.Id, Quantity = -1, UnitPrice = 1.0m } // Negative quantity
            }
        };
        
        // Act & Assert
        var exception = await Assert.ThrowsAsync<ArgumentException>(async () => 
            await _purchaseOrderService.CreatePurchaseOrderAsync(order));
        
        Assert.Contains("Quantity must be greater than zero for all items", exception.Message);
    }

    [Fact]
    public async Task ReceivePurchaseOrderAsync_WithInvalidStatus_ShouldThrowException()
    {
        // Arrange
        // First create a supplier and raw materials
        var supplier = new Supplier
        {
            Name = "Test Supplier",
            ContactPerson = "John Doe",
            Email = "<EMAIL>",
            Phone = "************",
            Address = "123 Test Street"
        };
        
        var supplierResult = await _supplierRepository.AddAsync(supplier);
        
        var material1 = new RawMaterial
        {
            Name = "Flour",
            Unit = "kg",
            PricePerUnit = 1.0m,
            CurrentStock = 100,
            MinimumStock = 20
        };
        
        var materialResult1 = await _rawMaterialRepository.AddAsync(material1);
        
        // Create an order
        var order = new PurchaseOrder
        {
            SupplierId = supplierResult.Id,
            OrderDate = DateTime.Now,
            Status = "Pending",
            TaxRate = 0.1m,
            Discount = 0.05m,
            FuelCost = 5.0m,
            Items = new List<PurchaseOrderItem>
            {
                new PurchaseOrderItem { RawMaterialId = materialResult1.Id, Quantity = 2, UnitPrice = 1.0m }
            }
        };
        
        var createdOrder = await _purchaseOrderService.CreatePurchaseOrderAsync(order);
        
        // Act & Assert
        // Try to receive without submitting first
        var exception = await Assert.ThrowsAsync<InvalidOperationException>(async () => 
            await _purchaseOrderService.ReceivePurchaseOrderAsync(createdOrder.Id));
        
        Assert.Contains("Cannot receive purchase order that is in 'Pending' status", exception.Message);
    }

    [Fact]
    public async Task DeletePurchaseOrderAsync_WithNonPendingStatus_ShouldThrowException()
    {
        // Arrange
        // First create a supplier and raw materials
        var supplier = new Supplier
        {
            Name = "Test Supplier",
            ContactPerson = "John Doe",
            Email = "<EMAIL>",
            Phone = "************",
            Address = "123 Test Street"
        };
        
        var supplierResult = await _supplierRepository.AddAsync(supplier);
        
        var material1 = new RawMaterial
        {
            Name = "Flour",
            Unit = "kg",
            PricePerUnit = 1.0m,
            CurrentStock = 100,
            MinimumStock = 20
        };
        
        var materialResult1 = await _rawMaterialRepository.AddAsync(material1);
        
        // Create an order
        var order = new PurchaseOrder
        {
            SupplierId = supplierResult.Id,
            OrderDate = DateTime.Now,
            Status = "Pending",
            TaxRate = 0.1m,
            Discount = 0.05m,
            FuelCost = 5.0m,
            Items = new List<PurchaseOrderItem>
            {
                new PurchaseOrderItem { RawMaterialId = materialResult1.Id, Quantity = 2, UnitPrice = 1.0m }
            }
        };
        
        var createdOrder = await _purchaseOrderService.CreatePurchaseOrderAsync(order);
        
        // Submit the order
        var submittedOrder = await _purchaseOrderService.SubmitPurchaseOrderAsync(createdOrder.Id);
        
        // Act & Assert
        var exception = await Assert.ThrowsAsync<InvalidOperationException>(async () => 
            await _purchaseOrderService.DeletePurchaseOrderAsync(createdOrder.Id));
        
        Assert.Contains("Cannot delete purchase order that is in 'Submitted' status", exception.Message);
    }

    [Fact]
    public async Task CreatePurchaseOrderAsync_WithZeroQuantity_ShouldThrowException()
    {
        // Arrange
        // First create a supplier and raw materials
        var supplier = new Supplier
        {
            Name = "Test Supplier",
            ContactPerson = "John Doe",
            Email = "<EMAIL>",
            Phone = "************",
            Address = "123 Test Street"
        };
        
        var supplierResult = await _supplierRepository.AddAsync(supplier);
        
        var material1 = new RawMaterial
        {
            Name = "Flour",
            Unit = "kg",
            PricePerUnit = 1.0m,
            CurrentStock = 100,
            MinimumStock = 20
        };
        
        var materialResult1 = await _rawMaterialRepository.AddAsync(material1);
        
        // Create an order with zero quantity
        var order = new PurchaseOrder
        {
            SupplierId = supplierResult.Id,
            OrderDate = DateTime.Now,
            Status = "Pending",
            TaxRate = 0.1m,
            Discount = 0.05m,
            FuelCost = 5.0m,
            Items = new List<PurchaseOrderItem>
            {
                new PurchaseOrderItem { RawMaterialId = materialResult1.Id, Quantity = 0, UnitPrice = 1.0m } // Zero quantity
            }
        };
        
        // Act & Assert
        var exception = await Assert.ThrowsAsync<ArgumentException>(async () => 
            await _purchaseOrderService.CreatePurchaseOrderAsync(order));
        
        Assert.Contains("Quantity must be greater than zero for all items", exception.Message);
    }

    [Fact]
    public async Task CreatePurchaseOrderAsync_WithNegativeUnitPrice_ShouldThrowException()
    {
        // Arrange
        // First create a supplier and raw materials
        var supplier = new Supplier
        {
            Name = "Test Supplier",
            ContactPerson = "John Doe",
            Email = "<EMAIL>",
            Phone = "************",
            Address = "123 Test Street"
        };
        
        var supplierResult = await _supplierRepository.AddAsync(supplier);
        
        var material1 = new RawMaterial
        {
            Name = "Flour",
            Unit = "kg",
            PricePerUnit = 1.0m,
            CurrentStock = 100,
            MinimumStock = 20
        };
        
        var materialResult1 = await _rawMaterialRepository.AddAsync(material1);
        
        // Create an order with negative unit price
        var order = new PurchaseOrder
        {
            SupplierId = supplierResult.Id,
            OrderDate = DateTime.Now,
            Status = "Pending",
            TaxRate = 0.1m,
            Discount = 0.05m,
            FuelCost = 5.0m,
            Items = new List<PurchaseOrderItem>
            {
                new PurchaseOrderItem { RawMaterialId = materialResult1.Id, Quantity = 2, UnitPrice = -1.0m } // Negative unit price
            }
        };
        
        // Act & Assert
        var exception = await Assert.ThrowsAsync<ArgumentException>(async () => 
            await _purchaseOrderService.CreatePurchaseOrderAsync(order));
        
        Assert.Contains("Unit price cannot be negative", exception.Message);
    }

    [Fact]
    public async Task SubmitPurchaseOrderAsync_WithInvalidId_ShouldThrowException()
    {
        // Arrange
        int nonExistentId = 999;
        
        // Act & Assert
        var exception = await Assert.ThrowsAsync<KeyNotFoundException>(async () => 
            await _purchaseOrderService.SubmitPurchaseOrderAsync(nonExistentId));
        
        Assert.Contains("Purchase order with ID 999 not found", exception.Message);
    }

    [Fact]
    public async Task ReceivePurchaseOrderAsync_WithInvalidId_ShouldThrowException()
    {
        // Arrange
        int nonExistentId = 999;
        
        // Act & Assert
        var exception = await Assert.ThrowsAsync<KeyNotFoundException>(async () => 
            await _purchaseOrderService.ReceivePurchaseOrderAsync(nonExistentId));
        
        Assert.Contains("Purchase order with ID 999 not found", exception.Message);
    }
}