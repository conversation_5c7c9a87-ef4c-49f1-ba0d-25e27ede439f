
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using CakeBistro.Models;
using Microsoft.EntityFrameworkCore;

namespace CakeBistro.Services
{
    /// <summary>
    /// Implementation of IDocumentService interface to handle document management operations.
    /// Implements PRD requirements for document management, organization, and recovery.
    /// </summary>
    public class DocumentService : BaseService<Document>, IDocumentService
    {
        private readonly CakeBistroContext _context;

        /// <summary>
        /// Initializes a new instance of the <see cref="DocumentService"/> class.
        /// </summary>
        /// <param name="context">The database context to use for document operations</param>
        public DocumentService(CakeBistroContext context) : base(new Repository<Document>(context))
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
        }

        /// <inheritdoc />
        public async Task<Document> UploadDocumentAsync(DocumentUpload documentUpload)
        {
            // Validate input parameters using base class helpers
            CheckEntityExists(documentUpload, "DocumentUploadNullCheck", "Document upload cannot be null");
            ValidateId(documentUpload.ProductId, "InvalidProductID", "Valid product ID is required for document upload");
            
            if (documentUpload.FileStream == null || documentUpload.FileStream.Length == 0)
                throw new BusinessRuleValidationException("FileStreamRequired", "File stream cannot be null or empty");

            if (string.IsNullOrEmpty(documentUpload.DocumentType))
                throw new BusinessRuleValidationException("DocumentTypeRequired", "Document type must be specified");

            // Generate unique file name
            string fileName = $"{Guid.NewGuid()}{Path.GetExtension(documentUpload.Name)}";
            
            // Create document record
            var document = new Document
            {
                ProductId = documentUpload.ProductId,
                Name = documentUpload.Name,
                Description = documentUpload.Description,
                DocumentType = documentUpload.DocumentType,
                ContentType = documentUpload.ContentType,
                FileName = fileName,
                Size = documentUpload.FileStream.Length,
                IsDeleted = false,
                CreatedDate = DateTime.UtcNow
            };

            // Save document content to storage (simplified - in real implementation would use blob storage)
            var filePath = GetDocumentFilePath(fileName);
            
            // Ensure directory exists
            var directory = Path.GetDirectoryName(filePath);
            if (!Directory.Exists(directory))
                Directory.CreateDirectory(directory);

            // Save the file stream to disk
            using (var fileStream = File.Create(filePath))
            {
                await documentUpload.FileStream.CopyToAsync(fileStream);
            }

            // Add document to database
            _context.Documents.Add(document);
            await _context.SaveChangesAsync();
            
            return document;
        }

        /// <inheritdoc />
        public async Task<List<Document>> GetDocumentsByProductAsync(int productId, string documentType = null)
        {
            // Validate input parameters using base class helpers
            ValidateId(productId, "InvalidProductID", "Valid product ID is required for retrieving documents");

            // Build query
            var query = _context.Documents
                .Where(d => d.ProductId == productId && !d.IsDeleted);

            // Filter by document type if specified
            if (!string.IsNullOrEmpty(documentType))
                query = query.Where(d => d.DocumentType == documentType);

            // Execute query
            return await query.ToListAsync();
        }

        /// <inheritdoc />
        public async Task<bool> DeleteDocumentAsync(int documentId)
        {
            // Validate input parameters using base class helpers
            ValidateId(documentId, "InvalidDocumentID", "Valid document ID is required for deletion");

            // Find the document
            var document = await _context.Documents.FindAsync(documentId);

            CheckEntityExists(document, "DocumentNotFound", $"Document not found: {documentId}");

            // Perform soft delete
            document.IsDeleted = true;
            document.DeletedDate = DateTime.UtcNow;
            
            await _context.SaveChangesAsync();
            
            return true;
        }

        /// <inheritdoc />
        public async Task<bool> RestoreDocumentAsync(int documentId)
        {
            // Validate input parameters using base class helpers
            ValidateId(documentId, "InvalidDocumentID", "Valid document ID is required for restoration");

            // Find the document
            var document = await _context.Documents.FindAsync(documentId);

            CheckEntityExists(document, "DocumentNotFound", $"Document not found: {documentId}");

            // Restore from soft delete
            document.IsDeleted = false;
            document.RestoredDate = DateTime.UtcNow;
            
            await _context.SaveChangesAsync();
            
            return true;
        }

        /// <inheritdoc />
        public async Task<Stream> DownloadDocumentAsync(int documentId)
        {
            // Validate input parameters using base class helpers
            ValidateId(documentId, "InvalidDocumentID", "Valid document ID is required for download");

            // Find the document
            var document = await _context.Documents.FindAsync(documentId);

            CheckEntityExists(document, "DocumentNotFound", $"Document not found: {documentId}");

            // Check if document is deleted
            if (document.IsDeleted)
                throw new BusinessRuleValidationException("DocumentDeleted", $"Document has been deleted: {documentId}");

            // Read the file stream from storage
            var filePath = GetDocumentFilePath(document.FileName);
            
            if (!File.Exists(filePath))
                throw new BusinessRuleValidationException("FileNotFound", $"Document file not found: {document.FileName}");

            // Return file stream
            return File.OpenRead(filePath);
        }

        /// <summary>
        /// Gets the file path for storing a document
        /// </summary>
        /// <param name="fileName">The name of the file</param>
        /// <returns>The full file path</returns>
        private string GetDocumentFilePath(string fileName)
        {
            // In production, this would point to a blob storage or dedicated file server
            // For development, we'll store files in a local folder
            return Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "documents", fileName);
        }
    }
}