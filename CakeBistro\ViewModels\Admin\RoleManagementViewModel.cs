using CakeBistro.Core.Models;
using CakeBistro.Services;
using System.Collections.ObjectModel;
using System.Windows.Input;
using Microsoft.Maui.Controls;

namespace CakeBistro.ViewModels.Admin
{
    public class RoleManagementViewModel : BindableObject
    {
        private readonly IAuthenticationService? _authService;
        public ObservableCollection<Role> Roles { get; set; } = new();
        public ICommand AddRoleCommand { get; }
        public ICommand EditRoleCommand { get; }
        public ICommand DeleteRoleCommand { get; }

        public RoleManagementViewModel()
        {
            _authService = Application.Current?.Handler?.MauiContext?.Services.GetService(typeof(IAuthenticationService)) as IAuthenticationService;
            AddRoleCommand = new Command(OnAddRole);
            EditRoleCommand = new Command<Role>(OnEditRole);
            DeleteRoleCommand = new Command<Role>(OnDeleteRole);
            LoadRoles();
        }

        private async void LoadRoles()
        {
            if (_authService != null)
            {
                var roles = await _authService.GetAllRolesAsync();
                Roles.Clear();
                foreach (var role in roles)
                    Roles.Add(role);
            }
        }

        private void OnAddRole()
        {
            // TODO: Show add role dialog
        }
        private void OnEditRole(Role role)
        {
            // TODO: Show edit role dialog
        }
        private async void OnDeleteRole(Role role)
        {
            if (_authService != null && role != null)
            {
                await _authService.DeleteRoleAsync(role.Id);
                LoadRoles();
            }
        }
    }
}
