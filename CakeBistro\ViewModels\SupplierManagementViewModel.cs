using System.Collections.ObjectModel;
using System.Windows.Input;
using CakeBistro.Core.Models;
using CakeBistro.Services;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Microsoft.UI.Xaml.Media;

namespace CakeBistro.ViewModels
{
    public partial class SupplierManagementViewModel : ObservableObject
    {
        private readonly ISupplierService _supplierService;
        [ObservableProperty]
        private string supplierName;
        [ObservableProperty]
        private string accountCode;
        public ObservableCollection<Supplier> Suppliers { get; } = new();
        private Color _statusColor = Colors.Transparent;

        public Color StatusColor
        {
            get => _statusColor;
            set => SetProperty(ref _statusColor, value);
        }

        public SupplierManagementViewModel(ISupplierService supplierService)
        {
            _supplierService = supplierService;
            LoadSuppliers();
        }

        private async void LoadSuppliers()
        {
            var suppliers = await _supplierService.GetAllSuppliersAsync();
            Suppliers.Clear();
            foreach (var s in suppliers)
                Suppliers.Add(s);
        }

        [RelayCommand]
        private async Task AddSupplier()
        {
            var supplier = new Supplier
            {
                Name = SupplierName,
                AccountCode = AccountCode
            };
            try
            {
                await _supplierService.CreateSupplierAsync(supplier);
                SupplierName = string.Empty;
                AccountCode = string.Empty;
                LoadSuppliers();
                StatusMessage = "Supplier added successfully.";
                StatusColor = Color.FromArgb("#388E3C");
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error: {ex.Message}";
                StatusColor = Color.FromArgb("#D32F2F");
            }
        }
    }
}
