﻿using CakeBistro.Data;
using CakeBistro.Services;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;

namespace CakeBistro
{
    public partial class App : Application
    {
        public App()
        {
            try
            {
                InitializeComponent();
                MainPage = new AppShell(); // Use parameterless constructor
                // Async initialization will be triggered after construction
                InitializeAsync();
            }
            catch (Exception ex)
            {
                // Log to file or fallback if logger is not available
                try
                {
                    var logger = MauiProgram.ServiceProvider?.GetService<ILogger<App>>();
                    logger?.LogCritical(ex, "Critical error during App startup: {Message}", ex.Message);
                }
                catch { }
                // Optionally show a fallback UI or message
                MainPage = new ContentPage { Content = new Label { Text = $"Startup error: {ex.Message}" } };
            }
        }

        private async void InitializeAsync()
        {
            var serviceProvider = MauiProgram.ServiceProvider;
            var logger = serviceProvider.GetService<ILogger<App>>();

            try
            {
                // Register global exception handler
                var exceptionHandler = serviceProvider.GetService<GlobalExceptionHandler>();
                exceptionHandler?.RegisterHandlers();

                // Ensure main database is created
                try
                {
                    var db = serviceProvider.GetService<CakeBistroContext>();
                    if (db != null)
                        await db.Database.EnsureCreatedAsync();
                    else
                        logger?.LogError("CakeBistroContext service is missing or not registered.");
                }
                catch (Exception ex)
                {
                    logger?.LogError(ex, "Error ensuring main database: {Message}", ex.Message);
                }

                // Ensure identity database is created (if present)
                try
                {
                    var identityContext = serviceProvider.GetService<Models.Identity.CakeBistroIdentityContext>();
                    if (identityContext != null)
                        await identityContext.Database.EnsureCreatedAsync();
                    else
                        logger?.LogWarning("CakeBistroIdentityContext service is missing or not registered.");
                }
                catch (Exception ex)
                {
                    logger?.LogError(ex, "Error ensuring identity database: {Message}", ex.Message);
                }
            }
            catch (Exception ex)
            {
                logger?.LogCritical(ex, "Critical error during async initialization: {Message}", ex.Message);
                MainPage = new ContentPage { Content = new Label { Text = $"Async init error: {ex.Message}" } };
            }
        }
    }
}