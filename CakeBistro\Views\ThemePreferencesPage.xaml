
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="CakeBistro.Views.ThemePreferencesPage"
             Title="Theme Preferences">
    <ScrollView>
        <StackLayout Margin="20">
            <Label Text="Current Theme" FontSize="Large" HorizontalOptions="Start"/>
            
            <Picker ItemsSource="{Binding AvailableThemes}"
                      SelectedItem="{Binding CurrentTheme, Mode=TwoWay}"
                      ItemDisplayBinding="{Binding Name}"
                      SelectedIndexChanged="OnThemeSelected"/>
            
            <Label Text="Theme Customization" FontSize="Large" Margin="0,30,0,10" HorizontalOptions="Start"/>
            
            <Label Text="Preferred Theme:" HorizontalOptions="Start"/>
            <Entry Text="{Binding ThemePreferences.PreferredTheme, Mode=TwoWay}" Placeholder="Enter preferred theme name"/>
            
            <Label Text="Use Dark Mode" Margin="0,10,0,0" HorizontalOptions="Start"/>
            <Switch IsToggled="{Binding ThemePreferences.UseDarkMode, Mode=TwoWay}" Toggled="OnPreferenceChanged"/>
            
            <Label Text="Preferred Accent Color:" Margin="0,10,0,0" HorizontalOptions="Start"/>
            <Entry Text="{Binding ThemePreferences.PreferredAccentColor, Mode=TwoWay}" Placeholder="Enter accent color in hex format (e.g. #FF6F00)"/>
            
            <Label Text="Font Families" FontSize="Large" Margin="0,30,0,10" HorizontalOptions="Start"/>
            
            <Label Text="Primary Font Family:" HorizontalOptions="Start"/>
            <Entry Text="{Binding ThemePreferences.PrimaryFontFamily, Mode=TwoWay}" Placeholder="Enter primary font family (Segoe UI)"/>
            
            <Label Text="Secondary Font Family:" Margin="0,10,0,0" HorizontalOptions="Start"/>
            <Entry Text="{Binding ThemePreferences.SecondaryFontFamily, Mode=TwoWay}" Placeholder="Enter secondary font family (Arial)"/>
            
            <Button Text="Save Preferences" Clicked="OnPreferenceChanged" Margin="0,20,0,0"/>
            
            <Label Text="Reset Preferences" FontSize="Large" Margin="0,30,0,10" HorizontalOptions="Start"/>
            
            <Button Text="Reset to Default" Clicked="OnResetPreferencesClicked"/>
            
        </StackLayout>
    </ScrollView>
</ContentPage>