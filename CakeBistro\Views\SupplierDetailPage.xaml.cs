using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using CakeBistro.Core.Models;
using CakeBistro.Repositories;
using CakeBistro.Services;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace CakeBistro.Views
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class SupplierDetailPage : BasePage
    {
        private SupplierDetailViewModel _viewModel;
        private readonly IInventoryService _inventoryService;
        private readonly ILogger<SupplierDetailPage> _logger;
        
        public SupplierDetailPage(IInventoryService inventoryService, ILogger<SupplierDetailPage> logger)
        {
            InitializeComponent();
            
            _logger = logger;
            // Initialize the view model
            _inventoryService = inventoryService;
            _viewModel = new SupplierDetailViewModel(inventoryService);
            BindingContext = _viewModel;
            
            // Subscribe to go back event
            MessagingCenter.Subscribe<SupplierDetailViewModel>(this, "GoBack", (sender) =>
            {
                Navigation.PopAsync();
            });
            
            // Subscribe to save event
            MessagingCenter.Subscribe<SupplierDetailViewModel>(this, "SupplierSaved", async (sender) =>
            {
                await Navigation.PopAsync();
            });
            
            // Subscribe to discard event
            MessagingCenter.Subscribe<SupplierDetailViewModel>(this, "SupplierDiscarded", async (sender) =>
            {
                await Navigation.PopAsync();
            });
            
            // Subscribe to navigate to inventory event
            MessagingCenter.Subscribe<SupplierDetailViewModel>(this, "NavigateToInventory", async (sender) =>
            {
                // Navigate to inventory page
                await Navigation.PushAsync(new InventoryPage(inventoryService));
            });
        }
        
        // Handle appearing of the page
        protected override async void OnAppearing()
        {
            base.OnAppearing();
            
            try
            {
                // Get CakeBistro.Core.Models.Supplier ID from navigation parameter
                if (Navigation.NavigationStack.Count > 1)
                {
                    var previousPage = Navigation.NavigationStack[Navigation.NavigationStack.Count - 2];
                    if (previousPage is SupplierPage supplierPage &&
                        supplierPage.BindingContext is SupplierViewModel supplierViewModel)
                    {
                        // Check if we have a selected CakeBistro.Core.Models.Supplier
                        var selectedItem = supplierViewModel.Suppliers.FirstOrDefault();
                        if (selectedItem != null)
                        {
                            await _viewModel.InitializeAsync(selectedItem.Id);
                        }
                        else
                        {
                            _logger.LogWarning("No supplier selected when initializing SupplierDetailPage");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in OnAppearing: {Message}", ex.Message);
            }
        }
    }
}
