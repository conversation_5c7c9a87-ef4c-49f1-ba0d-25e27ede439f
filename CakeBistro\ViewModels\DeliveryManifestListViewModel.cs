using CakeBistro.Models;
using CakeBistro.Services;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;

namespace CakeBistro.ViewModels
{
    public partial class DeliveryManifestListViewModel : ObservableObject
    {
        private readonly DeliveryManifestService _manifestService;
        private readonly VehicleService _vehicleService;
        private readonly DriverService _driverService;
        private readonly LoadingService _loadingService;

        [ObservableProperty]
        private ObservableCollection<DeliveryManifest> manifests = new();
        [ObservableProperty]
        private DeliveryManifest? selectedManifest;
        [ObservableProperty]
        private string? newDestination;
        [ObservableProperty]
        private int selectedVehicleId;
        [ObservableProperty]
        private int selectedDriverId;
        [ObservableProperty]
        private string? newNotes;
        [ObservableProperty]
        private ObservableCollection<ManifestItem> items = new();
        [ObservableProperty]
        private string? newProductName;
        [ObservableProperty]
        private int newQuantity;
        [ObservableProperty]
        private ManifestItem? selectedItem;
        [ObservableProperty]
        private ObservableCollection<Vehicle> vehicles = new();
        [ObservableProperty]
        private ObservableCollection<Driver> drivers = new();
        [ObservableProperty]
        private Vehicle? selectedVehicle;
        [ObservableProperty]
        private Driver? selectedDriver;

        public DeliveryManifestListViewModel(DeliveryManifestService manifestService, VehicleService vehicleService, DriverService driverService, LoadingService loadingService)
        {
            _manifestService = manifestService;
            _vehicleService = vehicleService;
            _driverService = driverService;
            _loadingService = loadingService;
        }

        [RelayCommand]
        public async Task LoadVehiclesAndDriversAsync()
        {
            Vehicles = new ObservableCollection<Vehicle>(await _vehicleService.GetAllVehiclesAsync());
            Drivers = new ObservableCollection<Driver>(await _driverService.GetAllDriversAsync());
        }

        [RelayCommand]
        public async Task LoadManifestsAsync()
        {
            var list = await _manifestService.GetAllManifestsAsync();
            Manifests = new ObservableCollection<DeliveryManifest>(list);
            await LoadVehiclesAndDriversAsync();
        }

        [RelayCommand]
        public void AddItem()
        {
            if (!string.IsNullOrWhiteSpace(NewProductName) && NewQuantity > 0)
            {
                var item = new ManifestItem { ProductName = NewProductName!, Quantity = NewQuantity };
                Items.Add(item);
                NewProductName = null;
                NewQuantity = 0;
            }
        }

        [RelayCommand]
        public void RemoveItem()
        {
            if (SelectedItem != null)
            {
                Items.Remove(SelectedItem);
            }
        }

        [RelayCommand]
        public async Task AddManifestAsync()
        {
            if (!string.IsNullOrWhiteSpace(NewDestination) && SelectedVehicle != null && SelectedDriver != null && Items.Any())
            {
                var manifest = new DeliveryManifest
                {
                    Destination = NewDestination!,
                    VehicleId = SelectedVehicle.Id,
                    DriverId = SelectedDriver.Id,
                    Notes = NewNotes,
                    Items = Items.ToList()
                };
                await _manifestService.CreateManifestAsync(manifest);
                await _loadingService.LoadManifestAsync(manifest); // Integrate with loading workflow
                await LoadManifestsAsync();
                NewDestination = null;
                SelectedVehicle = null;
                SelectedDriver = null;
                NewNotes = null;
                Items.Clear();
            }
        }

        [RelayCommand]
        public async Task RemoveManifestAsync()
        {
            if (SelectedManifest != null)
            {
                await _manifestService.RemoveManifestAsync(SelectedManifest.Id);
                await LoadManifestsAsync();
            }
        }
    }
}
