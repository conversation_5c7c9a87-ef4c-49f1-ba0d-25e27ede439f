using System;
using System.Collections.ObjectModel;
using System.Threading.Tasks;
using System.Windows.Input;
using CakeBistro.Core.Models;
using CakeBistro.Core.Services;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Xamarin.Forms;

namespace CakeBistro.ViewModels
{
    public class ProductionBatchViewModel : ObservableObject
    {
        private readonly ProductionControlService _productionService;
        public ObservableCollection<InventoryBatch> Batches { get; set; } = new();

        public ICommand CreateBatchCommand { get; }
        public ICommand LoadExpiringBatchesCommand { get; }

        public ProductionBatchViewModel(ProductionControlService productionService)
        {
            _productionService = productionService;
            CreateBatchCommand = new AsyncRelayCommand(CreateBatchInteractiveAsync);
            LoadExpiringBatchesCommand = new AsyncRelayCommand(() => LoadExpiringBatchesAsync(DateTime.UtcNow.AddDays(7)));
        }

        public async Task LoadExpiringBatchesAsync(DateTime threshold)
        {
            var expiring = await _productionService.GetBatchesExpiringSoonAsync(threshold);
            Batches.Clear();
            foreach (var batch in expiring)
                Batches.Add(batch);
        }

        public async Task CreateBatchAsync(int rawMaterialId, string batchNumber, DateTime productionDate, DateTime expiryDate, int quantity, decimal cost, string storageLocation, string traceabilityCode)
        {
            var batch = await _productionService.CreateBatchAsync(rawMaterialId, batchNumber, productionDate, expiryDate, quantity, cost, storageLocation, traceabilityCode);
            Batches.Add(batch);
        }

        public async Task UpdateBatchStatusAsync(Guid batchId, BatchStatus status)
        {
            await _productionService.UpdateBatchStatusAsync(batchId, status);
            // Optionally refresh the batch in the collection
        }

        private async Task CreateBatchInteractiveAsync()
        {
            // Example: Show dialog or use hardcoded values for demo
            await CreateBatchAsync(1, "BATCH-NEW", DateTime.UtcNow, DateTime.UtcNow.AddDays(10), 100, 50.0m, "A1", "TRACE-NEW");
        }

        [ObservableProperty]
        private int _inputRawMaterialId;
        [ObservableProperty]
        private string _inputBatchNumber = string.Empty;
        [ObservableProperty]
        private DateTime _inputProductionDate = DateTime.Today;
        [ObservableProperty]
        private DateTime _inputExpiryDate = DateTime.Today.AddDays(7);
        [ObservableProperty]
        private int _inputQuantity;
        [ObservableProperty]
        private decimal _inputCost;
        [ObservableProperty]
        private string _inputStorageLocation = string.Empty;
        [ObservableProperty]
        private string _inputTraceabilityCode = string.Empty;
        [ObservableProperty]
        private string _validationError = string.Empty;
        [ObservableProperty]
        private string _successMessage = string.Empty;
        private Color _statusColor = Colors.Transparent;

        public Color StatusColor
        {
            get => _statusColor;
            set => SetProperty(ref _statusColor, value);
        }

        public async Task ShowCreateBatchDialogAsync()
        {
            ValidationError = string.Empty;
            SuccessMessage = string.Empty;
            if (InputRawMaterialId <= 0)
            {
                ValidationError = "Raw Material ID must be positive.";
                StatusColor = Color.FromArgb("#D32F2F"); // Red for error
                return;
            }
            if (string.IsNullOrWhiteSpace(InputBatchNumber))
            {
                ValidationError = "Batch Number is required.";
                StatusColor = Color.FromArgb("#D32F2F"); // Red for error
                return;
            }
            if (InputQuantity <= 0)
            {
                ValidationError = "Quantity must be greater than zero.";
                StatusColor = Color.FromArgb("#D32F2F"); // Red for error
                return;
            }
            if (InputCost < 0)
            {
                ValidationError = "Cost cannot be negative.";
                StatusColor = Color.FromArgb("#D32F2F"); // Red for error
                return;
            }
            if (InputExpiryDate <= InputProductionDate)
            {
                ValidationError = "Expiry date must be after production date.";
                StatusColor = Color.FromArgb("#D32F2F"); // Red for error
                return;
            }
            await CreateBatchAsync(InputRawMaterialId, InputBatchNumber, InputProductionDate, InputExpiryDate, InputQuantity, InputCost, InputStorageLocation, InputTraceabilityCode);
            SuccessMessage = "Batch created successfully!";
            StatusColor = Color.FromArgb("#388E3C"); // Green for success
            ClearInputFields();
        }

        private void ClearInputFields()
        {
            InputRawMaterialId = 0;
            InputBatchNumber = string.Empty;
            InputProductionDate = DateTime.Today;
            InputExpiryDate = DateTime.Today.AddDays(7);
            InputQuantity = 0;
            InputCost = 0;
            InputStorageLocation = string.Empty;
            InputTraceabilityCode = string.Empty;
        }
    }
}
