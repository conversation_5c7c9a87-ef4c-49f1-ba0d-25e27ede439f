
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using CakeBistro.Models;
using Microsoft.EntityFrameworkCore;

namespace CakeBistro.Services
{
    /// <summary>
    /// Implementation of ILowStockAlertService interface to handle low stock monitoring and alerts
    /// </summary>
    public class LowStockAlertService : BaseService<LowStockAlert>, ILowStockAlertService
    {
        private readonly CakeBistroContext _context;

        /// <summary>
        /// Initializes a new instance of the <see cref="LowStockAlertService"/> class.
        /// </summary>
        /// <param name="context">The database context to use for low stock operations</param>
        public LowStockAlertService(CakeBistroContext context) : base(new Repository<LowStockAlert>(context))
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
        }

        /// <inheritdoc />
        public async Task<List<LowStockWarning>> CheckLowStockAsync(double thresholdPercentage = 20.0)
        {
            // Get all raw materials below threshold
            var rawMaterials = await _context.RawMaterials
                .Where(r => r.CurrentStock < (r.MinimumStockThreshold * thresholdPercentage / 100))
                .Select(r => new LowStockWarning
                {
                    ItemId = r.Id,
                    ItemName = r.Name,
                    ItemType = "RawMaterial",
                    CurrentStock = r.CurrentStock,
                    MinimumStock = r.MinimumStockThreshold,
                    PercentageRemaining = (double)(r.CurrentStock / r.MinimumStockThreshold) * 100
                })
                .ToListAsync();

            // Get all finished products below threshold
            var finishedProducts = await _context.FinishedProducts
                .Where(f => f.CurrentStock < (f.MinimumStockThreshold * thresholdPercentage / 100))
                .Select(f => new LowStockWarning
                {
                    ItemId = f.Id,
                    ItemName = f.Name,
                    ItemType = "FinishedProduct",
                    CurrentStock = f.CurrentStock,
                    MinimumStock = f.MinimumStockThreshold,
                    PercentageRemaining = (double)(f.CurrentStock / f.MinimumStockThreshold) * 100
                })
                .ToListAsync();

            // Combine results
            var warnings = new List<LowStockWarning>();
            warnings.AddRange(rawMaterials);
            warnings.AddRange(finishedProducts);

            // Create alerts for any items that don't already have an active alert
            foreach (var warning in warnings)
            {
                // Check if there's already an active alert for this item and type
                var existingAlert = await _context.LowStockAlerts
                    .FirstOrDefaultAsync(a => a.ItemId == warning.ItemId && 
                                              a.ItemType == warning.ItemType && 
                                              !a.IsResolved);

                if (existingAlert == null)
                {
                    // Create a new alert
                    var alert = new LowStockAlert
                    {
                        ItemId = warning.ItemId,
                        ItemType = warning.ItemType,
                        CurrentStock = warning.CurrentStock,
                        MinimumStock = warning.MinimumStock,
                        TriggeredDate = DateTime.UtcNow,
                        IsResolved = false,
                        AlertMessage = $"Low stock alert for {warning.ItemType} '{warning.ItemName}' ({warning.PercentageRemaining:F2}% remaining)"
                    };

                    _context.LowStockAlerts.Add(alert);
                }
                else
                {
                    // Update existing alert with current information
                    existingAlert.CurrentStock = warning.CurrentStock;
                    existingAlert.MinimumStock = warning.MinimumStock;
                    existingAlert.LastUpdated = DateTime.UtcNow;
                }
            }

            await _context.SaveChangesAsync();
            
            return warnings;
        }

        /// <inheritdoc />
        public async Task<List<LowStockAlert>> GetActiveAlertsAsync()
        {
            return await _context.LowStockAlerts
                .Where(a => !a.IsResolved)
                .OrderByDescending(a => a.TriggeredDate)
                .ToListAsync();
        }

        /// <inheritdoc />
        public async Task<List<LowStockAlert>> GetHistoricalAlertsAsync(DateTime startDate, DateTime endDate)
        {
            return await _context.LowStockAlerts
                .Where(a => a.TriggeredDate >= startDate && a.TriggeredDate <= endDate)
                .OrderByDescending(a => a.TriggeredDate)
                .ToListAsync();
        }

        /// <inheritdoc />
        public async Task<bool> AcknowledgeAlertAsync(int alertId)
        {
            var alert = await _context.LowStockAlerts.FindAsync(alertId);

            if (alert == null)
                return false;

            alert.IsResolved = true;
            alert.ResolvedDate = DateTime.UtcNow;
            alert.AlertAcknowledged = true;
            
            await _context.SaveChangesAsync();
            
            return true;
        }

        /// <inheritdoc />
        public async Task<LowStockAlert> CreateManualAlertAsync(LowStockAlert alert)
        {
            // Validate input parameters using base class helpers
            if (alert == null)
                throw new BusinessRuleValidationException("AlertNullCheck", "Alert cannot be null");
            
            if (alert.ItemId <= 0)
                throw new BusinessRuleValidationException("InvalidItemId", "Valid item ID is required for alert creation");
            
            if (string.IsNullOrEmpty(alert.ItemType))
                throw new BusinessRuleValidationException("ItemTypeRequired", "Item type must be specified");
            
            if (alert.CurrentStock < 0)
                throw new BusinessRuleValidationException("InvalidCurrentStock", "Current stock cannot be negative");
            
            if (alert.MinimumStock <= 0)
                throw new BusinessRuleValidationException("InvalidMinimumStock", "Minimum stock must be greater than zero");
            
            // Set default values for manual alert
            alert.TriggeredDate = DateTime.UtcNow;
            alert.IsResolved = false;
            alert.AlertAcknowledged = false;
            
            // If no message provided, create one based on the data
            if (string.IsNullOrEmpty(alert.AlertMessage))
            {
                alert.AlertMessage = $"Manual alert for {alert.ItemType} ID {alert.ItemId}: Current stock {alert.CurrentStock}, Minimum stock {alert.MinimumStock}";
            }
            
            // Add to database
            _context.LowStockAlerts.Add(alert);
            await _context.SaveChangesAsync();
            
            return alert;
        }

        /// <inheritdoc />
        public async Task<LowStockAlert> GetAlertByIdAsync(int alertId)
        {
            return await _context.LowStockAlerts.FindAsync(alertId);
        }
    }
}