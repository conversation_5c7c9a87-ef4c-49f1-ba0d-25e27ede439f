using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace CakeBistro.Core.Models
{
    public class QualityCheck
    {
        [Key]
        public int Id { get; set; }
        public int ProductionBatchId { get; set; }
        public string? Type { get; set; }
        public string? Parameter { get; set; }
        public decimal? MeasuredValue { get; set; }
        public decimal? MinValue { get; set; }
        public decimal? MaxValue { get; set; }
        public string? ExpectedResult { get; set; }
        public string? ActualResult { get; set; }
        public string? Status { get; set; } // Passed, Failed, NeedsReview
        public string? Notes { get; set; }
        public string? DefectDescription { get; set; }
        public string? CorrectiveAction { get; set; }
        public string? CheckedBy { get; set; }
        public DateTime CheckDate { get; set; } = DateTime.UtcNow;

        // Navigation
        public ProductionBatch? ProductionBatch { get; set; }
    }
}
