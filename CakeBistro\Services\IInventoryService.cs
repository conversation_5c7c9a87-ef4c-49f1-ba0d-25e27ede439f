using System;
using System.Threading.Tasks;
using CakeBistro.Core.Models;

namespace CakeBistro.Services
{
    // Interface defining operations for inventory management
    public interface IInventoryService : IBaseService<CakeBistro.Core.Models.RawMaterial>
    {
        /// <summary>
        /// Registers a new raw material with validation of minimum stock threshold
        /// </summary>
        /// <param name="rawMaterial">The raw material to register</param>
        /// <returns>The created raw material</returns>
        /// <exception cref="BusinessRuleValidationException">
        /// Thrown when validation fails:
        /// - Raw material is null (ruleName: "RawMaterialNullCheck")
        /// - Price per unit is not valid (ruleName: "InvalidPrice")
        /// - Minimum stock threshold is not valid (ruleName: "InvalidMinimumStockThreshold")
        /// </exception>
        Task<RawMaterial> RegisterRawMaterialAsync(RawMaterial rawMaterial);

        /// <summary>
        /// Registers a new finished product with validation of minimum stock threshold
        /// </summary>
        /// <param name="finishedProduct">The finished product to register</param>
        /// <returns>The created finished product</returns>
        /// <exception cref="BusinessRuleValidationException">
        /// Thrown when validation fails:
        /// - Finished product is null (ruleName: "FinishedProductNullCheck")
        /// - Sale price is not valid (ruleName: "InvalidSalePrice")
        /// - Minimum stock threshold is not valid (ruleName: "InvalidMinimumStockThreshold")
        /// </exception>
        Task<FinishedProduct> RegisterFinishedProductAsync(FinishedProduct finishedProduct);

        /// <summary>
        /// Updates an existing raw material with validation of minimum stock threshold
        /// </summary>
        /// <param name="rawMaterial">The raw material with updated information</param>
        /// <returns>The updated raw material</returns>
        /// <exception cref="BusinessRuleValidationException">
        /// Thrown when validation fails:
        /// - Raw material is null (ruleName: "RawMaterialNullCheck")
        /// - ID is not valid (ruleName: "InvalidId")
        /// - Price per unit is not valid (ruleName: "InvalidPrice")
        /// - Minimum stock threshold is not valid (ruleName: "InvalidMinimumStockThreshold")
        /// </exception>
        Task<RawMaterial> UpdateRawMaterialAsync(RawMaterial rawMaterial);

        /// <summary>
        /// Updates an existing finished product with validation of minimum stock threshold
        /// </summary>
        /// <param name="finishedProduct">The finished product with updated information</param>
        /// <returns>The updated finished product</returns>
        /// <exception cref="BusinessRuleValidationException">
        /// Thrown when validation fails:
        /// - Finished product is null (ruleName: "FinishedProductNullCheck")
        /// - ID is not valid (ruleName: "InvalidId")
        /// - Sale price is not valid (ruleName: "InvalidSalePrice")
        /// - Minimum stock threshold is not valid (ruleName: "InvalidMinimumStockThreshold")
        /// </exception>
        Task<FinishedProduct> UpdateFinishedProductAsync(FinishedProduct finishedProduct);

        // Register a new CakeBistro.Core.Models.Supplier
        Task<Guid> RegisterSupplierAsync(CakeBistro.Core.Models.Supplier CakeBistro.Core.Models.Supplier);
        
        // Get all active suppliers
        Task<IEnumerable<CakeBistro.Core.Models.Supplier>> GetAllSuppliersAsync();
        
        // Get suppliers by category
        Task<IEnumerable<CakeBistro.Core.Models.Supplier>> GetSuppliersByCategoryAsync(string category);
        
        // Search suppliers by name or contact information
        Task<IEnumerable<CakeBistro.Core.Models.Supplier>> SearchSuppliersAsync(string searchTerm);
        
        // Get active suppliers
        Task<IEnumerable<CakeBistro.Core.Models.Supplier>> GetActiveSuppliersAsync();
        
        // Record a stock movement (incoming or outgoing)
        Task<Guid> RecordStockMovementAsync(StockMovement movement);
        
        // Get all stock movements
        Task<IEnumerable<StockMovement>> GetAllStockMovementsAsync();
        
        // Get stock movements for a specific material
        Task<IEnumerable<StockMovement>> GetStockMovementsAsync(Guid materialId);
        
        // Create an inter-branch transfer
        Task<Guid> CreateInterBranchTransferAsync(InterBranchTransfer transfer);
        
        // Get all inter-branch transfers
        Task<IEnumerable<InterBranchTransfer>> GetAllInterBranchTransfersAsync();
        
        // Get inter-branch transfers for a specific material
        Task<IEnumerable<InterBranchTransfer>> GetInterBranchTransfersAsync(Guid materialId);
        
        // Get total stock quantity for a specific raw material
        Task<decimal> GetTotalStockQuantityAsync(Guid materialId);
        
        // Generate a stock statement for a specific material
        Task<RawMaterialStockStatement> GenerateStockStatementAsync(Guid materialId, DateTime startDate, DateTime endDate);
        
        // Get all purchase orders
        Task<IEnumerable<CakeBistro.Core.Models.PurchaseOrder>> GetAllPurchaseOrdersAsync();
        
        // Get purchase order by ID
        Task<CakeBistro.Core.Models.PurchaseOrder> GetPurchaseOrderByIdAsync(Guid id);
        
        // Create a new purchase order
        Task<Guid> CreatePurchaseOrderAsync(CakeBistro.Core.Models.PurchaseOrder order);
        
        // Update an existing purchase order
        Task UpdatePurchaseOrderAsync(CakeBistro.Core.Models.PurchaseOrder order);
        
        // Delete a purchase order
        Task DeletePurchaseOrderAsync(Guid id);
        
        // Add an item to a purchase order
        Task AddPurchaseItemAsync(PurchaseOrderItem item);
        
        // Remove an item from a purchase order
        Task RemovePurchaseItemAsync(Guid itemId);
        
        // Get all low stock alerts
        Task<IEnumerable<LowStockAlert>> GetAllLowStockAlertsAsync();
        
        // Get active low stock alerts
        Task<IEnumerable<LowStockAlert>> GetActiveLowStockAlertsAsync();
        
        // Create a new low stock alert
        Task<Guid> CreateLowStockAlertAsync(LowStockAlert alert);
        
        // Update an existing low stock alert
        Task UpdateLowStockAlertAsync(LowStockAlert alert);
        
        // Delete a low stock alert
        Task DeleteLowStockAlertAsync(Guid id);
        
        // Sales Order Operations
        // Get all sales orders
        Task<IEnumerable<SalesOrder>> GetAllSalesOrdersAsync();
        
        // Get sales order by ID
        Task<SalesOrder> GetSalesOrderByIdAsync(Guid id);
        
        // Create a new sales order
        Task<Guid> CreateSalesOrderAsync(SalesOrder order);
        
        // Update an existing sales order
        Task UpdateSalesOrderAsync(SalesOrder order);
        
        // Delete a sales order
        Task DeleteSalesOrderAsync(Guid id);
        
        // Add an item to a sales order
        Task AddSalesItemAsync(SalesOrderItem item);
        
        // Remove an item from a sales order
        Task RemoveSalesItemAsync(Guid itemId);
        
        // Customer operations
        // Get all customers
        Task<IEnumerable<Customer>> GetAllCustomersAsync();
        
        // Search customers
        Task<IEnumerable<Customer>> SearchCustomersAsync(string searchTerm);
        
        // Get active customers
        Task<IEnumerable<Customer>> GetActiveCustomersAsync();
        
        // Product operations
        // Get products for sale
        Task<IEnumerable<Product>> GetSaleableProductsAsync();
        
        // Search products
        Task<IEnumerable<Product>> SearchProductsAsync(string searchTerm);
        
        // Get products by category
        Task<IEnumerable<Product>> GetProductsByCategoryAsync(string category);
        
        // Get low stock products
        Task<IEnumerable<Product>> GetLowStockProductsAsync(int threshold = 10);
        
        // Goods Receipt Operations
        // Get all goods receipts
        Task<IEnumerable<CakeBistro.Core.Models.GoodsReceipt>> GetAllGoodsReceiptsAsync();
        
        // Get goods receipt by ID
        Task<CakeBistro.Core.Models.GoodsReceipt> GetGoodsReceiptByIdAsync(Guid id);
        
        // Create a new goods receipt
        Task<Guid> CreateGoodsReceiptAsync(CakeBistro.Core.Models.GoodsReceipt receipt);
        
        // Update an existing goods receipt
        Task UpdateGoodsReceiptAsync(CakeBistro.Core.Models.GoodsReceipt receipt);
        
        // Delete a goods receipt
        Task DeleteGoodsReceiptAsync(Guid id);
        
        // Get goods receipts by purchase order
        Task<IEnumerable<CakeBistro.Core.Models.GoodsReceipt>> GetReceiptsByPurchaseOrderAsync(Guid purchaseOrderId);
        
        // Get goods receipts by date range
        Task<IEnumerable<CakeBistro.Core.Models.GoodsReceipt>> GetReceiptsByDateRangeAsync(DateTime startDate, DateTime endDate);
        
        // Get total received quantity for a purchase order item
        Task<decimal> GetTotalReceivedQuantityAsync(Guid purchaseOrderItemId);
        
        // Goods Receipt Items Operations
        // Get all goods receipt items
        Task<IEnumerable<GoodsReceiptItem>> GetAllGoodsReceiptItemsAsync();
        
        // Get goods receipt item by ID
        Task<GoodsReceiptItem> GetGoodsReceiptItemByIdAsync(Guid id);
        
        // Add a new goods receipt item
        Task<Guid> AddGoodsReceiptItemAsync(GoodsReceiptItem item);
        
        // Update an existing goods receipt item
        Task UpdateGoodsReceiptItemAsync(GoodsReceiptItem item);
        
        // Delete a goods receipt item
        Task DeleteGoodsReceiptItemAsync(Guid id);
        
        // Get items for a specific goods receipt
        Task<IEnumerable<GoodsReceiptItem>> GetGoodsReceiptItemsByReceiptAsync(Guid receiptId);
        
        // Get items by purchase order item
        Task<IEnumerable<GoodsReceiptItem>> GetGoodsReceiptItemsByPurchaseOrderItemAsync(Guid purchaseOrderItemId);
        
        // Report Operations
        // Generate sales report
        Task<SalesReport> GenerateSalesReportAsync(DateTime startDate, DateTime endDate);
        
        // Generate inventory report
        Task<InventoryReport> GenerateInventoryReportAsync();
        
        // Generate purchase report
        Task<PurchaseReport> GeneratePurchaseReportAsync(DateTime startDate, DateTime endDate);
        
        // Get historical reports
        Task<IEnumerable<ReportBase>> GetHistoricalReportsAsync(DateTime startDate, DateTime endDate);
        
        // Export report data
        Task<byte[]> ExportReportAsync(Guid reportId, string format);
    }
}
