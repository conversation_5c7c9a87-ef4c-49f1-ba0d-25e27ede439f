using CakeBistro.ViewModels;
using Microsoft.Maui.Controls;

namespace CakeBistro.Views
{
    public partial class RoleManagementPage : ContentPage
    {
        private readonly RoleManagementViewModel _viewModel;
        
        public RoleManagementPage(RoleManagementViewModel viewModel)
        {
            InitializeComponent();
            _viewModel = viewModel;
            BindingContext = _viewModel;
            
            // Subscribe to messaging center events
            MessagingCenter.Subscribe<RoleManagementViewModel, string>(this, "NavigateTo", (sender, args) =>
            {
                // Handle navigation requests from view model
                if (args == "MainPage")
                {
                    // Navigate to main page
                    Shell.Current.GoToAsync("//MainPage");
                }
            });
        }
        
        protected override async void OnAppearing()
        {
            base.OnAppearing();
            
            // Load roles when page appears
            await _viewModel.LoadRolesCommand.ExecuteAsync(null);
            
            // Load permissions when page appears
            await _viewModel.LoadAllPermissionsCommand.ExecuteAsync(null);
        }
        
        protected override void OnDisappearing()
        {
            base.OnDisappearing();
            // Unsubscribe from events when page is not visible
            MessagingCenter.Unsubscribe<RoleManagementViewModel, string>(this, "NavigateTo");
        }
    }
}
