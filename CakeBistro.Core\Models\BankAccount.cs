namespace CakeBistro.Core.Models
{
    public class BankAccount
    {
        public int Id { get; set; }
        public string? AccountNumber { get; set; }
        public string? BankName { get; set; }
        public decimal Balance { get; set; }
        public DateTime? CreatedDate { get; set; }
        public string? Status { get; set; }
        public DateTime? LastReconciled { get; set; }
        public decimal? ReconciledBalance { get; set; }
    }
}
