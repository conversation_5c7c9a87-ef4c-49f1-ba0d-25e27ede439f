{"ConnectionStrings": {"DefaultConnection": "Data Source=data/cake_bistro.db", "RedisConnection": "localhost:6379,options=abortConnect=false"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "RetryPolicy": {"MaxRetries": 5, "RetryDelaySeconds": 2}, "HealthCheck": {"DatabaseCheckEnabled": true, "DatabaseCheckIntervalSeconds": 30, "ApiCheckEnabled": true, "ApiCheckIntervalSeconds": 60, "ApiUrl": "https://api.example.com/health"}, "CacheSettings": {"SizeLimit": ********, "ExpirationScanFrequencyMinutes": 5, "CompactOnMemoryPressure": true, "UseRedis": false, "DefaultCacheDurationSeconds": 1800, "ShortTermCacheDurationSeconds": 300, "LongTermCacheDurationSeconds": 86400}, "IdentitySettings": {"PasswordRequireDigit": true, "PasswordRequiredLength": 8, "PasswordRequireNonAlphanumeric": true, "PasswordRequireUppercase": true, "PasswordRequireLowercase": true, "RequireAccountConfirmation": false, "TokenLifespanMinutes": 30, "MaxFailedAccessAttempts": 5}}