// Create FinanceServiceTests.cs
using CakeBistro.Services;
using CakeBistro.Core.Models;
using CakeBistro.Repositories;
using Microsoft.EntityFrameworkCore;
using Xunit;

namespace CakeBistro.Tests;

public class FinanceServiceTests
{
    private readonly CakeBistroContext _context;
    private readonly IFinanceService _financeService;
    private readonly IRepository<BankAccount> _accountRepository;
    private readonly IRepository<Transaction> _transactionRepository;
    private readonly ISupplierRepository _supplierRepository;
    private readonly ISalesRepository _salesRepository;
    private readonly IInventoryRepository _inventoryRepository;
    private readonly IAssetRepository _assetRepository;
    private readonly IDepreciationRepository _depreciationRepository;
    private readonly IMaintenanceRepository _maintenanceRepository;

    public FinanceServiceTests()
    {
        // Set up in-memory database
        var options = new DbContextOptionsBuilder<CakeBistroContext>()
            .UseInMemoryDatabase(databaseName: "TestDatabase")
            .Options;

        _context = new CakeBistroContext(options);
        
        // Initialize repositories
        _accountRepository = new BaseRepository<BankAccount>(_context);
        _transactionRepository = new BaseRepository<Transaction>(_context);
        _supplierRepository = new SupplierRepository(_context);
        _salesRepository = new SalesRepository(_context);
        _inventoryRepository = new InventoryRepository(_context);
        _assetRepository = new AssetRepository(_context);
        _depreciationRepository = new DepreciationRepository(_context);
        _maintenanceRepository = new MaintenanceRepository(_context);
        
        // Initialize service with test repositories
        _financeService = new FinanceService(
            _accountRepository,
            _transactionRepository,
            _supplierRepository,
            _salesRepository,
            _inventoryRepository,
            _assetRepository,
            _depreciationRepository,
            _maintenanceRepository);
    }

    [Fact]
    public async Task AddAccountAsync_ShouldAddAccount()
    {
        // Arrange
        var account = new BankAccount
        {
            AccountNumber = "**********",
            Name = "Test Account",
            Type = "Checking",
            Status = "Active"
        };

        // Act
        var result = await _financeService.AddAccountAsync(account);
        
        // Assert
        Assert.NotNull(result);
        Assert.Equal("Test Account", result.Name);
        Assert.True(result.Id > 0);
    }

    [Fact]
    public async Task GetAccountByIdAsync_ShouldReturnAccount()
    {
        // Arrange
        var account = new BankAccount
        {
            AccountNumber = "**********",
            Name = "Test Account",
            Type = "Checking",
            Status = "Active"
        };
        
        var addedAccount = await _financeService.AddAccountAsync(account);
        
        // Act
        var result = await _financeService.GetAccountByIdAsync(addedAccount.Id);
        
        // Assert
        Assert.NotNull(result);
        Assert.Equal(addedAccount.Id, result.Id);
        Assert.Equal("Test Account", result.Name);
    }

    [Fact]
    public async Task UpdateAccountAsync_ShouldUpdateAccount()
    {
        // Arrange
        var account = new BankAccount
        {
            AccountNumber = "**********",
            Name = "Test Account",
            Type = "Checking",
            Status = "Active"
        };
        
        var addedAccount = await _financeService.AddAccountAsync(account);
        
        // Modify account
        addedAccount.Name = "Updated Account";
        addedAccount.Type = "Savings";
        
        // Act
        await _financeService.UpdateAccountAsync(addedAccount);
        
        // Get updated account
        var result = await _financeService.GetAccountByIdAsync(addedAccount.Id);
        
        // Assert
        Assert.NotNull(result);
        Assert.Equal("Updated Account", result.Name);
        Assert.Equal("Savings", result.Type);
    }

    [Fact]
    public async Task DeleteAccountAsync_ShouldRemoveAccount()
    {
        // Arrange
        var account = new BankAccount
        {
            AccountNumber = "**********",
            Name = "Test Account",
            Type = "Checking",
            Status = "Active"
        };
        
        var addedAccount = await _financeService.AddAccountAsync(account);
        
        // Act
        await _financeService.DeleteAccountAsync(addedAccount.Id);
        
        // Try to get deleted account
        var result = await _financeService.GetAccountByIdAsync(addedAccount.Id);
        
        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task RecordTransactionAsync_ShouldRecordTransaction()
    {
        // Arrange
        var account = new BankAccount
        {
            AccountNumber = "**********",
            Name = "Test Account",
            Type = "Checking",
            Status = "Active"
        };
        
        var addedAccount = await _financeService.AddAccountAsync(account);
        
        var transaction = new Transaction
        {
            AccountId = addedAccount.Id,
            Date = DateTime.Now,
            Amount = 100.0m,
            Description = "Test Transaction",
            Type = "Deposit"
        };
        
        // Act
        var result = await _financeService.RecordTransactionAsync(transaction);
        
        // Assert
        Assert.True(result.Success);
        Assert.Contains("successfully", result.Message);
        
        // Verify transaction was saved
        var transactions = await _financeService.GetTransactionHistoryAsync(addedAccount.Id, DateTime.Now.AddDays(-1), DateTime.Now.AddDays(1));
        Assert.Single(transactions);
        Assert.Equal(100.0m, transactions.First().Amount);
    }

    [Fact]
    public async Task GetAccountBalanceAsync_ShouldCalculateCorrectly()
    {
        // Arrange
        var account = new BankAccount
        {
            AccountNumber = "**********",
            Name = "Test Account",
            Type = "Checking",
            Status = "Active"
        };
        
        var addedAccount = await _financeService.AddAccountAsync(account);
        
        var transaction1 = new Transaction
        {
            AccountId = addedAccount.Id,
            Date = DateTime.Now,
            Amount = 100.0m,
            Description = "Test Transaction 1",
            Type = "Deposit"
        };
        
        var transaction2 = new Transaction
        {
            AccountId = addedAccount.Id,
            Date = DateTime.Now,
            Amount = -50.0m,
            Description = "Test Transaction 2",
            Type = "Withdrawal"
        };
        
        await _financeService.RecordTransactionAsync(transaction1);
        await _financeService.RecordTransactionAsync(transaction2);
        
        // Act
        var balance = await _financeService.GetAccountBalanceAsync(addedAccount.Id);
        
        // Assert
        Assert.Equal(50.0m, balance);
    }

    [Fact]
    public async Task ReconcileAccountAsync_ShouldUpdateReconciliationInfo()
    {
        // Arrange
        var account = new BankAccount
        {
            AccountNumber = "**********",
            Name = "Test Account",
            Type = "Checking",
            Status = "Active"
        };
        
        var addedAccount = await _financeService.AddAccountAsync(account);
        
        var reconciliationDate = new DateTime(2023, 1, 1);
        var statementBalance = 1000.0m;
        
        // Act
        await _financeService.ReconcileAccountAsync(addedAccount.Id, reconciliationDate, statementBalance);
        
        // Get updated account
        var result = await _financeService.GetAccountByIdAsync(addedAccount.Id);
        
        // Assert
        Assert.NotNull(result);
        Assert.Equal(reconciliationDate, result.LastReconciled);
        Assert.Equal(statementBalance, result.ReconciledBalance);
    }
}