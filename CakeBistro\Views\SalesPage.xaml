<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:viewModels="clr-namespace:CakeBistro.ViewModels"
             x:Class="CakeBistro.Views.SalesPage"
             Title="Sales">
    <ContentPage.BindingContext>
        <viewModels:SalesTransactionListViewModel />
    </ContentPage.BindingContext>
    <ScrollView>
        <VerticalStackLayout Padding="10">
            <Label Text="Sales Management"
                   FontSize="24"
                   HorizontalOptions="Center" />
            <Picker ItemsSource="{Binding Manifests}"
                    ItemDisplayBinding="{Binding Name}"
                    SelectedItem="{Binding SelectedManifest, Mode=TwoWay}" />
            <Entry Placeholder="Product Name" Text="{Binding ProductName, Mode=TwoWay}" />
            <Entry Placeholder="Quantity" Keyboard="Numeric" Text="{Binding Quantity, Mode=TwoWay}" />
            <Entry Placeholder="Unit Price" Keyboard="Numeric" Text="{Binding UnitPrice, Mode=TwoWay}" />
            <Entry Placeholder="Notes" Text="{Binding Notes, Mode=TwoWay}" />
            <Button Text="Add Transaction" Command="{Binding AddTransactionCommand}" />
            <CollectionView ItemsSource="{Binding Transactions}">
                <CollectionView.ItemTemplate>
                    <DataTemplate>
                        <Grid ColumnDefinitions="*,*,*,*">
                            <Label Text="{Binding ProductName}" />
                            <Label Text="{Binding Quantity}" />
                            <Label Text="{Binding UnitPrice, StringFormat='{}{0:C2}'}" />
                            <Label Text="{Binding Notes}" />
                        </Grid>
                    </DataTemplate>
                </CollectionView.ItemTemplate>
            </CollectionView>
        </VerticalStackLayout>
    </ScrollView>
</ContentPage>