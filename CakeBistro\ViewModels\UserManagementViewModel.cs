using System;
using System.Collections.ObjectModel;
using System.Windows.Input;
using CommunityToolkit.Mvvm.ComponentModel;

namespace CakeBistro.ViewModels
{
    public partial class UserManagementViewModel : ObservableObject
    {
        // ...existing properties...

        [ObservableProperty]
        private string? statusMessage;

        private Color _statusColor = Colors.Transparent;
        public Color StatusColor
        {
            get => _statusColor;
            set => SetProperty(ref _statusColor, value);
        }

        // ...existing code...

        public void AddUser()
        {
            try
            {
                // ...code to add user...

                StatusMessage = "User added successfully.";
                StatusColor = Color.FromArgb("#388E3C");
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error: {ex.Message}";
                StatusColor = Color.FromArgb("#D32F2F");
            }
        }

        public void UpdateUser()
        {
            try
            {
                // ...code to update user...

                StatusMessage = "User updated successfully.";
                StatusColor = Color.FromArgb("#388E3C");
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error: {ex.Message}";
                StatusColor = Color.FromArgb("#D32F2F");
            }
        }

        public void DeleteUser()
        {
            try
            {
                // ...code to delete user...

                StatusMessage = "User deleted successfully.";
                StatusColor = Color.FromArgb("#388E3C");
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error: {ex.Message}";
                StatusColor = Color.FromArgb("#D32F2F");
            }
        }

        public void SomeInfoAction()
        {
            // ...some code...

            StatusMessage = "Please fill all required fields.";
            StatusColor = Color.FromArgb("#1976D2");
        }

        // ...existing code...
    }
}
