<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2022/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="CakeBistro.Views.SupplierPaymentPage"
             Title="Supplier Payments">
    <ContentPage.Content>
        <VerticalStackLayout Spacing="20" Padding="20">
            <Label Text="Record Supplier Payment" FontSize="24"/>
            <Entry Placeholder="Supplier ID" Keyboard="Numeric" Text="{Binding SupplierId, Mode=TwoWay}"/>
            <Entry Placeholder="Amount" Keyboard="Numeric" Text="{Binding Amount, Mode=TwoWay}"/>
            <DatePicker Date="{Binding PaymentDate, Mode=TwoWay}"/>
            <Entry Placeholder="Payment Method" Text="{Binding PaymentMethod, Mode=TwoWay}"/>
            <Entry Placeholder="Reference Number" Text="{Binding ReferenceNumber, Mode=TwoWay}"/>
            <Editor Placeholder="Notes" Text="{Binding Notes, Mode=TwoWay}"/>
            <Button Text="Record Payment" Command="{Binding RecordPaymentCommand}"/>
            <Label Text="{Binding ResultMessage}" TextColor="Green"/>
            <Label Text="{Binding StatusMessage}"
                   IsVisible="{Binding StatusMessage, Converter={StaticResource EmptyToFalseConverter}}"
                   TextColor="{Binding StatusColor}" />

            <Label Text="Payment History" FontSize="20"/>
            <CollectionView ItemsSource="{Binding Payments}">
                <CollectionView.ItemTemplate>
                    <DataTemplate>
                        <Frame Padding="10" Margin="5">
                            <VerticalStackLayout>
                                <Label Text="{Binding PaymentDate, StringFormat='Date: {0:yyyy-MM-dd}'}"/>
                                <Label Text="{Binding Amount, StringFormat='Amount: {0:C2}'}"/>
                                <Label Text="{Binding PaymentMethod}"/>
                                <Label Text="{Binding ReferenceNumber}"/>
                                <Label Text="{Binding Notes}"/>
                            </VerticalStackLayout>
                        </Frame>
                    </DataTemplate>
                </CollectionView.ItemTemplate>
            </CollectionView>
        </VerticalStackLayout>
    </ContentPage.Content>
</ContentPage>
