using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Configuration;
using CakeBistro.Data;

namespace CakeBistro
{
    public static class DatabaseConfiguration
    {
        public static IServiceCollection AddDatabase(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddDbContext<CakeBistroContext>(options =>
                options.UseSqlite(configuration.GetConnectionString("CakeBistroDatabase")));

            return services;
        }
    }
}