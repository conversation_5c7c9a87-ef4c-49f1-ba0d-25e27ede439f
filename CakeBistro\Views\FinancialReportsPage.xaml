<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="CakeBistro.Views.FinancialReportsPage"
             xmlns:viewModels="clr-namespace:CakeBistro.ViewModels"
             Title="Financial Reports">
    <ContentPage.BindingContext>
        <viewModels:FinancialReportsViewModel />
    </ContentPage.BindingContext>
    <ScrollView>
        <VerticalStackLayout Spacing="16" Padding="16">
            <Label Text="Financial Reports Dashboard" FontSize="24" HorizontalOptions="Start"/>
            <Label Text="{Binding StatusMessage}"
                   IsVisible="{Binding StatusMessage, Converter={StaticResource EmptyToFalseConverter}}"
                   TextColor="{Binding StatusColor}" />
            <Grid ColumnDefinitions="*,*" RowDefinitions="Auto">
                <Label Grid.Row="0" Grid.Column="0" Text="Report Date:"/>
                <DatePicker Grid.Row="0" Grid.Column="1" Date="{Binding ReportDate, Mode=TwoWay, StringFormat='{0:yyyy-MM-dd}'}"/>
            </Grid>
            <HorizontalStackLayout Spacing="10" HorizontalOptions="Center">
                <Button Text="Balance Sheet" Command="{Binding GenerateBalanceSheetCommand}"/>
                <Button Text="Income Statement" Command="{Binding GenerateIncomeStatementCommand}"/>
                <Button Text="Cash Flow" Command="{Binding GenerateCashFlowCommand}"/>
            </HorizontalStackLayout>
            <HorizontalStackLayout Spacing="10" HorizontalOptions="Center">
                <Button Text="Export as PDF" Command="{Binding ExportPdfCommand}" IsEnabled="{Binding CurrentReport, Converter={StaticResource NullToFalseConverter}}"/>
                <Button Text="Export as Excel" Command="{Binding ExportExcelCommand}" IsEnabled="{Binding CurrentReport, Converter={StaticResource NullToFalseConverter}}"/>
            </HorizontalStackLayout>
            <ActivityIndicator IsRunning="{Binding IsBusy}" IsVisible="{Binding IsBusy}" Color="DarkBlue"/>
            <Frame IsVisible="{Binding CurrentReport, Converter={StaticResource NullToFalseConverter}}" BackgroundColor="LightYellow">
                <VerticalStackLayout Spacing="4">
                    <Label Text="{Binding CurrentReport.ReportDate, StringFormat='Report Date: {0:d}'}" FontAttributes="Bold"/>
                    <Label Text="{Binding CurrentReport.ReportType, StringFormat='Type: {0}'}" FontAttributes="Italic"/>
                    <Label Text="{Binding CurrentReport.TotalAssets, StringFormat='Total Assets: {0:C}'}" IsVisible="{Binding CurrentReport.TotalAssets, Converter={StaticResource GreaterThanZeroConverter}}"/>
                    <Label Text="{Binding CurrentReport.TotalLiabilities, StringFormat='Total Liabilities: {0:C}'}" IsVisible="{Binding CurrentReport.TotalLiabilities, Converter={StaticResource GreaterThanZeroConverter}}"/>
                    <Label Text="{Binding CurrentReport.TotalEquity, StringFormat='Total Equity: {0:C}'}" IsVisible="{Binding CurrentReport.TotalEquity, Converter={StaticResource GreaterThanZeroConverter}}"/>
                    <Label Text="{Binding CurrentReport.NetIncome, StringFormat='Net Income: {0:C}'}" IsVisible="{Binding CurrentReport.NetIncome, Converter={StaticResource GreaterThanZeroConverter}}"/>
                    <Label Text="{Binding CurrentReport.CashFlow, StringFormat='Cash Flow: {0:C}'}" IsVisible="{Binding CurrentReport.CashFlow, Converter={StaticResource GreaterThanZeroConverter}}"/>
                    <Label Text="Notes: {Binding CurrentReport.Notes}" LineBreakMode="WordWrap" MaxLines="3" IsVisible="{Binding CurrentReport.Notes, Converter={StaticResource EmptyToFalseConverter}}"/>
                    <CollectionView ItemsSource="{Binding CurrentReport.BankAccounts}" IsVisible="{Binding CurrentReport.BankAccounts.Count, Converter={StaticResource GreaterThanZeroConverter}}">
                        <CollectionView.HeaderTemplate>
                            <DataTemplate>
                                <Label Text="Bank Accounts" FontAttributes="Bold"/>
                            </DataTemplate>
                        </CollectionView.HeaderTemplate>
                        <CollectionView.ItemTemplate>
                            <DataTemplate>
                                <StackLayout Orientation="Horizontal" Spacing="10">
                                    <Label Text="{Binding Name}"/>
                                    <Label Text="{Binding Balance, StringFormat='Balance: {0:C}'}"/>
                                </StackLayout>
                            </DataTemplate>
                        </CollectionView.ItemTemplate>
                    </CollectionView>
                </VerticalStackLayout>
            </Frame>
            <Label Text="No report generated yet. Please select a report type above." IsVisible="{Binding CurrentReport, Converter={StaticResource NullToTrueConverter}}" FontAttributes="Italic" TextColor="Gray"/>
        </VerticalStackLayout>
    </ScrollView>
</ContentPage>
