using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using CakeBistro.Core.Models;

namespace CakeBistro.Core.Interfaces
{
    public interface IProductionService
    {
        // Product Management
        Task<IEnumerable<Product>> GetAllProductsAsync();
        Task<Product> GetProductByIdAsync(int id);
        Task<Product> AddProductAsync(Product product);
        Task UpdateProductAsync(Product product);
        Task DeleteProductAsync(int id);

        // Recipe Management
        Task<Recipe> GetRecipeByIdAsync(int id);
        Task<IEnumerable<Recipe>> GetAllRecipesAsync();
        Task<Recipe> CreateRecipeAsync(Recipe recipe);
        Task<Recipe> UpdateRecipeAsync(Recipe recipe);
        Task<bool> DeleteRecipeAsync(int id);
        Task<decimal> CalculateRecipeCostAsync(int recipeId);

        // Production Batch Management
        Task<ProductionBatch> GetProductionBatchByIdAsync(int id);
        Task<IEnumerable<ProductionBatch>> GetAllProductionBatchesAsync();
        Task<IEnumerable<ProductionBatch>> GetProductionBatchesByDateRangeAsync(DateTime startDate, DateTime endDate);
        Task<ProductionBatch> CreateProductionBatchAsync(ProductionBatch batch);
        Task<ProductionBatch> UpdateProductionBatchAsync(ProductionBatch batch);
        Task<bool> DeleteProductionBatchAsync(int id);
        Task<(bool Success, string Message)> StartProductionBatchAsync(int batchId);
        Task<(bool Success, string Message)> CompleteProductionBatchAsync(int batchId, int actualQuantity);
        Task<(bool Success, string Message)> CancelProductionBatchAsync(int batchId, string reason);

        // Inventory Management
        Task<(bool Success, string Message)> UpdateStockAsync(InventoryBatch batch);
        Task<(bool Success, string Message)> RecordDamageAsync(InventoryBatch batch, decimal quantity);
        Task<IEnumerable<InventoryBatch>> GetInventoryBatchesAsync();
    }
}
