
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using CakeBistro.Models;

namespace CakeBistro.Services
{
    /// <summary>
    /// Interface defining methods for low stock alert operations
    /// </summary>
    public interface ILowStockAlertService
    {
        /// <summary>
        /// Checks inventory items for low stock levels based on minimum thresholds
        /// </summary>
        /// <param name="thresholdPercentage">The percentage threshold to consider as low stock (default is 20%)</param>
        /// <returns>A list of items with low stock warnings</returns>
        Task<List<LowStockWarning>> CheckLowStockAsync(double thresholdPercentage = 20.0);

        /// <summary>
        /// Gets a list of current low stock alerts
        /// </summary>
        /// <returns>A list of active low stock alerts</returns>
        Task<List<LowStockAlert>> GetActiveAlertsAsync();

        /// <summary>
        /// Gets historical low stock alerts filtered by date range
        /// </summary>
        /// <param name="startDate">Start date for filtering historical alerts</param>
        /// <param name="endDate">End date for filtering historical alerts</param>
        /// <returns>A list of historical low stock alerts</returns>
        Task<List<LowStockAlert>> GetHistoricalAlertsAsync(DateTime startDate, DateTime endDate);

        /// <summary>
        /// Acknowledges a specific low stock alert
        /// </summary>
        /// <param name="alertId">The ID of the alert to acknowledge</param>
        /// <returns>True if the alert was successfully acknowledged</returns>
        Task<bool> AcknowledgeAlertAsync(int alertId);

        /// <summary>
        /// Creates a new low stock alert manually
        /// </summary>
        /// <param name="alert">The alert to create</param>
        /// <returns>The created alert</returns>
        Task<LowStockAlert> CreateManualAlertAsync(LowStockAlert alert);

        /// <summary>
        /// Gets a low stock alert by its ID
        /// </summary>
        /// <param name="alertId">The ID of the alert to retrieve</param>
        /// <returns>The requested low stock alert</returns>
        Task<LowStockAlert> GetAlertByIdAsync(int alertId);
    }

    /// <summary>
    /// Represents a low stock warning generated during inventory check
    /// </summary>
    public class LowStockWarning
    {
        /// <summary>
        /// Gets or sets the ID of the item with low stock
        /// </summary>
        public int ItemId { get; set; }
        
        /// <summary>
        /// Gets or sets the name of the item with low stock
        /// </summary>
        public string ItemName { get; set; }
        
        /// <summary>
        /// Gets or sets the type of item (RawMaterial or FinishedProduct)
        /// </summary>
        public string ItemType { get; set; }
        
        /// <summary>
        /// Gets or sets the current stock level
        /// </summary>
        public decimal CurrentStock { get; set; }
        
        /// <summary>
        /// Gets or sets the minimum stock threshold
        /// </summary>
        public decimal MinimumStock { get; set; }
        
        /// <summary>
        /// Gets or sets the percentage of stock remaining relative to the threshold
        /// </summary>
        public double PercentageRemaining { get; set; }
    }
}