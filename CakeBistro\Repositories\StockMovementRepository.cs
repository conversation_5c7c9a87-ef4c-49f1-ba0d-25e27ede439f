using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using CakeBistro.Core.Models;
using CakeBistro.Repositories;

namespace MCakeBistro.Repositories
{
    // Implementation of stock movement repository with inventory-specific data operations
    public class StockMovementRepository : RepositoryBase<StockMovement>, IStockMovementRepository
    {
        private readonly InventoryContext _context;
        
        public StockMovementRepository(InventoryContext context)
        {
            _context = context;
        }
        
        public override async Task<IEnumerable<StockMovement>> GetAllAsync()
        {
            return await _context.StockMovements.ToListAsync();
        }
        
        public override async Task<StockMovement> GetByIdAsync(Guid id)
        {
            return await _context.StockMovements.FindAsync(id);
        }
        
        public override async Task AddAsync(StockMovement movement)
        {
            await _context.StockMovements.AddAsync(movement);
            await _context.SaveChangesAsync();
        }
        
        public override async Task UpdateAsync(StockMovement movement)
        {
            _context.StockMovements.Update(movement);
            await _context.SaveChangesAsync();
        }
        
        public override async Task DeleteAsync(Guid id)
        {
            var movement = await _context.StockMovements.FindAsync(id);
            if (movement != null)
            {
                _context.StockMovements.Remove(movement);
                await _context.SaveChangesAsync();
            }
        }
        
        public async Task<IEnumerable<StockMovement>> GetStockMovementsAsync(Guid materialId)
        {
            return await _context.StockMovements
                .Where(m => m.RawMaterialId == materialId)
                .ToListAsync();
        }
        
        public async Task<IEnumerable<StockMovement>> GetStockMovementsByTypeAsync(MovementType type)
        {
            return await _context.StockMovements
                .Where(m => m.MovementType == type)
                .ToListAsync();
        }
        
        public async Task<IEnumerable<StockMovement>> GetStockMovementsByPeriodAsync(DateTime startDate, DateTime endDate)
        {
            return await _context.StockMovements
                .Where(m => m.MovementDate >= startDate && m.MovementDate <= endDate)
                .ToListAsync();
        }
        
        public async Task<decimal> GetTotalStockQuantityAsync(Guid materialId)
        {
            // Calculate total stock quantity from movements
            var movements = await _context.StockMovements
                .Where(m => m.RawMaterialId == materialId)
                .ToListAsync();
            
            decimal incoming = movements
                .Where(m => m.MovementType == MovementType.Incoming)
                .Sum(m => m.Quantity);
            
            decimal outgoing = movements
                .Where(m => m.MovementType == MovementType.Outgoing)
                .Sum(m => m.Quantity);
            
            return incoming - outgoing;
        }
    }
}
