// Update the namespaces below if 'CakeBistro.Core' does not exist or is named differently
// using CakeBistro.Repositories;
// using CakeBistro.Core.Entities; // Ensure this namespace contains InterBranchTransfer
using CakeBistro.Entities; // Update this to the actual namespace containing InterBranchTransfer

namespace CakeBistro.Services
{
    public class InterBranchTransferService
        : BaseService<CakeBistro.Core.Entities.InterBranchTransfer>,
            IInterBranchTransferService
    {
        public InterBranchTransferService(
            CakeBistro.Core.Interfaces.IRepository<InterBranchTransfer> repository
        )
            : base(repository) { }

        // Additional methods specific to InterBranchTransfer can be added here
    }
}
