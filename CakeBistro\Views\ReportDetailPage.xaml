<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="MCakeBistro.Views.ReportDetailPage"
             Title="Report Details">
    <VerticalStackLayout>
        <!-- Report header -->
        <Grid Padding="10" BackgroundColor="#f5f5f5">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>
            
            <VerticalStackLayout>
                <Label Text="{Binding Report.ReportName}"
                       FontSize="Large" FontAttributes="Bold"/>
                <Label Text="{Binding Report.ReportType} Report"
                       TextColor="Gray"/>
                <Label Text="Generated: {Binding Report.GeneratedDate, StringFormat='{0:MMM d, yyyy HH:mm}'}"
                       TextColor="Gray"/>
            </VerticalStackLayout>
            
            <HorizontalStackLayout Grid.Column="1" HorizontalOptions="End">
                <Button Text="PDF"
                        Command="{Binding ExportPdfCommand}"
                        HorizontalOptions="FillAndExpand"/>
                <Button Text="Excel"
                        Command="{Binding ExportExcelCommand}"
                        HorizontalOptions="FillAndExpand"/>
                <Button Text="CSV"
                        Command="{Binding ExportCsvCommand}"
                        HorizontalOptions="FillAndExpand"/>
            </HorizontalStackLayout>
        </Grid>
        
        <!-- Sales report details -->
        <ContentView x:Name="SalesReportContent" IsVisible="{Binding Report is SalesReport}">
            <VerticalStackLayout Padding="10">
                <Label Text="Sales Summary" FontSize="Medium" FontAttributes="Bold"/>
                
                <Grid Margin="0,10">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    
                    <Label Text="Total Sales:" Grid.Row="0" Grid.Column="0" TextColor="Gray"/>
                    <Label Text="{Binding Report.TotalSales, StringFormat='{}{0:C}}" Grid.Row="0" Grid.Column="1" HorizontalOptions="End"/>
                    
                    <Label Text="Total Orders:" Grid.Row="1" Grid.Column="0" TextColor="Gray"/>
                    <Label Text="{Binding Report.TotalOrders}" Grid.Row="1" Grid.Column="1" HorizontalOptions="End"/>
                    
                    <Label Text="Average Order Value:" Grid.Row="2" Grid.Column="0" TextColor="Gray"/>
                    <Label Text="{Binding Report.AverageOrderValue, StringFormat='{}{0:C}}" Grid.Row="2" Grid.Column="1" HorizontalOptions="End"/>
                    
                    <Label Text="Total Items Sold:" Grid.Row="3" Grid.Column="0" TextColor="Gray"/>
                    <Label Text="{Binding Report.TotalItemsSold}" Grid.Row="3" Grid.Column="1" HorizontalOptions="End"/>
                </Grid>
                
                <!-- Customer statistics -->
                <Label Text="Customer Statistics" FontSize="Medium" FontAttributes="Bold" Margin="0,20,0,10"/>
                
                <Grid Margin="0,10">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    
                    <Label Text="Total Customers:" Grid.Row="0" Grid.Column="0" TextColor="Gray"/>
                    <Label Text="{Binding Report.TotalCustomers}" Grid.Row="0" Grid.Column="1" HorizontalOptions="End"/>
                    
                    <Label Text="Active Customers:" Grid.Row="1" Grid.Column="0" TextColor="Gray"/>
                    <Label Text="{Binding Report.ActiveCustomers}" Grid.Row="1" Grid.Column="1" HorizontalOptions="End"/>
                </Grid>
                
                <!-- Product performance -->
                <Label Text="Top Selling Products" FontSize="Medium" FontAttributes="Bold" Margin="0,20,0,10"/>
                
                <CollectionView ItemsSource="{Binding Report.TopSellingProducts}"
                            SelectionMode="None">
                    <CollectionView.ItemTemplate>
                        <DataTemplate>
                            <Grid Padding="5">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                
                                <VerticalStackLayout>
                                    <Label Text="{Binding ProductName}"
                                           FontAttributes="Bold"/>
                                </VerticalStackLayout>
                                
                                <Label Grid.Column="1" Text="{Binding QuantitySold}" HorizontalOptions="Center" VerticalOptions="Center"/>
                                <Label Grid.Column="2" Text="{Binding TotalRevenue, StringFormat='{}{0:C}}" HorizontalOptions="End" VerticalOptions="Center"/>
                            </Grid>
                        </DataTemplate>
                    </CollectionView.ItemTemplate>
                </CollectionView>
                
                <Label Text="Low Performing Products" FontSize="Medium" FontAttributes="Bold" Margin="0,20,0,10"/>
                
                <CollectionView ItemsSource="{Binding Report.LowPerformingProducts}"
                            SelectionMode="None">
                    <CollectionView.ItemTemplate>
                        <DataTemplate>
                            <Grid Padding="5">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                
                                <VerticalStackLayout>
                                    <Label Text="{Binding ProductName}"
                                           FontAttributes="Bold"/>
                                </VerticalStackLayout>
                                
                                <Label Grid.Column="1" Text="{Binding QuantitySold}" HorizontalOptions="Center" VerticalOptions="Center"/>
                                <Label Grid.Column="2" Text="{Binding TotalRevenue, StringFormat='{}{0:C}}" HorizontalOptions="End" VerticalOptions="Center"/>
                            </Grid>
                        </DataTemplate>
                    </CollectionView.ItemTemplate>
                </CollectionView>
                
                <!-- Category performance -->
                <Label Text="Category Performance" FontSize="Medium" FontAttributes="Bold" Margin="0,20,0,10"/>
                
                <CollectionView ItemsSource="{Binding Report.CategoryPerformance}"
                            SelectionMode="None">
                    <CollectionView.ItemTemplate>
                        <DataTemplate>
                            <Grid Padding="5">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                
                                <Label Text="{Binding Category}" FontAttributes="Bold"/>
                                <Label Grid.Column="1" Text="{Binding TotalSales, StringFormat='{}{0:C}}" HorizontalOptions="End" VerticalOptions="Center"/>
                                <Label Grid.Column="2" Text="{Binding TotalOrders}" HorizontalOptions="End" VerticalOptions="Center"/>
                                <Label Grid.Column="3" Text="{Binding ProfitPercentage, StringFormat='{}{0:F1}%'}" HorizontalOptions="End" VerticalOptions="Center"/>
                            </Grid>
                        </DataTemplate>
                    </CollectionView.ItemTemplate>
                </CollectionView>
            </VerticalStackLayout>
        </ContentView>
        
        <!-- Inventory report details -->
        <ContentView x:Name="InventoryReportContent" IsVisible="{Binding Report is InventoryReport}">
            <VerticalStackLayout Padding="10">
                <Label Text="Inventory Summary" FontSize="Medium" FontAttributes="Bold"/>
                
                <Grid Margin="0,10">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    
                    <Label Text="Total Items:" Grid.Row="0" Grid.Column="0" TextColor="Gray"/>
                    <Label Text="{Binding Report.TotalItems}" Grid.Row="0" Grid.Column="1" HorizontalOptions="End"/>
                    
                    <Label Text="Total Inventory Value:" Grid.Row="1" Grid.Column="0" TextColor="Gray"/>
                    <Label Text="{Binding Report.TotalInventoryValue, StringFormat='{}{0:C}}" Grid.Row="1" Grid.Column="1" HorizontalOptions="End"/>
                    
                    <Label Text="In Stock Items:" Grid.Row="2" Grid.Column="0" TextColor="Gray"/>
                    <Label Text="{Binding Report.ItemsInStock}" Grid.Row="2" Grid.Column="1" HorizontalOptions="End"/>
                    
                    <Label Text="Low Stock Items:" Grid.Row="3" Grid.Column="0" TextColor="Gray"/>
                    <Label Text="{Binding Report.ItemsLowOnStock}" Grid.Row="3" Grid.Column="1" HorizontalOptions="End"/>
                </Grid>
                
                <!-- Turnover rates -->
                <Label Text="Turnover Analysis" FontSize="Medium" FontAttributes="Bold" Margin="0,20,0,10"/>
                
                <Grid Margin="0,10">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    
                    <Label Text="Inventory Turnover Rate:" Grid.Row="0" Grid.Column="0" TextColor="Gray"/>
                    <Label Text="{Binding Report.InventoryTurnoverRate, StringFormat='{}{0:F1}'}x" Grid.Row="0" Grid.Column="1" HorizontalOptions="End"/>
                    
                    <Label Text="Average Cost per Item:" Grid.Row="1" Grid.Column="0" TextColor="Gray"/>
                    <Label Text="{Binding Report.AverageCostPerItem, StringFormat='{}{0:C}}" Grid.Row="1" Grid.Column="1" HorizontalOptions="End"/>
                </Grid>
                
                <!-- Fast moving items -->
                <Label Text="Fast Moving Items" FontSize="Medium" FontAttributes="Bold" Margin="0,20,0,10"/>
                
                <CollectionView ItemsSource="{Binding Report.FastMovingItems}"
                            SelectionMode="None">
                    <CollectionView.ItemTemplate>
                        <DataTemplate>
                            <Grid Padding="5">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                
                                <Label Text="{Binding RawMaterialName}" FontAttributes="Bold"/>
                                <Label Grid.Column="1" Text="{Binding QuantitySold}" HorizontalOptions="End" VerticalOptions="Center"/>
                                <Label Grid.Column="2" Text="{Binding SalesAmount, StringFormat='{}{0:C}}" HorizontalOptions="End" VerticalOptions="Center"/>
                            </Grid>
                        </DataTemplate>
                    </CollectionView.ItemTemplate>
                </CollectionView>
                
                <!-- Slow moving items -->
                <Label Text="Slow Moving Items" FontSize="Medium" FontAttributes="Bold" Margin="0,20,0,10"/>
                
                <CollectionView ItemsSource="{Binding Report.SlowMovingItems}"
                            SelectionMode="None">
                    <CollectionView.ItemTemplate>
                        <DataTemplate>
                            <Grid Padding="5">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                
                                <Label Text="{Binding RawMaterialName}" FontAttributes="Bold"/>
                                <Label Grid.Column="1" Text="{Binding QuantitySold}" HorizontalOptions="End" VerticalOptions="Center"/>
                                <Label Grid.Column="2" Text="{Binding SalesAmount, StringFormat='{}{0:C}}" HorizontalOptions="End" VerticalOptions="Center"/>
                            </Grid>
                        </DataTemplate>
                    </CollectionView.ItemTemplate>
                </CollectionView>
                
                <!-- Category analysis -->
                <Label Text="Category Analysis" FontSize="Medium" FontAttributes="Bold" Margin="0,20,0,10"/>
                
                <CollectionView ItemsSource="{Binding Report.CategoryAnalyses}"
                            SelectionMode="None">
                    <CollectionView.ItemTemplate>
                        <DataTemplate>
                            <Grid Padding="5">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                
                                <Label Text="{Binding Category}" FontAttributes="Bold"/>
                                <Label Grid.Column="1" Text="{Binding ItemCount}" HorizontalOptions="End" VerticalOptions="Center"/>
                                <Label Grid.Column="2" Text="{Binding TotalCostValue, StringFormat='{}{0:C}}" HorizontalOptions="End" VerticalOptions="Center"/>
                                <Label Grid.Column="3" Text="{Binding ProfitPotential, StringFormat='{}{0:C}}" HorizontalOptions="End" VerticalOptions="Center"/>
                            </Grid>
                        </DataTemplate>
                    </CollectionView.ItemTemplate>
                </CollectionView>
                
                <!-- Recent movements -->
                <Label Text="Recent Movements" FontSize="Medium" FontAttributes="Bold" Margin="0,20,0,10"/>
                
                <CollectionView ItemsSource="{Binding Report.RecentMovements}"
                            SelectionMode="None">
                    <CollectionView.ItemTemplate>
                        <DataTemplate>
                            <Grid Padding="5">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                
                                <VerticalStackLayout>
                                    <Label Text="{Binding RawMaterial.Name}"
                                           FontAttributes="Bold"/>
                                    <Label Text="{Binding MovementType} - {Binding Location}"
                                           TextColor="Gray"/>
                                </VerticalStackLayout>
                                
                                <Label Grid.Column="1" Text="{Binding Quantity, StringFormat='{}{0:F2}}'" HorizontalOptions="End" VerticalOptions="Center"/>
                                <Label Grid.Column="2" Text="{Binding Date, StringFormat='{0:MMM d}'}" HorizontalOptions="End" VerticalOptions="Center"/>
                            </Grid>
                        </DataTemplate>
                    </CollectionView.ItemTemplate>
                </CollectionView>
            </VerticalStackLayout>
        </ContentView>
        
        <!-- Purchase report details -->
        <ContentView x:Name="PurchaseReportContent" IsVisible="{Binding Report is PurchaseReport}">
            <VerticalStackLayout Padding="10">
                <Label Text="Purchase Summary" FontSize="Medium" FontAttributes="Bold"/>
                
                <Grid Margin="0,10">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    
                    <Label Text="Total Purchases:" Grid.Row="0" Grid.Column="0" TextColor="Gray"/>
                    <Label Text="{Binding Report.TotalPurchases, StringFormat='{}{0:C}}" Grid.Row="0" Grid.Column="1" HorizontalOptions="End"/>
                    
                    <Label Text="Total Purchase Orders:" Grid.Row="1" Grid.Column="0" TextColor="Gray"/>
                    <Label Text="{Binding Report.TotalPurchaseOrders}" Grid.Row="1" Grid.Column="1" HorizontalOptions="End"/>
                    
                    <Label Text="Total Suppliers:" Grid.Row="2" Grid.Column="0" TextColor="Gray"/>
                    <Label Text="{Binding Report.TotalSuppliers}" Grid.Row="2" Grid.Column="1" HorizontalOptions="End"/>
                    
                    <Label Text="Average Order Value:" Grid.Row="3" Grid.Column="0" TextColor="Gray"/>
                    <Label Text="{Binding Report.AverageOrderValue, StringFormat='{}{0:C}}" Grid.Row="3" Grid.Column="1" HorizontalOptions="End"/>
                    
                    <Label Text="On Time Delivery Rate:" Grid.Row="4" Grid.Column="0" TextColor="Gray"/>
                    <Label Text="{Binding Report.OnTimeDeliveryRate, StringFormat='{}{0:F1}%'}" Grid.Row="4" Grid.Column="1" HorizontalOptions="End"/>
                    
                    <Label Text="Average Lead Time:" Grid.Row="5" Grid.Column="0" TextColor="Gray"/>
                    <Label Text="{Binding Report.AverageLeadTimeDays, StringFormat='{}{0:F1} days'}" Grid.Row="5" Grid.Column="1" HorizontalOptions="End"/>
                </Grid>
                
                <!-- Supplier performance -->
                <Label Text="Supplier Performance" FontSize="Medium" FontAttributes="Bold" Margin="0,20,0,10"/>
                
                <CollectionView ItemsSource="{Binding Report.SupplierPerformances}"
                            SelectionMode="None">
                    <CollectionView.ItemTemplate>
                        <DataTemplate>
                            <Grid Padding="5">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                
                                <VerticalStackLayout>
                                    <Label Text="{Binding SupplierName}"
                                           FontAttributes="Bold"/>
                                    <Label Text="{Binding PurchaseCount} orders" TextColor="Gray"/>
                                </VerticalStackLayout>
                                
                                <Label Grid.Column="1" Text="{Binding TotalSpent, StringFormat='{}{0:C}}" HorizontalOptions="End" VerticalOptions="Center"/>
                                <Label Grid.Column="2" Text="{Binding OnTimeDeliveryRate, StringFormat='{}{0:F1}%'}" HorizontalOptions="End" VerticalOptions="Center"/>
                                <Label Grid.Column="3" Text="{Binding AverageLeadTimeDays, StringFormat='{}{0:F1} days'}" HorizontalOptions="End" VerticalOptions="Center"/>
                            </Grid>
                        </DataTemplate>
                    </CollectionView.ItemTemplate>
                </CollectionView>
                
                <!-- Top suppliers -->
                <Label Text="Top Suppliers by Volume" FontSize="Medium" FontAttributes="Bold" Margin="0,20,0,10"/>
                
                <CollectionView ItemsSource="{Binding Report.TopSuppliersByVolume}"
                            SelectionMode="None">
                    <CollectionView.ItemTemplate>
                        <DataTemplate>
                            <Grid Padding="5">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                
                                <Label Text="{Binding SupplierName}"
                                       FontAttributes="Bold"/>
                                <Label Grid.Column="1" Text="{Binding PurchaseCount}" HorizontalOptions="End" VerticalOptions="Center"/>
                                <Label Grid.Column="2" Text="{Binding TotalSpent, StringFormat='{}{0:C}}" HorizontalOptions="End" VerticalOptions="Center"/>
                            </Grid>
                        </DataTemplate>
                    </CollectionView.ItemTemplate>
                </CollectionView>
                
                <Label Text="Top Suppliers by Value" FontSize="Medium" FontAttributes="Bold" Margin="0,20,0,10"/>
                
                <CollectionView ItemsSource="{Binding Report.TopSuppliersByValue}"
                            SelectionMode="None">
                    <CollectionView.ItemTemplate>
                        <DataTemplate>
                            <Grid Padding="5">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                
                                <Label Text="{Binding SupplierName}"
                                       FontAttributes="Bold"/>
                                <Label Grid.Column="1" Text="{Binding PurchaseCount}" HorizontalOptions="End" VerticalOptions="Center"/>
                                <Label Grid.Column="2" Text="{Binding TotalSpent, StringFormat='{}{0:C}}" HorizontalOptions="End" VerticalOptions="Center"/>
                            </Grid>
                        </DataTemplate>
                    </CollectionView.ItemTemplate>
                </CollectionView>
                
                <!-- Purchase item analysis -->
                <Label Text="Top Purchased Items" FontSize="Medium" FontAttributes="Bold" Margin="0,20,0,10"/>
                
                <CollectionView ItemsSource="{Binding Report.TopPurchasedItems}"
                            SelectionMode="None">
                    <CollectionView.ItemTemplate>
                        <DataTemplate>
                            <Grid Padding="5">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                
                                <Label Text="{Binding RawMaterialName}"
                                       FontAttributes="Bold"/>
                                <Label Grid.Column="1" Text="{Binding QuantityPurchased}" HorizontalOptions="End" VerticalOptions="Center"/>
                                <Label Grid.Column="2" Text="{Binding TotalCost, StringFormat='{}{0:C}}" HorizontalOptions="End" VerticalOptions="Center"/>
                            </Grid>
                        </DataTemplate>
                    </CollectionView.ItemTemplate>
                </CollectionView>
                
                <Label Text="High Cost Items" FontSize="Medium" FontAttributes="Bold" Margin="0,20,0,10"/>
                
                <CollectionView ItemsSource="{Binding Report.HighCostItems}"
                            SelectionMode="None">
                    <CollectionView.ItemTemplate>
                        <DataTemplate>
                            <Grid Padding="5">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                
                                <Label Text="{Binding RawMaterialName}"
                                       FontAttributes="Bold"/>
                                <Label Grid.Column="1" Text="{Binding QuantityPurchased}" HorizontalOptions="End" VerticalOptions="Center"/>
                                <Label Grid.Column="2" Text="{Binding TotalCost, StringFormat='{}{0:C}}" HorizontalOptions="End" VerticalOptions="Center"/>
                            </Grid>
                        </DataTemplate>
                    </CollectionView.ItemTemplate>
                </CollectionView>
                
                <!-- Order status -->
                <Label Text="Order Status" FontSize="Medium" FontAttributes="Bold" Margin="0,20,0,10"/>
                
                <Grid Margin="0,10">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    
                    <Label Text="Draft Orders:" Grid.Row="0" Grid.Column="0" TextColor="Gray"/>
                    <Label Text="{Binding Report.DraftOrders}" Grid.Row="0" Grid.Column="1" HorizontalOptions="End"/>
                    
                    <Label Text="Submitted Orders:" Grid.Row="1" Grid.Column="0" TextColor="Gray"/>
                    <Label Text="{Binding Report.SubmittedOrders}" Grid.Row="1" Grid.Column="1" HorizontalOptions="End"/>
                    
                    <Label Text="Partially Received:" Grid.Row="2" Grid.Column="0" TextColor="Gray"/>
                    <Label Text="{Binding Report.PartiallyReceivedOrders}" Grid.Row="2" Grid.Column="1" HorizontalOptions="End"/>
                    
                    <Label Text="Completed Orders:" Grid.Row="3" Grid.Column="0" TextColor="Gray"/>
                    <Label Text="{Binding Report.CompletedOrders}" Grid.Row="3" Grid.Column="1" HorizontalOptions="End"/>
                    
                    <Label Text="On Time Delivery Rate:" Grid.Row="4" Grid.Column="0" TextColor="Gray"/>
                    <Label Text="{Binding Report.OnTimeDeliveryRate, StringFormat='{}{0:F1}%'}" Grid.Row="4" Grid.Column="1" HorizontalOptions="End"/>
                    
                    <Label Text="Average Lead Time:" Grid.Row="5" Grid.Column="0" TextColor="Gray"/>
                    <Label Text="{Binding Report.AverageLeadTimeDays, StringFormat='{}{0:F1} days'}" Grid.Row="5" Grid.Column="1" HorizontalOptions="End"/>
                </Grid>
                
                <!-- Category analysis -->
                <Label Text="Category Analysis" FontSize="Medium" FontAttributes="Bold" Margin="0,20,0,10"/>
                
                <CollectionView ItemsSource="{Binding Report.CategoryAnalyses}"
                            SelectionMode="None">
                    <CollectionView.ItemTemplate>
                        <DataTemplate>
                            <Grid Padding="5">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                
                                <Label Text="{Binding Category}" FontAttributes="Bold"/>
                                <Label Grid.Column="1" Text="{Binding ItemCount}" HorizontalOptions="End" VerticalOptions="Center"/>
                                <Label Grid.Column="2" Text="{Binding TotalCostValue, StringFormat='{}{0:C}}" HorizontalOptions="End" VerticalOptions="Center"/>
                                <Label Grid.Column="3" Text="{Binding ProfitPercentage, StringFormat='{}{0:F1}%'}" HorizontalOptions="End" VerticalOptions="Center"/>
                            </Grid>
                        </DataTemplate>
                    </CollectionView.ItemTemplate>
                </CollectionView>
            </VerticalStackLayout>
        </ContentView>
        
        <!-- Action buttons -->
        <HorizontalStackLayout Spacing="10" Padding="10">
            <Button Text="Export PDF"
                    Command="{Binding ExportPdfCommand}"
                    HorizontalOptions="FillAndExpand"/>
            <Button Text="Export Excel"
                    Command="{Binding ExportExcelCommand}"
                    HorizontalOptions="FillAndExpand"/>
            <Button Text="Export CSV"
                    Command="{Binding ExportCsvCommand}"
                    HorizontalOptions="FillAndExpand"/>
        </HorizontalStackLayout>
    </VerticalStackLayout>
</ContentPage>