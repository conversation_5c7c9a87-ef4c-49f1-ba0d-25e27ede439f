using CakeBistro.Core.Models;

namespace MCakeBistro.Repositories
{
    public class RoleRepository : Repository<CakeBistro.Core.Models.Role>, IRoleRepository
    {
        public RoleRepository(InventoryContext context) : base(context)
        {
        }
        
        public async Task<CakeBistro.Core.Models.Role> GetRoleByNameAsync(string roleName)
        {
            return await _context.Roles
                .FirstOrDefaultAsync(r => r.Name == roleName);
        }
        
        public async Task<Guid> AddRoleAsync(CakeBistro.Core.Models.Role CakeBistro.Core.Models.Role)
        {
            await _context.Roles.AddAsync(CakeBistro.Core.Models.Role);
            await _context.SaveChangesAsync();
            return CakeBistro.Core.Models.Role.Id;
        }
        
        public async Task UpdateRoleAsync(CakeBistro.Core.Models.Role CakeBistro.Core.Models.Role)
        {
            var existingRole = await _context.Roles.FindAsync(CakeBistro.Core.Models.Role.Id);
            if (existingRole != null)
            {
                existingRole.Name = CakeBistro.Core.Models.Role.Name;
                existingRole.Description = CakeBistro.Core.Models.Role.Description;
                existingRole.IsSystemRole = CakeBistro.Core.Models.Role.IsSystemRole;
                
                await _context.SaveChangesAsync();
            }
        }
        
        public async Task DeleteRoleAsync(Guid id)
        {
            var CakeBistro.Core.Models.Role = await _context.Roles.FindAsync(id);
            if (CakeBistro.Core.Models.Role != null)
            {
                _context.Roles.Remove(CakeBistro.Core.Models.Role);
                await _context.SaveChangesAsync();
            }
        }
        
        public async Task<IEnumerable<CakeBistro.Core.Models.Role>> GetAllRolesWithPermissionsAsync()
        {
            return await _context.Roles
                .Include(r => r.RolePermissions)
                    .ThenInclude(rp => rp.Permission)
                .ToListAsync();
        }
        
        public async Task<IEnumerable<Permission>> GetRolePermissionsAsync(Guid roleId)
        {
            return await _context.RolePermissions
                .Where(rp => rp.RoleId == roleId)
                .Select(rp => rp.Permission)
                .ToListAsync();
        }
        
        public async Task AssignPermissionsToRoleAsync(Guid roleId, IEnumerable<Guid> permissionIds)
        {
            var CakeBistro.Core.Models.Role = await _context.Roles
                .Include(r => r.RolePermissions)
                .FirstOrDefaultAsync(r => r.Id == roleId);
            
            if (CakeBistro.Core.Models.Role == null) return;
            
            // Remove existing permissions that are not in the new list
            var existingPermissions = CakeBistro.Core.Models.Role.RolePermissions.Select(rp => rp.PermissionId).ToList();
            var permissionsToRemove = CakeBistro.Core.Models.Role.RolePermissions
                .Where(rp => !permissionIds.Contains(rp.PermissionId)).ToList();
            
            foreach (var permission in permissionsToRemove)
            {
                CakeBistro.Core.Models.Role.RolePermissions.Remove(permission);
            }
            
            // Add new permissions that aren't already assigned
            var permissionsToAdd = permissionIds
                .Where(id => !existingPermissions.Contains(id))
                .Select(id => new RolePermission
                {
                    RoleId = roleId,
                    PermissionId = id
                });
            
            foreach (var permission in permissionsToAdd)
            {
                CakeBistro.Core.Models.Role.RolePermissions.Add(permission);
            }
            
            await _context.SaveChangesAsync();
        }
        
        public async Task RemovePermissionsFromRoleAsync(Guid roleId, IEnumerable<Guid> permissionIds)
        {
            var CakeBistro.Core.Models.Role = await _context.Roles
                .Include(r => r.RolePermissions)
                .FirstOrDefaultAsync(r => r.Id == roleId);
            
            if (CakeBistro.Core.Models.Role == null) return;
            
            var permissionsToRemove = CakeBistro.Core.Models.Role.RolePermissions
                .Where(rp => permissionIds.Contains(rp.PermissionId)).ToList();
            
            foreach (var permission in permissionsToRemove)
            {
                CakeBistro.Core.Models.Role.RolePermissions.Remove(permission);
            }
            
            await _context.SaveChangesAsync();
        }
        
        public async Task<bool> HasUserPermissionAsync(Guid userId, string permissionName)
        {
            return await _context.UserPermissions
                .AnyAsync(up => up.UserId == userId && 
                              up.Permission.Name == permissionName);
        }
    }
}
