<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:vm="clr-namespace:CakeBistro.ViewModels"
             x:Class="CakeBistro.Views.NotificationSettingsPage"
             x:DataType="vm:NotificationSettingsViewModel"
             Title="Notification Settings">
    <VerticalStackLayout Padding="20" Spacing="20">
        <Label Text="Notification Preferences" FontSize="20" FontAttributes="Bold"/>
        <Switch IsToggled="{Binding Preferences.EnableFailedCheckNotifications}"/>
        <Label Text="Enable notifications for failed quality checks"/>
        <Switch IsToggled="{Binding Preferences.EnablePendingCheckNotifications}"/>
        <Label Text="Enable notifications for pending quality checks"/>
        <Switch IsToggled="{Binding Preferences.EnableBatchStatusNotifications}"/>
        <Label Text="Enable notifications for batch status changes"/>
        <Switch IsToggled="{Binding Preferences.EnableLocalNotifications}"/>
        <Label Text="Enable local (device) notifications"/>
        <Button Text="Save Preferences" Command="{Binding SaveCommand}"/>
    </VerticalStackLayout>
</ContentPage>
