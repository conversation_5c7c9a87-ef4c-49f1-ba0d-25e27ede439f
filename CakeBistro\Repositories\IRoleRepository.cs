using CakeBistro.Core.Models;

namespace CakeBistro.Repositories
{
    public interface IRoleRepository : IRepository<CakeBistro.Core.Models.Role>
    {
        // Get CakeBistro.Core.Models.Role by name
        Task<CakeBistro.Core.Models.Role> GetRoleByNameAsync(string roleName);
        
        // Add a new CakeBistro.Core.Models.Role
        Task<Guid> AddRoleAsync(CakeBistro.Core.Models.Role CakeBistro.Core.Models.Role);
        
        // Update an existing CakeBistro.Core.Models.Role
        Task UpdateRoleAsync(CakeBistro.Core.Models.Role CakeBistro.Core.Models.Role);
        
        // Delete a CakeBistro.Core.Models.Role
        Task DeleteRoleAsync(Guid id);
        
        // Get all roles with their permissions
        Task<IEnumerable<CakeBistro.Core.Models.Role>> GetAllRolesWithPermissionsAsync();
        
        // Get CakeBistro.Core.Models.Role permissions
        Task<IEnumerable<Permission>> GetRolePermissionsAsync(Guid roleId);
        
        // Assign permissions to CakeBistro.Core.Models.Role
        Task AssignPermissionsToRoleAsync(Guid roleId, IEnumerable<Guid> permissionIds);
        
        // Remove permissions from CakeBistro.Core.Models.Role
        Task RemovePermissionsFromRoleAsync(Guid roleId, IEnumerable<Guid> permissionIds);
        
        // Check if CakeBistro.Core.Models.User has permission
        Task<bool> HasUserPermissionAsync(Guid userId, string permissionName);
    }
}
