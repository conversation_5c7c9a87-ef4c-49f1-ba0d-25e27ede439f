using CakeBistro.Core.Models;

namespace CakeBistro.Repositories
{
    public interface IRoleRepository : IRepository<Role>
    {
        // Get Role by name
        Task<Role?> GetRoleByNameAsync(string roleName);

        // Add a new Role
        Task<Guid> AddRoleAsync(Role role);

        // Update an existing Role
        Task UpdateRoleAsync(Role role);

        // Delete a Role
        Task DeleteRoleAsync(Guid id);

        // Get all roles with their permissions
        Task<IEnumerable<Role>> GetAllRolesWithPermissionsAsync();

        // Get Role permissions
        Task<IEnumerable<Permission>> GetRolePermissionsAsync(Guid roleId);

        // Assign permissions to Role
        Task AssignPermissionsToRoleAsync(Guid roleId, IEnumerable<Guid> permissionIds);

        // Remove permissions from Role
        Task RemovePermissionsFromRoleAsync(Guid roleId, IEnumerable<Guid> permissionIds);

        // Check if User has permission
        Task<bool> HasUserPermissionAsync(Guid userId, string permissionName);

        // Get all permissions
        Task<IEnumerable<Permission>> GetAllPermissionsAsync();
    }
}
