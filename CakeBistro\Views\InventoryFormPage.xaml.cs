using System;
using System.Threading.Tasks;
using CakeBistro.ViewModels;
using CakeBistro.Core.Interfaces;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Maui.Controls;

namespace CakeBistro.Views
{
    [QueryProperty(nameof(InventoryId), "id")]
    public partial class InventoryFormPage : ContentPage
    {
        public int? InventoryId { get; set; }
        private InventoryFormViewModel _viewModel;
        public InventoryFormPage()
        {
            InitializeComponent();
            var inventoryService = MauiProgram.ServiceProvider.GetService<IInventoryService>();
            var rawMaterialService = MauiProgram.ServiceProvider.GetService<IRawMaterialService>();
            _viewModel = new InventoryFormViewModel(inventoryService, rawMaterialService);
            BindingContext = _viewModel;
        }

        protected override async void OnAppearing()
        {
            base.OnAppearing();
            if (InventoryId.HasValue)
            {
                await _viewModel.LoadAsync(InventoryId.Value);
            }
        }
    }
}
