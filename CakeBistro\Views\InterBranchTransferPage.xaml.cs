using Microsoft.Extensions.DependencyInjection;
using Microsoft.Maui.Controls;
using CakeBistro.ViewModels;
using CakeBistro.Core.Interfaces;

namespace CakeBistro.Views
{
    public partial class InterBranchTransferPage : ContentPage
    {
        public InterBranchTransferPage()
        {
            InitializeComponent();
            var transferService = MauiProgram.ServiceProvider.GetService<IInterBranchTransferService>();
            BindingContext = new InterBranchTransferViewModel(transferService);
        }

        private void InitializeComponent() { }
    }
}
