using CakeBistro.Core.Models;
using CakeBistro.Repositories;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace CakeBistro.Services;

public class PurchaseOrderService : IPurchaseOrderService
{
    private readonly IPurchaseOrderRepository _purchaseOrderRepository;
    private readonly ISupplierRepository _supplierRepository;
    private readonly IRawMaterialRepository _rawMaterialRepository;
    
    public PurchaseOrderService(IPurchaseOrderRepository purchaseOrderRepository, ISupplierRepository supplierRepository, IRawMaterialRepository rawMaterialRepository)
    {
        _purchaseOrderRepository = purchaseOrderRepository;
        _supplierRepository = supplierRepository;
        _rawMaterialRepository = rawMaterialRepository;
    }
    
    public async Task<CakeBistro.Core.Models.PurchaseOrder> CreatePurchaseOrderAsync(CakeBistro.Core.Models.PurchaseOrder order)
    {
        // Validate input
        if (order == null)
            throw new ArgumentNullException(nameof(order), "Purchase order cannot be null");

        if (order.SupplierId <= 0)
            throw new ArgumentException("Valid CakeBistro.Core.Models.Supplier ID is required", nameof(order.SupplierId));

        if (order.Items == null || !order.Items.Any())
            throw new ArgumentException("At least one item is required for the purchase order", nameof(order.Items));

        if (order.Items.Any(i => i.RawMaterialId <= 0))
            throw new ArgumentException("Each item must have a valid raw material ID", nameof(order.Items));

        if (order.Items.Any(i => i.Quantity <= 0))
            throw new ArgumentException("Quantity must be greater than zero for all items", nameof(order.Items));

        if (order.Items.Any(i => i.UnitPrice < 0))
            throw new ArgumentException("Unit price cannot be negative", nameof(order.Items));

        try
        {
            // Set default values if not provided
            order.OrderDate ??= DateTime.Today;
            order.Status ??= "Pending";
            
            // Validate CakeBistro.Core.Models.Supplier exists
            var CakeBistro.Core.Models.Supplier = await _supplierRepository.GetByIdAsync(order.SupplierId);
            if (CakeBistro.Core.Models.Supplier == null)
                throw new ArgumentException($"CakeBistro.Core.Models.Supplier with ID {order.SupplierId} not found", nameof(order.SupplierId));
            
            // Validate raw materials exist
            var materialIds = order.Items.Select(i => i.RawMaterialId).Distinct();
            var materials = await _rawMaterialRepository.GetAllAsync();
            var availableMaterials = materials.ToDictionary(m => m.Id, m => m);
            
            foreach (var materialId in materialIds)
            {
                if (!availableMaterials.ContainsKey(materialId))
                    throw new ArgumentException($"Raw material with ID {materialId} not found", nameof(order.Items));
            }
            
            // Calculate total amount
            order.SubTotal = order.Items.Sum(i => i.Quantity * i.UnitPrice);
            
            // Apply discount if any
            if (order.Discount > 0 && order.Discount <= 1)
            {
                order.TotalAmount = order.SubTotal * (1 - order.Discount);
            }
            else
            {
                order.TotalAmount = order.SubTotal;
            }
            
            // Add tax
            if (order.TaxRate > 0 && order.TaxRate <= 1)
            {
                order.TotalAmount += order.TotalAmount * order.TaxRate;
            }
            
            // Round to 2 decimal places
            order.TotalAmount = Math.Round(order.TotalAmount, 2);
            
            // Save the purchase order
            return await _purchaseOrderRepository.AddAsync(order);
        }
        catch (Exception ex)
        {
            // Log the error (in a real application)
            // For now, just rethrow with a more descriptive message
            throw new ApplicationException($"Error creating purchase order: {ex.Message}", ex);
        }
    }
    
    public async Task<CakeBistro.Core.Models.PurchaseOrder> GetPurchaseOrderByIdAsync(int id)
    {
        if (id <= 0)
            throw new ArgumentException("Invalid purchase order ID", nameof(id));
            
        return await _purchaseOrderRepository.GetByIdAsync(id);
    }
    
    public async Task<IEnumerable<CakeBistro.Core.Models.PurchaseOrder>> GetAllPurchaseOrdersAsync()
    {
        return await _purchaseOrderRepository.GetAllAsync();
    }
    
    public async Task<CakeBistro.Core.Models.PurchaseOrder> UpdatePurchaseOrderAsync(CakeBistro.Core.Models.PurchaseOrder order)
    {
        if (order == null)
            throw new ArgumentNullException(nameof(order), "Purchase order cannot be null");
            
        if (order.Id <= 0)
            throw new ArgumentException("Purchase order must have a valid ID", nameof(order.Id));
            
        return await _purchaseOrderRepository.UpdateAsync(order);
    }
    
    public async Task<bool> DeletePurchaseOrderAsync(int id)
    {
        if (id <= 0)
            throw new ArgumentException("Invalid purchase order ID", nameof(id));
            
        var order = await _purchaseOrderRepository.GetByIdAsync(id);
        if (order == null)
            return false;
            
        if (order.Status != "Pending")
            throw new InvalidOperationException($"Cannot delete purchase order that is in '{order.Status}' status");
            
        return await _purchaseOrderRepository.DeleteAsync(id);
    }
    
    public async Task<CakeBistro.Core.Models.PurchaseOrder> SubmitPurchaseOrderAsync(int id)
    {
        if (id <= 0)
            throw new ArgumentException("Invalid purchase order ID", nameof(id));
            
        var order = await _purchaseOrderRepository.GetByIdAsync(id);
        if (order == null)
            throw new KeyNotFoundException($"Purchase order with ID {id} not found");
            
        if (order.Status != "Pending")
            throw new InvalidOperationException($"Cannot submit purchase order that is in '{order.Status}' status");
            
        // Update status to submitted
        order.Status = "Submitted";
        order.SubmittedDate = DateTime.Now;
        
        return await _purchaseOrderRepository.UpdateAsync(order);
    }
    
    public async Task<CakeBistro.Core.Models.PurchaseOrder> ReceivePurchaseOrderAsync(int id)
    {
        if (id <= 0)
            throw new ArgumentException("Invalid purchase order ID", nameof(id));
            
        var order = await _purchaseOrderRepository.GetByIdAsync(id);
        if (order == null)
            throw new KeyNotFoundException($"Purchase order with ID {id} not found");
            
        if (order.Status != "Submitted" && order.Status != "PartiallyReceived")
            throw new InvalidOperationException($"Cannot receive purchase order that is in '{order.Status}' status");
            
        // Update status to received
        order.Status = "Received";
        order.ReceivedDate = DateTime.Now;
        
        // Update inventory with received items
        foreach (var item in order.Items)
        {
            var movement = new StockMovement
            {
                ProductId = item.RawMaterialId,
                Quantity = item.Quantity,
                MovementType = MovementType.Incoming,
                MovementDate = DateTime.UtcNow,
                ReferenceNumber = $"PO-{order.Id}",
                Status = StockMovementStatus.Completed
            };
            
            // If we have expiry date from the purchase order, include it
            if (item.ExpiryDate.HasValue)
            {
                movement.ExpiryDate = item.ExpiryDate.Value;
            }
            
            // Add the stock movement
            await _rawMaterialRepository.RecordStockMovementAsync(movement);
        }
        
        return await _purchaseOrderRepository.UpdateAsync(order);
    }

    public async Task<IEnumerable<CakeBistro.Core.Models.PurchaseOrder>> GetRecentOrdersAsync(int days)
    {
        var startDate = DateTime.UtcNow.AddDays(-days);
        return await _purchaseOrderRepository.GetByDateRangeAsync(startDate, DateTime.UtcNow);
    }
}
