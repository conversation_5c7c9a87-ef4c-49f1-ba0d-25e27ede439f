<?xml version="1.0" encoding="utf-8" ?>
<views:FormTemplate xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
                    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
                    xmlns:views="clr-namespace:CakeBistro.Views"
                    x:Class="CakeBistro.Views.RawMaterialFormFields">
    <StackLayout>
        <Entry Placeholder="Name" Text="{Binding RawMaterial.Name}" AutomationId="RawMaterialNameEntry">
            <Entry.Tooltip>Enter the unique name for this raw material.</Entry.Tooltip>
        </Entry>
        <Entry Placeholder="Category" Text="{Binding RawMaterial.Category}" AutomationId="RawMaterialCategoryEntry">
            <Entry.Tooltip>Specify the category (e.g., Dairy, Flour, etc.).</Entry.Tooltip>
        </Entry>
        <Entry Placeholder="Description" Text="{Binding RawMaterial.Description}" AutomationId="RawMaterialDescriptionEntry">
            <Entry.Tooltip>Optional: Add a description for this material.</Entry.Tooltip>
        </Entry>
        <Entry Placeholder="Unit Type" Text="{Binding RawMaterial.UnitType}" AutomationId="RawMaterialUnitTypeEntry">
            <Entry.Tooltip>e.g., kg, liter, pack, etc.</Entry.Tooltip>
        </Entry>
        <Entry Placeholder="Minimum Threshold" Keyboard="Numeric" Text="{Binding RawMaterial.MinimumThreshold}" AutomationId="RawMaterialMinThresholdEntry">
            <Entry.Tooltip>Alert when stock falls below this value.</Entry.Tooltip>
        </Entry>
        <Entry Placeholder="Reorder Level" Keyboard="Numeric" Text="{Binding RawMaterial.ReorderLevel}" AutomationId="RawMaterialReorderLevelEntry">
            <Entry.Tooltip>Recommended level to reorder stock.</Entry.Tooltip>
        </Entry>
        <Entry Placeholder="Unit Price" Keyboard="Numeric" Text="{Binding RawMaterial.UnitPrice}" AutomationId="RawMaterialUnitPriceEntry">
            <Entry.Tooltip>Cost per unit (e.g., per kg, per pack).</Entry.Tooltip>
        </Entry>
        <Picker Title="Supplier" ItemsSource="{Binding Suppliers}" ItemDisplayBinding="{Binding Name}" SelectedItem="{Binding RawMaterial.Supplier}" AutomationId="RawMaterialSupplierPicker">
            <Picker.Tooltip>Select the supplier for this material.</Picker.Tooltip>
        </Picker>
        <Entry Placeholder="Unit" Text="{Binding RawMaterial.Unit}" AutomationId="RawMaterialUnitEntry">
            <Entry.Tooltip>Measurement unit (e.g., kg, g, L).</Entry.Tooltip>
        </Entry>
        <Entry Placeholder="Price Per Unit" Keyboard="Numeric" Text="{Binding RawMaterial.PricePerUnit}" AutomationId="RawMaterialPricePerUnitEntry">
            <Entry.Tooltip>Enter the price per unit.</Entry.Tooltip>
        </Entry>
        <Entry Placeholder="Current Stock" Keyboard="Numeric" Text="{Binding RawMaterial.CurrentStock}" AutomationId="RawMaterialCurrentStockEntry">
            <Entry.Tooltip>Current available stock.</Entry.Tooltip>
        </Entry>
        <Entry Placeholder="Minimum Stock" Keyboard="Numeric" Text="{Binding RawMaterial.MinimumStock}" AutomationId="RawMaterialMinimumStockEntry">
            <Entry.Tooltip>Minimum required stock to avoid shortages.</Entry.Tooltip>
        </Entry>
        <DatePicker Date="{Binding RawMaterial.LastStockTake}" Format="yyyy-MM-dd" AutomationId="RawMaterialLastStockTakeDatePicker">
            <DatePicker.Tooltip>Date of the last stock take.</DatePicker.Tooltip>
        </DatePicker>
        <Editor Placeholder="Notes" Text="{Binding RawMaterial.Notes}" AutoSize="TextChanges" AutomationId="RawMaterialNotesEditor">
            <Editor.Tooltip>Additional notes or comments.</Editor.Tooltip>
        </Editor>
    </StackLayout>
</views:FormTemplate>
