using CakeBistro.Core.Models;
using CakeBistro.Core.Interfaces;
using Microsoft.EntityFrameworkCore;
using System.Linq.Expressions;

namespace CakeBistro.Repositories
{
    public class AssetRepository : BaseRepository<FixedAsset>, IAssetRepository
    {
        public AssetRepository(CakeBistroContext context) : base(context)
        {
        }

    public async Task<IEnumerable<FixedAsset>> GetByLocationAsync(string location)
    {
        return await _context.FixedAssets
            .Where(a => a.Location == location)
            .ToListAsync();
    }

    public async Task<IEnumerable<DepreciationRecord>> GetDepreciationHistoryAsync(int assetId)
    {
        return await _context.DepreciationRecords
            .Where(d => d.AssetId == assetId)
            .ToListAsync();        }
    }
}
