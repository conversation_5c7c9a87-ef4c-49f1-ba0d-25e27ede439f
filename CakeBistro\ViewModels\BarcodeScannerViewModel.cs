using System.Threading.Tasks;
using System.Windows.Input;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using CakeBistro.Interfaces;

namespace CakeBistro.ViewModels
{
    public partial class BarcodeScannerViewModel : ObservableObject
    {
        private readonly IBarcodeService _barcodeService;

        public BarcodeScannerViewModel(IBarcodeService barcodeService)
        {
            _barcodeService = barcodeService;
            ScanCommand = new Command(async () => await ExecuteScanCommand());
            ManualEntryCommand = new Command(async () => await ExecuteManualEntryCommand());
        }

        public ICommand ScanCommand { get; }
        public ICommand ManualEntryCommand { get; }

        [ObservableProperty]
        private string _scannedBarcode;

        [ObservableProperty]
        private string _barcodeOptions;

        private async Task ExecuteScanCommand()
        {
            var result = await _barcodeService.ScanBarcodeAsync();
            if (!string.IsNullOrEmpty(result))
            {
                ScannedBarcode = result;
                // Handle the scanned barcode (e.g., look up product information)
            }
        }

        private async Task ExecuteManualEntryCommand()
        {
            var result = await _barcodeService.ManualBarcodeEntryAsync();
            if (!string.IsNullOrEmpty(result))
            {
                ScannedBarcode = result;
                // Handle the manually entered barcode
            }
        }
    }
}