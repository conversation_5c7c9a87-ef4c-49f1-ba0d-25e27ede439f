using CakeBistro.Core.Interfaces;
using CakeBistro.Core.Models;
using Microsoft.EntityFrameworkCore;

namespace CakeBistro.Repositories
{
    public class InventoryRepository : BaseRepository<CakeBistro.Core.Models.RawMaterial>, IInventoryRepository
    {
        public InventoryRepository(CakeBistroContext context) : base(context)
        {
        }

        public async Task<IEnumerable<CakeBistro.Core.Models.RawMaterial>> GetLowStockItemsAsync(int minimumQuantity)
        {
            return await _dbSet.Where(rm => rm.StockLevel < minimumQuantity).ToListAsync();
        }

        public async Task<IEnumerable<CakeBistro.Core.Models.RawMaterial>> GetBySupplierAsync(Guid supplierId)
        {
            return await _dbSet.Where(rm => rm.SupplierId == supplierId).ToListAsync();
        }

        public async Task<IEnumerable<StockMovement>> GetStockMovementsAsync(Guid materialId)
        {
            return await _context.StockMovements
                .Where(sm => sm.ProductId == materialId)
                .OrderByDescending(sm => sm.MovementDate)
                .ToListAsync();
        }

        public async Task<CakeBistro.Core.Models.RawMaterial?> GetByBarcodeAsync(string barcode)
        {
            return await _dbSet.FirstOrDefaultAsync(rm => rm.Barcode == barcode);
        }

        public async Task<IEnumerable<InventoryBatch>> GetExpiringBatchesAsync(DateTime beforeDate)
        {
            return await _context.InventoryBatches
                .Where(ib => ib.ExpiryDate <= beforeDate && ib.Status == BatchStatus.Active)
                .Include(ib => ib.CakeBistro.Core.Models.RawMaterial)
                .ToListAsync();
        }
    }

    public class SupplierRepository : BaseRepository<CakeBistro.Core.Models.Supplier>, ISupplierRepository
    {
        public SupplierRepository(CakeBistroContext context) : base(context)
        {
        }

        public async Task<CakeBistro.Core.Models.Supplier?> GetByAccountCodeAsync(string accountCode)
        {
            return await _dbSet.FirstOrDefaultAsync(s => s.AccountCode == accountCode);
        }

        public async Task<IEnumerable<CakeBistro.Core.Models.Supplier>> GetActiveAsync()
        {
            return await _dbSet.Where(s => s.IsActive).ToListAsync();
        }

        public async Task<IEnumerable<CakeBistro.Core.Models.PurchaseOrder>> GetPurchaseOrdersAsync(Guid supplierId)
        {
            return await _context.PurchaseOrders
                .Where(po => po.SupplierId == supplierId)
                .Include(po => po.Items)
                .OrderByDescending(po => po.OrderDate)
                .ToListAsync();
        }
    }

    public class AssetRepository : BaseRepository<Asset>, IAssetRepository
    {
        public AssetRepository(CakeBistroContext context) : base(context)
        {
        }

        public async Task<IEnumerable<Asset>> GetByTypeAsync(string assetType)
        {
            return await _dbSet.Where(a => a.AssetType == assetType).ToListAsync();
        }

        public async Task<IEnumerable<Asset>> GetMaintenanceDueAsync(DateTime beforeDate)
        {
            return await _dbSet.Where(a => a.NextMaintenanceDate <= beforeDate).ToListAsync();
        }

        public async Task<decimal> GetTotalDepreciationAsync(Guid assetId)
        {
            return await _context.Depreciations
                .Where(d => d.AssetId == assetId)
                .SumAsync(d => d.DepreciationAmount);
        }
    }

    public class PurchaseOrderRepository : BaseRepository<CakeBistro.Core.Models.PurchaseOrder>, IPurchaseOrderRepository
    {
        public PurchaseOrderRepository(CakeBistroContext context) : base(context)
        {
        }

        public async Task<IEnumerable<CakeBistro.Core.Models.PurchaseOrder>> GetByStatusAsync(PurchaseOrderStatus status)
        {
            return await _dbSet.Where(po => po.Status == status)
                .Include(po => po.Items)
                .ToListAsync();
        }

        public async Task<IEnumerable<CakeBistro.Core.Models.PurchaseOrder>> GetBySupplierAsync(Guid supplierId)
        {
            return await _dbSet.Where(po => po.SupplierId == supplierId)
                .Include(po => po.Items)
                .OrderByDescending(po => po.OrderDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<CakeBistro.Core.Models.PurchaseOrder>> GetByDateRangeAsync(DateTime startDate, DateTime endDate)
        {
            return await _dbSet.Where(po => po.OrderDate >= startDate && po.OrderDate <= endDate)
                .Include(po => po.Items)
                .OrderByDescending(po => po.OrderDate)
                .ToListAsync();
        }
    }

    public class SalesOrderRepository : BaseRepository<SalesOrder>, ISalesOrderRepository
    {
        public SalesOrderRepository(CakeBistroContext context) : base(context)
        {
        }

        public async Task<IEnumerable<SalesOrder>> GetByStatusAsync(SalesOrderStatus status)
        {
            return await _dbSet.Where(so => so.Status == status)
                .Include(so => so.Items)
                .ToListAsync();
        }

        public async Task<IEnumerable<SalesOrder>> GetByCustomerAsync(Guid customerId)
        {
            return await _dbSet.Where(so => so.CustomerId == customerId)
                .Include(so => so.Items)
                .OrderByDescending(so => so.OrderDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<SalesOrder>> GetByDateRangeAsync(DateTime startDate, DateTime endDate)
        {
            return await _dbSet.Where(so => so.OrderDate >= startDate && so.OrderDate <= endDate)
                .Include(so => so.Items)
                .OrderByDescending(so => so.OrderDate)
                .ToListAsync();
        }
    }
}
