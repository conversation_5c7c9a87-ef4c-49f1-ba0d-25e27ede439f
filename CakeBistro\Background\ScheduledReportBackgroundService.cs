using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using CakeBistro.Core.Models;
using CakeBistro.Services;

namespace CakeBistro.Background;

public class ScheduledReportBackgroundService
{
    private readonly ScheduledReportService _scheduledReportService;
    private readonly ILogisticsService _logisticsService;
    private readonly ReportExportService _reportExportService;
    private readonly IReportingService _reportingService;
    
    public ScheduledReportBackgroundService(
        ScheduledReportService scheduledReportService,
        ILogisticsService logisticsService,
        ReportExportService reportExportService,
        IReportingService reportingService)
    {
        _scheduledReportService = scheduledReportService;
        _logisticsService = logisticsService;
        _reportExportService = reportExportService;
        _reportingService = reportingService;
    }
    
    /// <summary>
    /// Executes the background service
    /// </summary>
    public async Task ExecuteAsync(CancellationToken cancellationToken)
    {
        while (!cancellationToken.IsCancellationRequested)
        {
            await CheckAndGenerateScheduledReportsAsync();
            
            // Wait for 1 hour before checking again
            await Task.Delay(TimeSpan.FromHours(1), cancellationToken);
        }
    }
    
    /// <summary>
    /// Checks for due reports and generates them
    /// </summary>
    private async Task CheckAndGenerateScheduledReportsAsync()
    {
        try
        {
            var dueReports = await GetDueReportsAsync();
            
            foreach (var reportSchedule in dueReports)
            {
                await GenerateAndDistributeReportAsync(reportSchedule);
                
                // Update the schedule for next time
                await UpdateReportScheduleAsync(reportSchedule);
            }
        }
        catch (Exception ex)
        {
            // Log the error
            await HandleErrorAsync(ex);
        }
    }
    
    /// <summary>
    /// Gets all reports that are due to be generated
    /// </summary>
    private async Task<List<ScheduledReport>> GetDueReportsAsync()
    {
        var allSchedules = await _scheduledReportService.GetAllAsync();
        var now = DateTime.Now;
        
        return allSchedules
            .Where(s => s.IsActive && 
                        (s.LastGenerated == null || 
                         ShouldGenerateBasedOnFrequency(s, now)))
            .ToList();
    }
    
    /// <summary>
    /// Determines if a report should be generated based on its frequency
    /// </summary>
    private bool ShouldGenerateBasedOnFrequency(ScheduledReport schedule, DateTime now)
    {
        if (schedule.LastGenerated == null) return true;
        
        switch (schedule.Frequency.ToLower())
        {
            case "once":
                return false; // Already generated once
            case "daily":
                return now.Date > schedule.LastGenerated.Value.Date;
            case "weekly":
                return GetIso8601WeekOfYear(now) > GetIso8601WeekOfYear(schedule.LastGenerated.Value);
            case "monthly":
                return now.Year > schedule.LastGenerated.Value.Year || 
                       (now.Year == schedule.LastGenerated.Value.Year && now.Month > schedule.LastGenerated.Value.Month);
            default:
                return false;
        }
    }
    
    /// <summary>
    /// Gets the ISO 8601 week number of the year
    /// </summary>
    private int GetIso8601WeekOfYear(DateTime date)
    {
        var thursday = date.AddDays(DayOfWeek.Thursday - date.DayOfWeek);
        return (int)Math.Ceiling(((thursday - new DateTime(thursday.Year, 1, 1)).TotalDays + 1) / 7);
    }
    
    /// <summary>
    /// Generates a report and distributes it according to the schedule
    /// </summary>
    private async Task GenerateAndDistributeReportAsync(ScheduledReport schedule)
    {
        try
        {
            // Generate the report based on type
            byte[] reportBytes = await GenerateReportBytesAsync(schedule);
            
            // Distribute the report based on delivery format
            await DistributeReportAsync(schedule, reportBytes);
        }
        catch (Exception ex)
        {
            // Log the error
            await HandleErrorAsync(ex, schedule);
        }
    }
    
    /// <summary>
    /// Generates the report bytes based on the schedule
    /// </summary>
    private async Task<byte[]> GenerateReportBytesAsync(ScheduledReport schedule)
    {
        // Determine the date range for the report
        var endDate = DateTime.Now;
        var startDate = GetStartDateForReport(schedule, endDate);
        
        // Generate the appropriate type of report
        switch (schedule.ReportType.ToLower())
        {
            case "sales summary":
                var salesReport = await _reportingService.GenerateSalesSummaryAsync(startDate, endDate);
                return await _reportExportService.ExportAsync(salesReport, schedule.DeliveryFormat);
            case "financial statement":
                var financialReport = await _reportingService.GenerateFinancialReportAsync(startDate, endDate);
                return await _reportExportService.ExportAsync(financialReport, schedule.DeliveryFormat);
            case "inventory valuation":
                var inventoryReport = await _reportingService.GetInventoryValuationReportAsync();
                return await _reportExportService.ExportAsync(inventoryReport, schedule.DeliveryFormat);
            // Add more cases for other report types as needed
            default:
                throw new ArgumentException($"Unsupported report type: {schedule.ReportType}");
        }
    }
    
    /// <summary>
    /// Gets the start date for a report based on the schedule
    /// </summary>
    private DateTime GetStartDateForReport(ScheduledReport schedule, DateTime endDate)
    {
        switch (schedule.Frequency.ToLower())
        {
            case "daily":
                return endDate.Date;
            case "weekly":
                return endDate.AddDays(-(int)endDate.DayOfWeek + 1); // Monday of current week
            case "monthly":
                return new DateTime(endDate.Year, endDate.Month, 1);
            default:
                return endDate.Date; // Default to same day
        }
    }
    
    /// <summary>
    /// Distributes a report based on delivery format
    /// </summary>
    private async Task DistributeReportAsync(ScheduledReport schedule, byte[] reportBytes)
    {
        switch (schedule.DeliveryFormat.ToLower())
        {
            case "email":
                await SendEmailAsync(schedule.Recipients, schedule.ReportType, reportBytes);
                break;
            case "print":
                await PrintReportAsync(reportBytes);
                break;
            case "fileshare":
                await SaveToFileAsync(schedule.Parameters["FilePath"], reportBytes);
                break;
            // Add more cases as needed
            default:
                throw new ArgumentException($"Unsupported delivery format: {schedule.DeliveryFormat}");
        }
    }
    
    /// <summary>
    /// Sends a report via email
    /// </summary>
    private async Task SendEmailAsync(List<string> recipients, string reportType, byte[] reportBytes)
    {
        // Implementation for sending emails
        // This would typically use an email service
        
        // For now, just log that we would send the email
        Console.WriteLine($"Would send {reportType} report to {string.Join(", ", recipients)}");
    }
    
    /// <summary>
    /// Prints a report
    /// </summary>
    private async Task PrintReportAsync(byte[] reportBytes)
    {
        // Implementation for printing reports
        // This would typically use a printing service
        
        // For now, just log that we would print the report
        Console.WriteLine($"Would print report with {reportBytes.Length} bytes");
    }
    
    /// <summary>
    /// Saves a report to a file
    /// </summary>
    private async Task SaveToFileAsync(string filePath, byte[] reportBytes)
    {
        // Ensure directory exists
        var directory = Path.GetDirectoryName(filePath);
        if (!Directory.Exists(directory))
        {
            Directory.CreateDirectory(directory);
        }
        
        // Add timestamp to filename to avoid overwriting
        var fileName = Path.GetFileNameWithoutExtension(filePath);
        var extension = Path.GetExtension(filePath);
        var newFilePath = $"{fileName}_{DateTime.Now:yyyyMMddHHmmss}{extension}";
        
        // Save the file
        await File.WriteAllBytesAsync(newFilePath, reportBytes);
    }
    
    /// <summary>
    /// Updates a report schedule after generation
    /// </summary>
    private async Task UpdateReportScheduleAsync(ScheduledReport schedule)
    {
        schedule.LastGenerated = DateTime.Now;
        
        // If the schedule is one-time, deactivate it
        if (schedule.Frequency.ToLower() == "once")
        {
            schedule.IsActive = false;
        }
        
        await _scheduledReportService.UpdateAsync(schedule);
    }
    
    /// <summary>
    /// Handles errors during report generation
    /// </summary>
    private async Task HandleErrorAsync(Exception ex, ScheduledReport schedule = null)
    {
        // Log the error
        Console.WriteLine($"Error generating report: {ex.Message}");
        Console.WriteLine(ex.StackTrace);
        
        // You could also send an alert email or store the error in a log
        if (schedule != null)
        {
            Console.WriteLine($"Failed schedule ID: {schedule.Id}");
        }
    }
}
