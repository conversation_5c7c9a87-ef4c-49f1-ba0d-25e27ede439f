<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:vm="clr-namespace:CakeBistro.ViewModels"
             xmlns:model="clr-namespace:CakeBistro.Models"
             x:Class="CakeBistro.Views.NotificationsPage"
             x:DataType="vm:NotificationsViewModel"
             Title="Notifications">

    <Grid RowDefinitions="Auto,*">
        <!-- Header -->
        <HorizontalStackLayout Grid.Row="0"
                             Padding="20,10"
                             Spacing="10">
            <Button Text="Mark All as Read"
                    Command="{Binding MarkAllAsReadCommand}"
                    IsEnabled="{Binding HasUnreadNotifications}"/>
            <ActivityIndicator IsRunning="{Binding IsBusy}"/>
        </HorizontalStackLayout>

        <!-- Notifications List -->
        <RefreshView Grid.Row="1"
                     Command="{Binding LoadNotificationsCommand}"
                     IsRefreshing="{Binding IsBusy}">
            <CollectionView ItemsSource="{Binding Notifications}"
                          EmptyView="No notifications">
                <CollectionView.ItemTemplate>
                    <DataTemplate x:DataType="model:AppNotification">
                        <SwipeView>
                            <SwipeView.RightItems>
                                <SwipeItems>
                                    <SwipeItem Text="Delete"
                                             BackgroundColor="Red"
                                             Command="{Binding Source={RelativeSource AncestorType={x:Type vm:NotificationsViewModel}}, Path=DeleteNotificationCommand}"
                                             CommandParameter="{Binding .}"/>
                                </SwipeItems>
                            </SwipeView.RightItems>

                            <Frame Margin="10,5" 
                                   Padding="15"
                                   BorderColor="{Binding Type, Converter={StaticResource NotificationTypeColorConverter}}"
                                   BackgroundColor="{Binding IsRead, Converter={StaticResource BoolToColorConverter}, ConverterParameter='White|#F5F5F5'}">
                                <Frame.GestureRecognizers>
                                    <TapGestureRecognizer 
                                        Command="{Binding Source={RelativeSource AncestorType={x:Type vm:NotificationsViewModel}}, Path=NotificationTappedCommand}"
                                        CommandParameter="{Binding .}"/>
                                </Frame.GestureRecognizers>

                                <Grid RowDefinitions="Auto,Auto,Auto" ColumnDefinitions="*,Auto">
                                    <!-- Title -->
                                    <Label Grid.Row="0"
                                           Grid.Column="0"
                                           Text="{Binding Title}"
                                           FontAttributes="Bold"
                                           FontSize="16"/>

                                    <!-- Timestamp -->
                                    <Label Grid.Row="0"
                                           Grid.Column="1"
                                           Text="{Binding CreatedAt, StringFormat='{0:MM/dd HH:mm}'}"
                                           FontSize="12"
                                           TextColor="Gray"/>

                                    <!-- Message -->
                                    <Label Grid.Row="1"
                                           Grid.Column="0"
                                           Grid.ColumnSpan="2"
                                           Text="{Binding Message}"
                                           FontSize="14"
                                           LineBreakMode="WordWrap"/>

                                    <!-- Read Status -->
                                    <Label Grid.Row="2"
                                           Grid.Column="0"
                                           Grid.ColumnSpan="2"
                                           Text="{Binding ReadAt, StringFormat='Read: {0:MM/dd HH:mm}'}"
                                           FontSize="12"
                                           TextColor="Gray"
                                           IsVisible="{Binding IsRead}"/>
                                </Grid>
                            </Frame>
                        </SwipeView>
                    </DataTemplate>
                </CollectionView.ItemTemplate>
            </CollectionView>
        </RefreshView>
    </Grid>
</ContentPage>
