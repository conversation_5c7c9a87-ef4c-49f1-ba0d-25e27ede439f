<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:vm="clr-namespace:CakeBistro.ViewModels"
             xmlns:model="clr-namespace:CakeBistro.Models"
             x:Class="CakeBistro.Views.QualityCheckFormPage"
             x:DataType="vm:QualityCheckFormViewModel"
             Title="{Binding Title}">

    <ScrollView>
        <VerticalStackLayout Spacing="10" Padding="20">
            <!-- Batch Selection -->
            <Label Text="Production Batch" />
            <Picker ItemsSource="{Binding AvailableBatches}"
                    ItemDisplayBinding="{Binding BatchNumber}"
                    SelectedItem="{Binding SelectedBatch}"
                    Title="Select Production Batch"/>

            <!-- Check Type -->
            <Label Text="Check Type" />
            <Picker ItemsSource="{Binding CheckTypes}"
                    SelectedItem="{Binding SelectedCheckType}"
                    Title="Select Check Type"/>

            <!-- Parameter -->
            <Label Text="Parameter" />
            <Entry Text="{Binding Parameter}"
                   Placeholder="Enter parameter being checked"/>

            <!-- Measured Value -->
            <Label Text="Measured Value" />
            <Entry Text="{Binding MeasuredValue}"
                   Keyboard="Numeric"
                   Placeholder="Enter measured value"/>

            <!-- Min/Max Values (if applicable) -->
            <Grid ColumnDefinitions="*,*" ColumnSpacing="10">
                <VerticalStackLayout Grid.Column="0">
                    <Label Text="Minimum Value" />
                    <Entry Text="{Binding MinValue}"
                           Keyboard="Numeric"
                           Placeholder="Min value"/>
                </VerticalStackLayout>
                <VerticalStackLayout Grid.Column="1">
                    <Label Text="Maximum Value" />
                    <Entry Text="{Binding MaxValue}"
                           Keyboard="Numeric"
                           Placeholder="Max value"/>
                </VerticalStackLayout>
            </Grid>

            <!-- Expected Result -->
            <Label Text="Expected Result" />
            <Entry Text="{Binding ExpectedResult}"
                   Placeholder="Enter expected result"/>

            <!-- Actual Result -->
            <Label Text="Actual Result" />
            <Entry Text="{Binding ActualResult}"
                   Placeholder="Enter actual result"/>

            <!-- Check Status -->
            <Label Text="Status" />
            <Picker ItemsSource="{Binding StatusOptions}"
                    SelectedItem="{Binding SelectedStatus}"
                    Title="Select Status"/>

            <!-- Notes -->
            <Label Text="Notes" />
            <Editor Text="{Binding Notes}"
                    Placeholder="Enter any additional notes"
                    AutoSize="TextChanges"
                    MaxLength="500"/>

            <!-- Defects (if any) -->
            <Label Text="Defect Description (if applicable)" />
            <Editor Text="{Binding DefectDescription}"
                    Placeholder="Describe any defects found"
                    AutoSize="TextChanges"
                    MaxLength="500"/>

            <!-- Corrective Action -->
            <Label Text="Corrective Action (if needed)" />
            <Editor Text="{Binding CorrectiveAction}"
                    Placeholder="Describe any corrective actions taken"
                    AutoSize="TextChanges"
                    MaxLength="500"/>

            <!-- Save Button -->
            <Button Text="Save Quality Check"
                    Command="{Binding SaveCommand}"
                    IsEnabled="{Binding IsNotBusy}"
                    Margin="0,20,0,20"/>

            <!-- Activity Indicator -->
            <ActivityIndicator IsRunning="{Binding IsBusy}"
                             IsVisible="{Binding IsBusy}"
                             HorizontalOptions="Center"/>
        </VerticalStackLayout>
    </ScrollView>
</ContentPage>
