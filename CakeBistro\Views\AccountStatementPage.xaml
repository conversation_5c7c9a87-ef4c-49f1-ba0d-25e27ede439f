<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2022/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="CakeBistro.Views.AccountStatementPage"
             Title="Account Statement">
    <ContentPage.Content>
        <VerticalStackLayout Spacing="20" Padding="20">
            <Label Text="Account Statement" FontSize="24"/>
            <Entry Placeholder="Account ID" Keyboard="Numeric" Text="{Binding AccountId, Mode=TwoWay}"/>
            <HorizontalStackLayout>
                <Label Text="Start Date:"/>
                <DatePicker Date="{Binding StartDate, Mode=TwoWay}"/>
                <Label Text="End Date:"/>
                <DatePicker Date="{Binding EndDate, Mode=TwoWay}"/>
                <Button Text="Load Statement" Command="{Binding LoadStatementCommand}"/>
            </HorizontalStackLayout>
            <Label Text="Opening Balance:"/>
            <Label Text="{Binding OpeningBalance, StringFormat='Opening: {0:C2}'}"/>
            <Label Text="Closing Balance:"/>
            <Label Text="{Binding ClosingBalance, StringFormat='Closing: {0:C2}'}"/>
            <Button Text="Export to PDF" Command="{Binding ExportStatementCommand}" CommandParameter="pdf"/>
            <Button Text="Export to Excel" Command="{Binding ExportStatementCommand}" CommandParameter="excel"/>
            <Label Text="{Binding ExportResultMessage}" TextColor="Green"/>
            <Label Text="Transactions" FontSize="18"/>
            <CollectionView ItemsSource="{Binding Transactions}">
                <CollectionView.ItemTemplate>
                    <DataTemplate>
                        <Frame Padding="10" Margin="5">
                            <VerticalStackLayout>
                                <Label Text="{Binding Date, StringFormat='Date: {0:yyyy-MM-dd}'}"/>
                                <Label Text="{Binding Description}"/>
                                <Label Text="{Binding Amount, StringFormat='Amount: {0:C2}'}"/>
                                <Label Text="{Binding Type}"/>
                                <Label Text="{Binding RunningBalance, StringFormat='Balance: {0:C2}'}"/>
                            </VerticalStackLayout>
                        </Frame>
                    </DataTemplate>
                </CollectionView.ItemTemplate>
            </CollectionView>
            <Label Text="{Binding StatusMessage}"
                   IsVisible="{Binding StatusMessage, Converter={StaticResource EmptyToFalseConverter}}"
                   TextColor="{Binding StatusColor}" />
        </VerticalStackLayout>
    </ContentPage.Content>
</ContentPage>
