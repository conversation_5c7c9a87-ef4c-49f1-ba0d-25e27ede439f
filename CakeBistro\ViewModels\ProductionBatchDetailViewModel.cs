using System.Collections.ObjectModel;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using CakeBistro.Core.Models;
using CakeBistro.Core.Interfaces;
using System.Linq;
using System.Threading.Tasks;

namespace CakeBistro.ViewModels
{
    public partial class ProductionBatchDetailViewModel : ObservableObject
    {
        private readonly IProductionService _productionService;
        private readonly IReportExportService _reportExportService;

        [ObservableProperty]
        private ProductionBatch _productionBatch;
        [ObservableProperty]
        private Recipe _recipe;
        [ObservableProperty]
        private int _recipeId;
        [ObservableProperty]
        private int _plannedQuantity;
        [ObservableProperty]
        private decimal _plannedCost;
        [ObservableProperty]
        private System.DateTime _plannedStartDate = System.DateTime.Today;
        [ObservableProperty]
        private string _notes;
        [ObservableProperty]
        private bool _isNewBatch;
        [ObservableProperty]
        private ObservableCollection<Recipe> _availableRecipes;
        [ObservableProperty]
        private ObservableCollection<CostBreakdownItem> _costBreakdown = new();
        [ObservableProperty]
        private decimal _expectedRevenue;
        [ObservableProperty]
        private decimal _expectedProfit;
        [ObservableProperty]
        private decimal _profitMargin;
        [ObservableProperty]
        private decimal _overheadCostPerBatch = 0;
        [ObservableProperty]
        private decimal _laborCostPerBatch = 0;
        [ObservableProperty]
        private decimal _utilitiesCostPerBatch = 0;
        [ObservableProperty]
        private decimal _packagingCostPerBatch = 0;
        [ObservableProperty]
        private decimal _wastageCostPerBatch = 0;
        [ObservableProperty]
        private int _actualQuantity;
        [ObservableProperty]
        private decimal _actualRevenue;
        [ObservableProperty]
        private decimal _actualProfit;
        [ObservableProperty]
        private decimal _actualProfitMargin;
        [ObservableProperty]
        private decimal _grossMargin;
        [ObservableProperty]
        private decimal _netMargin;
        [ObservableProperty]
        private decimal _costPerUnit;
        [ObservableProperty]
        private decimal _actualCostPerUnit;
        [ObservableProperty]
        private decimal _batchEfficiency;
        [ObservableProperty]
        private decimal _wastagePercentage;

        public ProductionBatchDetailViewModel(IProductionService productionService, IReportExportService reportExportService)
        {
            _productionService = productionService;
            _reportExportService = reportExportService;
            _availableRecipes = new ObservableCollection<Recipe>();
            IsNewBatch = true;
        }

        partial void OnProductionBatchChanged(ProductionBatch value)
        {
            if (value != null)
            {
                IsNewBatch = false;
                RecipeId = value.RecipeId;
                PlannedQuantity = value.PlannedQuantity;
                PlannedCost = value.PlannedCost;
                PlannedStartDate = value.PlannedStartDate;
                Notes = value.Notes;
            }
        }

        partial void OnRecipeChanged(Recipe value)
        {
            if (value != null)
            {
                RecipeId = value.Id;
            }
        }
        // ...rest of the class unchanged...
    }
}
