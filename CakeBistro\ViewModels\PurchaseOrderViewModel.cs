using System;
using System.Collections.Generic;
using System.Text;
using Xamarin.Forms;

namespace YourNamespace
{
    public class PurchaseOrderViewModel : BaseViewModel
    {
        private string _statusMessage;
        public string StatusMessage
        {
            get => _statusMessage;
            set => SetProperty(ref _statusMessage, value);
        }

        private Color _statusColor = Color.Transparent;
        public Color StatusColor
        {
            get => _statusColor;
            set => SetProperty(ref _statusColor, value);
        }

        public void PlaceOrder()
        {
            try
            {
                // Your order placement logic here

                // On success
                StatusMessage = "Order placed successfully.";
                StatusColor = Color.FromArgb("#388E3C");
            }
            catch (Exception ex)
            {
                // On error
                StatusMessage = $"Error: {ex.Message}";
                StatusColor = Color.FromArgb("#D32F2F");
            }
        }

        public void SelectSupplier()
        {
            // Your supplier selection logic here

            // On info
            StatusMessage = "Please select a supplier.";
            StatusColor = Color.FromArgb("#1976D2");
        }

    }
}