using System;
using System.Collections.Generic;

namespace CakeBistro.Core.Models {
    public class InventoryValuationReport : BaseEntity {
        public DateTime ReportDate { get; set; }
        public decimal TotalInventoryValue { get; set; }
        public int TotalItemCount { get; set; }
        public int LowStockItemCount { get; set; }
        public int OutOfStockItemCount { get; set; }
        public int CriticalStockItemCount { get; set; }
        public string Notes { get; set; } = string.Empty;
        public List<string> Recommendations { get; set; } = new();
        public ICollection<InventoryItemValuation> ItemValuations { get; set; } = new List<InventoryItemValuation>();
    }
}
