using System;
using System.Collections.Generic;
using System.IO;
using System.Text.Json;
using System.Threading.Tasks;
using CakeBistro.Core.Models;

namespace CakeBistro.Services;

public class ScheduledReportService
{
    private readonly string _storagePath = Path.Combine(FileSystem.Current.AppDataDirectory, "scheduled_reports.json");

    public async Task<List<ScheduledReport>> GetAllAsync()
    {
        if (!File.Exists(_storagePath)) return new List<ScheduledReport>();
        var json = await File.ReadAllTextAsync(_storagePath);
        return JsonSerializer.Deserialize<List<ScheduledReport>>(json) ?? new List<ScheduledReport>();
    }

    public async Task SaveAsync(List<ScheduledReport> reports)
    {
        var json = JsonSerializer.Serialize(reports);
        await File.WriteAllTextAsync(_storagePath, json);
    }

    public async Task AddAsync(ScheduledReport report)
    {
        var reports = await GetAllAsync();
        report.Id = reports.Count > 0 ? reports[^1].Id + 1 : 1;
        reports.Add(report);
        await SaveAsync(reports);
    }
    
    /// <summary>
    /// Updates an existing scheduled report
    /// </summary>
    public async Task UpdateAsync(ScheduledReport report)
    {
        var reports = await GetAllAsync();
        var index = reports.FindIndex(r => r.Id == report.Id);
        if (index >= 0)
        {
            reports[index] = report;
            await SaveAsync(reports);
        }
    }
}
