using CakeBistro.Models;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace CakeBistro.Services
{
    public interface IReportingService
    {
        Task<SalesSummaryReport> GenerateSalesSummaryReportAsync(DateTime startDate, DateTime endDate);
        Task<VehicleReport> GenerateVehicleReportAsync(DateTime startDate, DateTime endDate);
        Task<DistributionReport> GenerateDistributionReportAsync(DateTime startDate, DateTime endDate);
        Task<AnalysisReport> GenerateAnalysisReportAsync(DateTime startDate, DateTime endDate);
        Task<StockValuation> GetStockValuationAsync(DateTime asOfDate);
        Task<ExpiringBatchReport> GetExpiringBatchReportAsync(DateTime asOfDate);
        Task<AccountStatementReport> GetAccountStatementReportAsync(int accountId, DateTime startDate, DateTime endDate);
        Task<DebtorList> GetDebtorListAsync();
        Task<CreditorList> GetCreditorListAsync();
    }
}
