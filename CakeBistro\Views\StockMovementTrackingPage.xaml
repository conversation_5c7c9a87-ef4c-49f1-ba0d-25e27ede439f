<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:controls="clr-namespace:CakeBistro.Controls"
             xmlns:viewModels="clr-namespace:CakeBistro.ViewModels"
             x:Class="CakeBistro.Views.StockMovementTrackingPage"
             Title="Stock Movement Tracking">
    <ContentPage.BindingContext>
        <viewModels:StockMovementTrackingViewModel />
    </ContentPage.BindingContext>
    <VerticalStackLayout Padding="20" Spacing="16">
        <controls:CardView>
            <StackLayout>
                <Label Text="Material" FontAttributes="Bold"/>
                <Picker ItemsSource="{Binding Materials}"
                        ItemDisplayBinding="{Binding Name}"
                        SelectedItem="{Binding SelectedMaterial, Mode=TwoWay}" />
                <Label Text="Quantity" FontAttributes="Bold"/>
                <Entry Text="{Binding Quantity, Mode=TwoWay}" Placeholder="Enter quantity" Keyboard="Numeric" />
                <Label Text="Movement Type" FontAttributes="Bold"/>
                <Picker SelectedItem="{Binding MovementType, Mode=TwoWay}">
                    <Picker.ItemsSource>
                        <x:Array Type="x:String">
                            <x:String>Incoming</x:String>
                            <x:String>Outgoing</x:String>
                        </x:Array>
                    </Picker.ItemsSource>
                </Picker>
                <Button Text="Record Movement"
                        Command="{Binding RecordMovementCommand}"
                        StyleClass="PrimaryButton AnimatedButton" />
            </StackLayout>
        </controls:CardView>
        <CollectionView ItemsSource="{Binding Movements}">
            <!-- Define item template as needed -->
        </CollectionView>
    </VerticalStackLayout>
</ContentPage>
