using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace CakeBistro.Core.Models
{
    public class User : BaseEntity
    {
        [Required]
        public string Username { get; set; } = string.Empty;
        [Required]
        public string Email { get; set; } = string.Empty;
        [Required]
        public string PasswordHash { get; set; } = string.Empty;
        public string FirstName { get; set; } = string.Empty;
        public string LastName { get; set; } = string.Empty;
        public bool IsActive { get; set; } = true;
        public DateTime? LastLoginDate { get; set; }
        public Guid? RoleId { get; set; }
        public Role? Role { get; set; }
        public Guid? CreatedBy { get; set; }
        public Guid? UpdatedBy { get; set; }
        public DateTime CreatedDate { get; set; } = DateTime.UtcNow;
        public DateTime UpdatedDate { get; set; } = DateTime.UtcNow;
        public ICollection<UserActivity> ActivityLogs { get; set; } = new List<UserActivity>();
        public ICollection<UserPermission> UserPermissions { get; set; } = new List<UserPermission>();
    }
    public class Role : BaseEntity
    {
        [Required]
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public bool IsSystemRole { get; set; }
        public ICollection<User> Users { get; set; } = new List<User>();
    }
}
