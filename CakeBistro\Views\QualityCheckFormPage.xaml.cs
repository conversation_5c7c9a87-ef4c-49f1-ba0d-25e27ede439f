namespace CakeBistro.Views;

public partial class QualityCheckFormPage : ContentPage
{
    private readonly QualityCheckFormViewModel _viewModel;

    public QualityCheckFormPage(QualityCheckFormViewModel viewModel)
    {
        InitializeComponent();
        _viewModel = viewModel;
        BindingContext = viewModel;
    }

    protected override async void OnAppearing()
    {
        base.OnAppearing();
        await _viewModel.Initialize();
    }
}
