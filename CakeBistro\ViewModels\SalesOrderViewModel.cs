using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using CakeBistro.Core.Models;
using CakeBistro.Core.Interfaces;
using System.Threading.Tasks;
using System.Windows.Media; // Add this using directive for Color

namespace CakeBistro.ViewModels
{
    public class SalesOrderViewModel : INotifyPropertyChanged
    {
        private readonly ISalesService _salesService;

        public event PropertyChangedEventHandler PropertyChanged;

        protected void OnPropertyChanged([CallerMemberName] string name = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(name));
        }

        // ...existing properties, methods, and logic, minus any direct UI dependencies...

        private Color _statusColor = Colors.Transparent;
        public Color StatusColor
        {
            get => _statusColor;
            set => SetProperty(ref _statusColor, value);
        }

        // Example properties for StatusMessage and Order
        private string _statusMessage;
        public string StatusMessage
        {
            get => _statusMessage;
            set => SetProperty(ref _statusMessage, value);
        }

        private SalesOrder _order;
        public SalesOrder Order
        {
            get => _order;
            set => SetProperty(ref _order, value);
        }

        // Example method that sets StatusMessage and StatusColor
        public void PlaceOrder()
        {
            try
            {
                // ...code to place order...

                StatusMessage = "Order placed successfully.";
                StatusColor = Color.FromArgb("#388E3C"); // Green color for success
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error: {ex.Message}";
                StatusColor = Color.FromArgb("#D32F2F"); // Red color for error
            }
        }

        // Example method that checks order and sets StatusMessage and StatusColor
        public void CheckOrder()
        {
            if (Order == null)
            {
                StatusMessage = "Please select a customer.";
                StatusColor = Color.FromArgb("#1976D2"); // Blue color for info
            }
            else
            {
                // ...code to check order...
            }
        }
    }
}
