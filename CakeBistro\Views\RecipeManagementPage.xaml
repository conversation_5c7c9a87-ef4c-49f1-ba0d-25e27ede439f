<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:controls="clr-namespace:CakeBistro.Controls"
             xmlns:viewModels="clr-namespace:CakeBistro.ViewModels"
             x:Class="CakeBistro.Views.RecipeManagementPage"
             Title="Recipe Management">
    <ContentPage.BindingContext>
        <viewModels:RecipeManagementViewModel />
    </ContentPage.BindingContext>
    <VerticalStackLayout Padding="20" Spacing="16">
        <controls:CardView>
            <StackLayout>
                <Label Text="Recipe Name" FontAttributes="Bold"/>
                <Entry Text="{Binding RecipeName, Mode=TwoWay}" Placeholder="Enter recipe name" />
                <Label Text="Product" FontAttributes="Bold"/>
                <Picker ItemsSource="{Binding Products}"
                        ItemDisplayBinding="{Binding Name}"
                        SelectedItem="{Binding SelectedProduct, Mode=TwoWay}" />
                <Label Text="Ingredients" FontAttributes="Bold"/>
                <CollectionView ItemsSource="{Binding Ingredients}">
                    <!-- Define item template as needed -->
                </CollectionView>
                <Button Text="Add Ingredient"
                        Command="{Binding AddIngredientCommand}"
                        StyleClass="SecondaryButton AnimatedButton" />
                <Button Text="Save Recipe"
                        Command="{Binding SaveRecipeCommand}"
                        StyleClass="PrimaryButton AnimatedButton" />
            </StackLayout>
        </controls:CardView>
    </VerticalStackLayout>
</ContentPage>
