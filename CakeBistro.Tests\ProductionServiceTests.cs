// Create ProductionServiceTests.cs
using CakeBistro.Services;
using CakeBistro.Core.Models;
using CakeBistro.Repositories;
using Microsoft.EntityFrameworkCore;
using Xunit;

namespace CakeBistro.Tests;

public class ProductionServiceTests
{
    private readonly CakeBistroContext _context;
    private readonly IProductionService _productionService;
    private readonly IRepository<Product> _productRepository;
    private readonly IInventoryRepository _inventoryRepository;
    private readonly IRepository<InventoryBatch> _batchRepository;

    public ProductionServiceTests()
    {
        // Set up in-memory database
        var options = new DbContextOptionsBuilder<CakeBistroContext>()
            .UseInMemoryDatabase(databaseName: "TestDatabase")
            .Options;

        _context = new CakeBistroContext(options);
        
        // Initialize repositories
        _productRepository = new ProductRepository(_context);
        _inventoryRepository = new InventoryRepository(_context);
        _batchRepository = new BaseRepository<InventoryBatch>(_context);
        
        // Initialize service with test repositories
        _productionService = new ProductionService(
            _productRepository,
            _inventoryRepository,
            _batchRepository);
    }

    [Fact]
    public async Task AddProductAsync_ShouldAddProduct()
    {
        // Arrange
        var product = new Product
        {
            Name = "Test Product",
            Description = "Test Description",
            Price = 20.0m,
            CurrentStock = 50
        };

        // Act
        var result = await _productionService.AddProductAsync(product);
        
        // Assert
        Assert.NotNull(result);
        Assert.Equal("Test Product", result.Name);
        Assert.True(result.Id > 0);
    }

    [Fact]
    public async Task GetProductByIdAsync_ShouldReturnProduct()
    {
        // Arrange
        var product = new Product
        {
            Name = "Test Product",
            Description = "Test Description",
            Price = 20.0m,
            CurrentStock = 50
        };
        
        var addedProduct = await _productionService.AddProductAsync(product);
        
        // Act
        var result = await _productionService.GetProductByIdAsync(addedProduct.Id);
        
        // Assert
        Assert.NotNull(result);
        Assert.Equal(addedProduct.Id, result.Id);
        Assert.Equal("Test Product", result.Name);
    }

    [Fact]
    public async Task UpdateProductAsync_ShouldUpdateProduct()
    {
        // Arrange
        var product = new Product
        {
            Name = "Test Product",
            Description = "Test Description",
            Price = 20.0m,
            CurrentStock = 50
        };
        
        var addedProduct = await _productionService.AddProductAsync(product);
        
        // Modify product
        addedProduct.Name = "Updated Product";
        addedProduct.Price = 25.0m;
        
        // Act
        await _productionService.UpdateProductAsync(addedProduct);
        
        // Get updated product
        var result = await _productionService.GetProductByIdAsync(addedProduct.Id);
        
        // Assert
        Assert.NotNull(result);
        Assert.Equal("Updated Product", result.Name);
        Assert.Equal(25.0m, result.Price);
    }

    [Fact]
    public async Task DeleteProductAsync_ShouldRemoveProduct()
    {
        // Arrange
        var product = new Product
        {
            Name = "Test Product",
            Description = "Test Description",
            Price = 20.0m,
            CurrentStock = 50
        };
        
        var addedProduct = await _productionService.AddProductAsync(product);
        
        // Act
        await _productionService.DeleteProductAsync(addedProduct.Id);
        
        // Try to get deleted product
        var result = await _productionService.GetProductByIdAsync(addedProduct.Id);
        
        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task CalculateRecipeCostAsync_ShouldCalculateCorrectly()
    {
        // Arrange
        var product = new Product
        {
            Name = "Test Product",
            Description = "Test Description",
            Price = 20.0m,
            CurrentStock = 50,
            RawMaterials = new List<RawMaterialProduct>
            {
                new RawMaterialProduct { Quantity = 2, RawMaterial = new RawMaterial { Name = "Flour", PricePerUnit = 1.0m } },
                new RawMaterialProduct { Quantity = 1, RawMaterial = new RawMaterial { Name = "Sugar", PricePerUnit = 2.0m } }
            }
        };
        
        // Act
        var cost = await _productionService.CalculateRecipeCostAsync(product);
        
        // Assert
        Assert.Equal(4.0m, cost); // (2 * 1.0) + (1 * 2.0) = 4.0
    }

    [Fact]
    public async Task CreateRecipeAsync_ShouldCreateRecipe()
    {
        // Arrange
        var recipe = new Recipe
        {
            Name = "Test Recipe",
            Description = "Test Description",
            ProductId = 1,
            BatchSize = 10,
            EstimatedCostPerBatch = 100m,
            RecipeItems = new List<RecipeItem>
            {
                new RecipeItem { RawMaterialId = 1, Quantity = 2 }
            }
        };

        // Act
        var result = await _productionService.CreateRecipeAsync(recipe);

        // Assert
        Assert.NotNull(result);
        Assert.Equal("Test Recipe", result.Name);
        Assert.True(result.Id > 0);
    }

    [Fact]
    public async Task StartProductionBatchAsync_WithInsufficientStock_ShouldReturnError()
    {
        // Arrange
        var recipe = new Recipe
        {
            Name = "Test Recipe",
            ProductId = 1,
            BatchSize = 10,
            RecipeItems = new List<RecipeItem>
            {
                new RecipeItem
                {
                    RawMaterialId = 1,
                    Quantity = 2,
                    RawMaterial = new RawMaterial { Name = "Test Material" }
                }
            }
        };
        await _productionService.CreateRecipeAsync(recipe);

        var batch = new ProductionBatch
        {
            RecipeId = recipe.Id,
            PlannedQuantity = 10,
            PlannedStartDate = DateTime.Today,
            Status = "Planned"
        };
        var createdBatch = await _productionService.CreateProductionBatchAsync(batch);

        // Set up insufficient stock
        var rawMaterial = new RawMaterial
        {
            Id = 1,
            Name = "Test Material",
            CurrentStock = 5 // Not enough for 10 batches * 2 units
        };
        await _context.RawMaterials.AddAsync(rawMaterial);
        await _context.SaveChangesAsync();

        // Act
        var (success, message) = await _productionService.StartProductionBatchAsync(createdBatch.Id);

        // Assert
        Assert.False(success);
        Assert.Contains("Insufficient stock", message);
    }

    [Fact]
    public async Task CompleteProductionBatchAsync_WithValidData_ShouldUpdateStockAndStatus()
    {
        // Arrange
        var recipe = await SetupRecipeWithStock();
        var batch = new ProductionBatch
        {
            RecipeId = recipe.Id,
            PlannedQuantity = 10,
            PlannedStartDate = DateTime.Today,
            Status = "InProgress"
        };
        var createdBatch = await _productionService.CreateProductionBatchAsync(batch);

        // Act
        var (success, message) = await _productionService.CompleteProductionBatchAsync(createdBatch.Id, 8);

        // Assert
        Assert.True(success);
        Assert.Contains("completed successfully", message);

        var updatedBatch = await _productionService.GetProductionBatchByIdAsync(createdBatch.Id);
        Assert.Equal("Completed", updatedBatch.Status);
        Assert.Equal(8, updatedBatch.ActualQuantity);
        Assert.NotNull(updatedBatch.CompletionDate);
    }

    [Fact]
    public async Task CalculateProductionCostAsync_ValidProduct_ReturnsCalculatedCost()
    {
        // Arrange
        var product = new Product
        {
            Name = "Chocolate Cake",
            Price = 25.0m,
            RawMaterials = new List<RawMaterialProduct>
            {
                new RawMaterialProduct { Quantity = 2, RawMaterial = new RawMaterial { Name = "Flour", PricePerUnit = 1.0m } },
                new RawMaterialProduct { Quantity = 1, RawMaterial = new RawMaterial { Name = "Sugar", PricePerUnit = 2.0m } }
            }
        };
        
        await _productionService.AddProductAsync(product);

        // Act
        var result = await _productionService.CalculateProductionCostAsync(product.Id);

        // Assert
        Assert.Equal(4.0m, result); // (2 * 1.0) + (1 * 2.0) = 4.0
    }

    [Fact]
    public async Task CalculateProductionCostAsync_InvalidProductId_ThrowsException()
    {
        // Act & Assert
        await Assert.ThrowsAsync<InvalidOperationException>(() => 
            _productionService.CalculateProductionCostAsync(999));
    }

    [Fact]
    public async Task AnalyzeProfitabilityAsync_ValidProduct_ReturnsProfitabilityAnalysis()
    {
        // Arrange
        var product = new Product
        {
            Name = "Vanilla Cake",
            Price = 20.0m,
            RawMaterials = new List<RawMaterialProduct>
            {
                new RawMaterialProduct { Quantity = 0.8m, RawMaterial = new RawMaterial { Name = "Flour", PricePerUnit = 2.5m } },
                new RawMaterialProduct { Quantity = 0.4m, RawMaterial = new RawMaterial { Name = "Butter", PricePerUnit = 5.0m } }
            }
        };
        
        await _productionService.AddProductAsync(product);

        // Act
        var result = await _productionService.AnalyzeProfitabilityAsync(product.Id);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(product.Id, result.ProductId);
        Assert.Equal(product.Price, result.SalePrice);
        
        // Calculate expected values
        decimal rawMaterialCost = 0;
        foreach (var ingredient in product.RawMaterials)
        {
            rawMaterialCost += ingredient.Quantity * ingredient.RawMaterial.PricePerUnit;
        }
        
        decimal laborCost = 2 * 15.0m; // Assuming 2 hours of labor at $15/hour
        decimal overheadCost = (rawMaterialCost + laborCost) * 0.2m; // 20% overhead
        decimal expectedTotalCost = rawMaterialCost + laborCost + overheadCost;
        decimal expectedMargin = product.Price == 0 ? 0 : ((product.Price - expectedTotalCost) / product.Price) * 100;
        
        // Allow for small floating point differences
        Assert.True(Math.Abs(expectedTotalCost - result.ProductionCost) < 0.01m);
        Assert.True(Math.Abs(expectedMargin - result.ProfitMarginPercentage) < 0.01m);
    }

    [Fact]
    public async Task RegisterDamageAsync_ValidReport_ReducesBatchStock()
    {
        // Arrange
        var product = new Product
        {
            Name = "Strawberry Cake",
            Price = 22.0m
        };
        
        var addedProduct = await _productionService.AddProductAsync(product);
        
        var batch = new InventoryBatch
        {
            ProductId = addedProduct.Id,
            Quantity = 50,
            BatchNumber = "BATCH-TEST123",
            ExpiryDate = DateTime.UtcNow.AddDays(7),
            Status = BatchStatus.Active
        };
        
        await _context.InventoryBatches.AddAsync(batch);
        await _context.SaveChangesAsync();

        var damageReport = new DamageReport
        {
            BatchId = batch.Id,
            Quantity = 5,
            Reason = "Quality issues during packaging",
            ReportDate = DateTime.UtcNow
        };

        // Act
        var result = await _productionService.RegisterDamageAsync(damageReport);

        // Assert
        Assert.True(result);
        
        // Verify batch was updated
        var updatedBatch = await _context.InventoryBatches.FindAsync(batch.Id);
        Assert.NotNull(updatedBatch);
        Assert.Equal(45, updatedBatch.Quantity);
        Assert.Equal(DateTime.UtcNow.Date, updatedBatch.UpdatedDate?.Date);
    }

    [Fact]
    public async Task UpdateFinishedStockAsync_ValidBatch_UpdatesProductStock()
    {
        // Arrange
        var product = new Product
        {
            Name = "Cheesecake",
            Price = 28.0m,
            CurrentStock = 0
        };
        
        var addedProduct = await _productionService.AddProductAsync(product);
        
        var batch = new InventoryBatch
        {
            ProductId = addedProduct.Id,
            Quantity = 30,
            BatchNumber = "BATCH-PROD123",
            ExpiryDate = DateTime.UtcNow.AddDays(7),
            Status = BatchStatus.Active
        };
        
        await _context.InventoryBatches.AddAsync(batch);
        await _context.SaveChangesAsync();

        // Act
        var result = await _productionService.UpdateFinishedStockAsync(batch.Id);

        // Assert
        Assert.True(result);
        
        // Verify product stock was updated
        var updatedProduct = await _productionService.GetProductByIdAsync(addedProduct.Id);
        Assert.NotNull(updatedProduct);
        Assert.Equal(30, updatedProduct.CurrentStock);
        Assert.Equal(DateTime.UtcNow.Date, updatedProduct.UpdatedDate?.Date);
        
        // Verify batch was marked as processed
        var updatedBatch = await _context.InventoryBatches.FindAsync(batch.Id);
        Assert.NotNull(updatedBatch);
        Assert.True(updatedBatch.IsProcessed);
        Assert.NotNull(updatedBatch.ProcessedDate);
    }

    [Fact]
    public async Task TransferToPackingAsync_ValidRequest_CreatesTransferAndUpdatesStock()
    {
        // Arrange
        var product = new Product
        {
            Name = "Red Velvet Cake",
            Price = 24.0m,
            CurrentStock = 100
        };
        
        var addedProduct = await _productionService.AddProductAsync(product);

        var transferRequest = new TransferRequest
        {
            ProductId = addedProduct.Id,
            Quantity = 20,
            DestinationDepartment = "Packing",
            ReasonForTransfer = "Scheduled packing"
        };

        // Act
        var result = await _productionService.TransferToPackingAsync(transferRequest);

        // Assert
        Assert.NotNull(result);
        Assert.Equal(transferRequest.ProductId, result.ProductId);
        Assert.Equal(transferRequest.Quantity, result.Quantity);
        Assert.Equal(transferRequest.DestinationDepartment, result.DestinationLocation);
        Assert.Equal(TransferStatus.Pending, result.Status);
        Assert.NotEmpty(result.ReferenceNumber);
        
        // Verify product stock was reduced
        var updatedProduct = await _productionService.GetProductByIdAsync(addedProduct.Id);
        Assert.NotNull(updatedProduct);
        Assert.Equal(80, updatedProduct.CurrentStock);
        Assert.Equal(DateTime.UtcNow.Date, updatedProduct.UpdatedDate?.Date);
        
        // Verify batch was created
        var batch = await _context.InventoryBatches
            .FirstOrDefaultAsync(b => b.BatchId == result.BatchId);
        Assert.NotNull(batch);
        Assert.Equal(addedProduct.Id, batch.ProductId);
        Assert.Equal(transferRequest.Quantity, batch.Quantity);
        Assert.Equal(transferRequest.ReasonForTransfer, batch.ReasonForTransfer);
        Assert.Equal(DateTime.UtcNow.Date, batch.CreatedDate.Date);
        Assert.Equal(BatchStatus.PendingTransfer, batch.Status);
    }

    private async Task<Recipe> SetupRecipeWithStock()
    {
        // Create raw material with stock
        var rawMaterial = new RawMaterial
        {
            Name = "Test Material",
            CurrentStock = 100
        };
        await _context.RawMaterials.AddAsync(rawMaterial);
        await _context.SaveChangesAsync();

        // Create product
        var product = new Product
        {
            Name = "Test Product",
            Price = 20.0m
        };
        await _productionService.AddProductAsync(product);

        // Create recipe
        var recipe = new Recipe
        {
            Name = "Test Recipe",
            ProductId = product.Id,
            BatchSize = 10,
            RecipeItems = new List<RecipeItem>
            {
                new RecipeItem
                {
                    RawMaterialId = rawMaterial.Id,
                    Quantity = 2
                }
            }
        };
        return await _productionService.CreateRecipeAsync(recipe);
    }
}