<?xml version="1.0" encoding="utf-8" ?>
<ContentView xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="CakeBistro.Views.InterBranchTransferFormFields"
             xmlns:models="clr-namespace:CakeBistro.Models">
    <StackLayout Spacing="16" Padding="10">
        <!-- Raw Material Selector -->
        <Picker Title="Raw Material"
                ItemsSource="{Binding RawMaterials}"
                SelectedItem="{Binding SelectedRawMaterial}"
                ItemDisplayBinding="{Binding Name}"
                AutomationId="RawMaterialPicker" />
        <Label Text="{Binding RawMaterialError}" TextColor="Red" IsVisible="{Binding RawMaterialError, Converter={StaticResource StringToBooleanConverter}}" />
        
        <!-- From Branch Selector -->
        <Picker Title="From Branch"
                ItemsSource="{Binding Branches}"
                SelectedItem="{Binding FromBranch}"
                ItemDisplayBinding="{Binding Name}"
                AutomationId="FromBranchPicker" />
        <Label Text="{Binding FromBranchError}" TextColor="Red" IsVisible="{Binding FromBranchError, Converter={StaticResource StringToBooleanConverter}}" />
        
        <!-- To Branch Selector -->
        <Picker Title="To Branch"
                ItemsSource="{Binding Branches}"
                SelectedItem="{Binding ToBranch}"
                ItemDisplayBinding="{Binding Name}"
                AutomationId="ToBranchPicker" />
        <Label Text="{Binding ToBranchError}" TextColor="Red" IsVisible="{Binding ToBranchError, Converter={StaticResource StringToBooleanConverter}}" />
        
        <!-- Transfer Date -->
        <DatePicker Date="{Binding TransferDate}" AutomationId="TransferDatePicker" />
        
        <!-- Quantity -->
        <Entry Placeholder="Quantity" Text="{Binding Quantity}" Keyboard="Numeric" AutomationId="QuantityEntry" />
        <Label Text="{Binding QuantityError}" TextColor="Red" IsVisible="{Binding QuantityError, Converter={StaticResource StringToBooleanConverter}}" />
        
        <!-- Notes -->
        <Editor Placeholder="Notes" Text="{Binding Notes}" HeightRequest="80" AutomationId="NotesEditor" />
    </StackLayout>
</ContentView>
