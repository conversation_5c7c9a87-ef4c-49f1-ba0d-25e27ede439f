using System;
using System.ComponentModel.DataAnnotations;

namespace CakeBistro.Core.Models
{
    public class UserPermission : BaseEntity
    {
        [Required]
        public Guid UserId { get; set; }
        [Required]
        public Guid PermissionId { get; set; }
        [Required]
        public DateTime AssignedDate { get; set; } = DateTime.UtcNow;
        [Required]
        public bool IsActive { get; set; } = true;
        public Guid? CreatedBy { get; set; }
        public DateTime CreatedDate { get; set; } = DateTime.UtcNow;
        public Guid? UpdatedBy { get; set; }
        public DateTime? UpdatedDate { get; set; }
        public User? User { get; set; }
        public Permission? Permission { get; set; }
    }
}
