/// <summary>
/// Represents a template for report generation
/// </summary>
public class ReportTemplate
{
    /// <summary>
    /// Gets or sets the template ID
    /// </summary>
    public int Id { get; set; }
    
    /// <summary>
    /// Gets or sets the primary color for the template
    /// </summary>
    public string PrimaryColor { get; set; }
    
    /// <summary>
    /// Gets or sets the secondary color for the template
    /// </summary>
    public string SecondaryColor { get; set; }
    
    /// <summary>
    /// Gets or sets the accent color for the template
    /// </summary>
    public string AccentColor { get; set; }
    
    /// <summary>
    /// Gets or sets the font family for report headers
    /// </summary>
    public string HeaderFont { get; set; }
    
    /// <summary>
    /// Gets or sets the font family for report body text
    /// </summary>
    public string BodyFont { get; set; }
    
    /// <summary>
    /// Gets or sets the font size for report headers
    /// </summary>
    public double HeaderFontSize { get; set; }
    
    /// <summary>
    /// Gets or sets the font size for report body text
    /// </summary>
    public double BodyFontSize { get; set; }
    
    /// <summary>
    /// Gets or sets the company logo to include in the report
    /// </summary>
    public byte[] CompanyLogo { get; set; }
    
    /// <summary>
    /// Gets or sets the report footer text
    /// </summary>
    public string FooterText { get; set; }
}