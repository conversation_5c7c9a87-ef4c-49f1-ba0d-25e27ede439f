// Create InventoryServiceTests.cs
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using CakeBistro.Models;
using CakeBistro.Repositories;
using CakeBistro.Services;
using Microsoft.EntityFrameworkCore;
using Xunit;

namespace CakeBistro.Tests
{
    public class InventoryServiceTests
    {
        private readonly CakeBistroContext _context;
        private readonly IInventoryService _inventoryService;

        public InventoryServiceTests()
        {
            // Set up in-memory database for testing
            var options = new DbContextOptionsBuilder<CakeBistroContext>()
                .UseInMemoryDatabase(databaseName: $"InventoryTestDb_{Guid.NewGuid()}")
                .Options;

            _context = new CakeBistroContext(options);
            var repository = new Repository<RawMaterial>(_context);
            _inventoryService = new InventoryService(_context);
        }

        [Fact]
        public async Task RegisterRawMaterialAsync_ValidMaterial_ReturnsMaterialWithId()
        {
            // Arrange
            var material = new RawMaterial
            {
                Name = "Flour",
                Unit = "kg",
                PricePerUnit = 2.5m
            };

            // Act
            var result = await _inventoryService.RegisterRawMaterialAsync(material);

            // Assert
            Assert.NotNull(result);
            Assert.NotEqual(Guid.Empty, result.Id);
            Assert.Equal("Flour", result.Name);
        }

        [Fact]
        public async Task RegisterRawMaterialAsync_NullMaterial_ThrowsBusinessRuleValidationException()
        {
            // Act & Assert
            await Assert.ThrowsAsync<BusinessRuleValidationException>(() => 
                _inventoryService.RegisterRawMaterialAsync(null));
        }

        [Fact]
        public async Task GetRawMaterialByIdAsync_ExistingId_ReturnsMaterial()
        {
            // Arrange
            var material = new RawMaterial
            {
                Name = "Sugar",
                Unit = "kg",
                PricePerUnit = 3.0m
            };
            
            await _context.RawMaterials.AddAsync(material);
            await _context.SaveChangesAsync();

            // Act
            var result = await _inventoryService.GetRawMaterialByIdAsync(material.Id);

            // Assert
            Assert.NotNull(result);
            Assert.Equal("Sugar", result.Name);
        }

        [Fact]
        public async Task GetRawMaterialByIdAsync_NonExistentId_ThrowsBusinessRuleValidationException()
        {
            // Act & Assert
            await Assert.ThrowsAsync<BusinessRuleValidationException>(() => 
                _inventoryService.GetRawMaterialByIdAsync(999));
        }

        [Fact]
        public async Task RecordStockMovementAsync_ValidMovement_UpdatesStockCorrectly()
        {
            // Arrange
            var material = new RawMaterial
            {
                Name = "Eggs",
                Unit = "dozen",
                PricePerUnit = 4.0m,
                CurrentStock = 100
            };
            
            await _context.RawMaterials.AddAsync(material);
            await _context.SaveChangesAsync();

            var movement = new StockMovement
            {
                RawMaterialId = material.Id,
                Quantity = 50,
                MovementType = MovementType.Incoming,
                MovementDate = DateTime.UtcNow
            };

            // Act
            var result = await _inventoryService.RecordStockMovementAsync(movement);
            var updatedMaterial = await _context.RawMaterials.FindAsync(material.Id);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(150, updatedMaterial.CurrentStock);
        }

        [Fact]
        public async Task RecordStockMovementAsync_InsufficientStock_ThrowsBusinessRuleValidationException()
        {
            // Arrange
            var material = new RawMaterial
            {
                Name = "Butter",
                Unit = "kg",
                PricePerUnit = 5.0m,
                CurrentStock = 20
            };
            
            await _context.RawMaterials.AddAsync(material);
            await _context.SaveChangesAsync();

            var movement = new StockMovement
            {
                RawMaterialId = material.Id,
                Quantity = 30,
                MovementType = MovementType.Outgoing,
                MovementDate = DateTime.UtcNow
            };

            // Act & Assert
            await Assert.ThrowsAsync<BusinessRuleValidationException>(() => 
                _inventoryService.RecordStockMovementAsync(movement));
        }
    }
}