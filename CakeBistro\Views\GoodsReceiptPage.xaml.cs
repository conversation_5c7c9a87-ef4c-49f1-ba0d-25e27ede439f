using Microsoft.Maui.Controls;
using CakeBistro.ViewModels;

namespace CakeBistro.Views
{
    public partial class GoodsReceiptPage : ContentPage
    {
        private readonly IInventoryService _inventoryService;
        private readonly GoodsReceiptViewModel _viewModel;
        
        public GoodsReceiptPage(IInventoryService inventoryService)
            : base()
        {
            InitializeComponent();
            _inventoryService = inventoryService;
            _viewModel = new GoodsReceiptViewModel(inventoryService);
            BindingContext = _viewModel;
            
            // Subscribe to messages
            MessagingCenter.Subscribe<GoodsReceiptViewModel>(this, "NavigateBack", (sender) =>
            {
                Navigation.PopAsync();
            });
            
            MessagingCenter.Subscribe<GoodsReceiptViewModel>(this, "ShowGoodsReceiptDetails", async (sender, receiptId) =>
            {
                await Navigation.PushAsync(new GoodsReceiptDetailPage(_inventoryService) { BindingContext = new GoodsReceiptDetailViewModel(_inventoryService) });
            });
        }
        
        protected override void OnNavigatedTo(NavigatedToEventArgs args)
        {
            base.OnNavigatedTo(args);
            
            // Refresh data when page appears
            _viewModel.ExecuteRefreshCommand();
        }
    }
}
