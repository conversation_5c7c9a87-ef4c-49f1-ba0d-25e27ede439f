using CakeBistro.Core.Models;

namespace MCakeBistro.Repositories
{
    public interface IUserRepository : IRepository<CakeBistro.Core.Models.User>
    {
        // Get CakeBistro.Core.Models.User by username
        Task<CakeBistro.Core.Models.User> GetUserByUsernameAsync(string username);
        
        // Get CakeBistro.Core.Models.User by email
        Task<CakeBistro.Core.Models.User> GetUserByEmailAsync(string email);
        
        // Add a new CakeBistro.Core.Models.User
        Task<Guid> AddUserAsync(CakeBistro.Core.Models.User CakeBistro.Core.Models.User);
        
        // Update CakeBistro.Core.Models.User details
        Task UpdateUserAsync(CakeBistro.Core.Models.User CakeBistro.Core.Models.User);
        
        // Delete a CakeBistro.Core.Models.User
        Task DeleteUserAsync(Guid id);
        
        // Get all users with their roles
        Task<IEnumerable<CakeBistro.Core.Models.User>> GetAllUsersWithRolesAsync();
        
        // Get users by CakeBistro.Core.Models.Role
        Task<IEnumerable<CakeBistro.Core.Models.User>> GetUsersByRoleAsync(Guid roleId);
        
        // Get CakeBistro.Core.Models.User permissions
        Task<IEnumerable<Permission>> GetUserPermissionsAsync(Guid userId);
        
        // Log CakeBistro.Core.Models.User activity
        Task LogUserActivityAsync(Guid userId, string activityType, string description, string ipAddress);
        
        // Validate CakeBistro.Core.Models.User credentials
        Task<bool> ValidateUserCredentialsAsync(string username, string password);
        
        // Change CakeBistro.Core.Models.User password
        Task<bool> ChangePasswordAsync(Guid userId, string oldPassword, string newPassword);
    }
}
