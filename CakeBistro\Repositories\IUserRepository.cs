using CakeBistro.Core.Models;

namespace CakeBistro.Repositories
{
    public interface IUserRepository : IRepository<User>
    {
        // Get User by username
        Task<User?> GetUserByUsernameAsync(string username);

        // Get User by email
        Task<User?> GetUserByEmailAsync(string email);

        // Get User by ID
        Task<User?> GetUserByIdAsync(Guid id);

        // Add a new User
        Task<Guid> AddUserAsync(User user);

        // Update User details
        Task UpdateUserAsync(User user);

        // Delete a User
        Task DeleteUserAsync(Guid id);

        // Get all users with their roles
        Task<IEnumerable<User>> GetAllUsersWithRolesAsync();

        // Get users by Role
        Task<IEnumerable<User>> GetUsersByRoleAsync(Guid roleId);

        // Get User permissions
        Task<IEnumerable<Permission>> GetUserPermissionsAsync(Guid userId);

        // Log User activity
        Task LogUserActivityAsync(Guid userId, string activityType, string description, string ipAddress);

        // Validate User credentials
        Task<bool> ValidateUserCredentialsAsync(string username, string password);

        // Change User password
        Task<bool> ChangePasswordAsync(Guid userId, string oldPassword, string newPassword);

        // Get User activity logs
        Task<IEnumerable<UserActivity>> GetUserActivityLogsAsync(Guid userId, DateTime startDate, DateTime endDate);

        // Check if User has permission
        Task<bool> HasUserPermissionAsync(Guid userId, string permissionName);
    }
}
