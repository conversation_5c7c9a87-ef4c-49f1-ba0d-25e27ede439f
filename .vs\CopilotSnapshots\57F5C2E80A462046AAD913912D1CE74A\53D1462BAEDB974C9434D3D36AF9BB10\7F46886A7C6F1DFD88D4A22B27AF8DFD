﻿namespace CakeBistro;

public partial class AppShell : Shell
{
    private readonly IThemeService _themeService;

    public static new AppShell Current => (AppShell)Shell.Current;
    public static readonly BindableProperty IsBusyProperty = BindableProperty.Create(
        nameof(IsBusy), typeof(bool), typeof(AppShell), false);
    public bool IsBusy
    {
        get => (bool)GetValue(IsBusyProperty);
        set => SetValue(IsBusyProperty, value);
    }

    public AppShell(IThemeService themeService)
    {
        InitializeComponent();
        _themeService = themeService;
        
        // Register routes for Logistics Management
        Routing.RegisterRoute("VehicleListPage", typeof(Views.VehicleListPage));
        Routing.RegisterRoute("VehicleDetailPage", typeof(Views.VehicleDetailPage));
        Routing.RegisterRoute("DriverListPage", typeof(Views.DriverListPage));
        Routing.RegisterRoute("DriverDetailPage", typeof(Views.DriverDetailPage));
        Routing.RegisterRoute("DeliveryManifestListPage", typeof(Views.DeliveryManifestListPage));
        Routing.RegisterRoute("DeliveryManifestDetailPage", typeof(Views.DeliveryManifestDetailPage));

        // Register routes for Recipe Management
        Routing.RegisterRoute("RecipesPage", typeof(RecipesPage));
        Routing.RegisterRoute("RecipeDetailPage", typeof(RecipeDetailPage));
        Routing.RegisterRoute("RecipeItemDetailPage", typeof(RecipeItemDetailPage));
        
        // Register routes for Production Management
        Routing.RegisterRoute("ProductionBatchPage", typeof(ProductionBatchPage));
        Routing.RegisterRoute("ProductionBatchDetailPage", typeof(ProductionBatchDetailPage));
        
        // Register routes for Quality Control
        Routing.RegisterRoute("QualityControlPage", typeof(QualityControlPage));
        Routing.RegisterRoute("QualityCheckFormPage", typeof(QualityCheckFormPage));
        Routing.RegisterRoute("QualityCheckReportPage", typeof(Views.QualityCheckReportPage));

        // Register routes for Raw Material and Supplier Management
        Routing.RegisterRoute("RawMaterialRegistrationPage", typeof(Views.RawMaterialRegistrationPage));
        Routing.RegisterRoute("SupplierManagementPage", typeof(Views.SupplierManagementPage));
        Routing.RegisterRoute("StockMovementTrackingPage", typeof(Views.StockMovementTrackingPage));
        
        // Register routes for Recipe and Cost Management
        Routing.RegisterRoute("RecipeManagementPage", typeof(Views.RecipeManagementPage));
        Routing.RegisterRoute("CostCalculationDashboard", typeof(Views.CostCalculationDashboard));
        Routing.RegisterRoute("ProfitabilityAnalysisReport", typeof(Views.ProfitabilityAnalysisReport));

        // Register routes for Reporting
        Routing.RegisterRoute("ReportingDashboardPage", typeof(Views.ReportingDashboardPage));
        Routing.RegisterRoute("ReportSchedulePage", typeof(Views.ReportSchedulePage));

        // Register additional routes
        Routing.RegisterRoute("inventory", typeof(InventoryPage));
        Routing.RegisterRoute("products", typeof(ProductPage));
        Routing.RegisterRoute("themeettings", typeof(ThemeSettingsPage));  // Existing route
        Routing.RegisterRoute("themepreferences", typeof(ThemePreferencesPage));  // New route for preferences
        
        // Subscribe to theme changes
        _themeService.ThemeChanged += OnThemeChanged;
    }

    /// <summary>
    /// Handles theme change events
    /// </summary>
    private void OnThemeChanged(object sender, ThemeEventArgs e)
    {
        if (e?.Theme != null)
        {
            // Update application resources with new theme
            UpdateApplicationResources(e.Theme);
            
            // If the current page is a themable page, update it
            var themablePage = CurrentPage as IThemable;
            themablePage?.ApplyTheme(e.Theme);
        }
    }

    /// <summary>
    /// Updates application-wide resources based on the current theme
    /// </summary>
    private void UpdateApplicationResources(ThemeInfo theme)
    {
        // Implementation to update colors, fonts, etc.
        Resources["PrimaryColor"] = theme.PrimaryColor;
        Resources["SecondaryColor"] = theme.SecondaryColor;
        // Add more resource updates as needed
    }
    }
}
