using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using CakeBistro.Core.Models;

namespace CakeBistro.Repositories
{
    // Interface for purchase order item repository operations
    public interface IPurchaseOrderItemRepository : IRepository<PurchaseOrderItem>
    {
        // Get all purchase order items
        Task<IEnumerable<PurchaseOrderItem>> GetAllAsync();
        
        // Get purchase order item by ID
        Task<PurchaseOrderItem> GetByIdAsync(Guid id);
        
        // Add a new purchase order item
        Task AddAsync(PurchaseOrderItem item);
        
        // Update an existing purchase order item
        Task UpdateAsync(PurchaseOrderItem item);
        
        // Delete a purchase order item
        Task DeleteAsync(Guid id);
        
        // Get items for a specific purchase order
        Task<IEnumerable<PurchaseOrderItem>> GetItemsByOrderAsync(Guid orderId);
    }
}
