using System.Globalization;

namespace CakeBistro.Converters
{
    public class StatusColorConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is string status)
            {
                return status switch
                {
                    "Passed" => Colors.Green,
                    "Failed" => Colors.Red,
                    "NeedsReview" => Colors.Orange,
                    _ => Colors.Gray
                };
            }
            return Colors.Gray;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            // Since this converter is likely used for one-way binding,
            // we can safely return a default status of "Unknown".
            return "Unknown";
        }
    }
}
