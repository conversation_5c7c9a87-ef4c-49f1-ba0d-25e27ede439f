using System;
using System.Collections.Generic;

namespace CakeBistro.Core.Models
{
    public class InterBranchTransfer
    {
        public int Id { get; set; }
        public int SourceBranchId { get; set; }
        public int DestinationBranchId { get; set; }
        public DateTime TransferDate { get; set; }
        public string ReferenceNumber { get; set; } = string.Empty;
        public string Notes { get; set; } = string.Empty;
        public InterBranchTransferStatus Status { get; set; } = InterBranchTransferStatus.Pending;
        public bool IsReconciled { get; set; }
        public DateTime? ReconciledDate { get; set; }
        public ICollection<InterBranchTransferItem> Items { get; set; } = new List<InterBranchTransferItem>();
        public Branch SourceBranch { get; set; }
        public Branch DestinationBranch { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime UpdatedDate { get; set; }
    }

    public enum InterBranchTransferStatus
    {
        Pending,
        InTransit,
        Completed,
        Cancelled
    }

    public class InterBranchTransferItem
    {
        public int Id { get; set; }
        public int InterBranchTransferId { get; set; }
        public int RawMaterialId { get; set; }
        public decimal Quantity { get; set; }
        public RawMaterial RawMaterial { get; set; }
    }

    public class Branch
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Location { get; set; } = string.Empty;
    }
}
