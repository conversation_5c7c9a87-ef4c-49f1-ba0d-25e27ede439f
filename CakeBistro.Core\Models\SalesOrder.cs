using System.Collections.Generic;

namespace CakeBistro.Core.Models
{
    public class SalesOrder : BaseEntity
    {
        public int Id { get; set; }
        public int CustomerId { get; set; }
        public DateTime OrderDate { get; set; }
        public decimal TotalAmount { get; set; }
        public string Status { get; set; }
        public List<SalesOrderItem> Items { get; set; } = new List<SalesOrderItem>();
    }
}
