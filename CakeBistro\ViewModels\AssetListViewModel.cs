using CakeBistro.Models;
using CakeBistro.Services;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.Collections.ObjectModel;
using System.Threading.Tasks;
using System;

namespace CakeBistro.ViewModels
{
    public partial class AssetListViewModel : ObservableObject
    {
        private readonly AssetService _assetService;

        [ObservableProperty]
        private ObservableCollection<FixedAsset> assets = new();
        [ObservableProperty]
        private FixedAsset? selectedAsset;

        // Properties for asset creation/editing
        [ObservableProperty] private string? name;
        [ObservableProperty] private string? category;
        [ObservableProperty] private decimal acquisitionCost;
        [ObservableProperty] private decimal accumulatedDepreciation;
        [ObservableProperty] private string? location;
        [ObservableProperty] private string? status;
        [ObservableProperty] private DateTime purchaseDate = DateTime.Today;
        [ObservableProperty] private decimal purchasePrice;
        [ObservableProperty] private string? notes;
        [ObservableProperty] private string? serialNumber;
        [ObservableProperty] private string? assetTag;
        [ObservableProperty] private string? supplier;
        [ObservableProperty] private string? depreciationMethod;
        [ObservableProperty] private int usefulLifeYears;

        // Depreciation calculation properties
        [ObservableProperty] private string? selectedDepreciationMethod;
        [ObservableProperty] private decimal annualDepreciationRate;
        [ObservableProperty] private decimal netBookValue;
        [ObservableProperty] private ObservableCollection<DepreciationRecord> depreciationSchedule = new();

        // Maintenance scheduling properties
        [ObservableProperty] private DateTime? lastMaintenanceDate;
        [ObservableProperty] private DateTime? nextScheduledDate;
        [ObservableProperty] private string? maintenanceProvider;
        [ObservableProperty] private decimal maintenanceCost;
        [ObservableProperty] private ObservableCollection<MaintenanceRecord> maintenanceHistory = new();

        // Location history
        [ObservableProperty] private ObservableCollection<AssetLocationHistory> locationHistory = new();

        // Asset export feedback
        [ObservableProperty] private string? exportResultMessage;

        public AssetListViewModel(AssetService assetService)
        {
            _assetService = assetService;
        }

        [RelayCommand]
        public async Task LoadAssetsAsync()
        {
            // Use the correct GetAllAssetsAsync (should only be one for FixedAsset)
            var list = await _assetService.GetAllAssetsAsync();
            Assets = new ObservableCollection<FixedAsset>(list);
        }

        [RelayCommand]
        public async Task AddAssetAsync()
        {
            if (!string.IsNullOrWhiteSpace(Name) && !string.IsNullOrWhiteSpace(Category) && AcquisitionCost > 0)
            {
                var asset = new FixedAsset
                {
                    Name = Name!,
                    Category = Category!,
                    AcquisitionCost = AcquisitionCost,
                    AccumulatedDepreciation = AccumulatedDepreciation,
                    Location = Location,
                    Status = Status,
                    PurchaseDate = PurchaseDate,
                    PurchasePrice = PurchasePrice,
                    Notes = Notes,
                    SerialNumber = SerialNumber,
                    AssetTag = AssetTag,
                    Supplier = Supplier,
                    DepreciationMethod = DepreciationMethod,
                    UsefulLifeYears = UsefulLifeYears
                };
                await _assetService.AddAssetAsync(asset);
                await LoadAssetsAsync();
                // Reset fields
                Name = null;
                Category = null;
                AcquisitionCost = 0;
                AccumulatedDepreciation = 0;
                Location = null;
                Status = null;
                PurchaseDate = DateTime.Today;
                PurchasePrice = 0;
                Notes = null;
                SerialNumber = null;
                AssetTag = null;
                Supplier = null;
                DepreciationMethod = null;
                UsefulLifeYears = 0;
            }
        }

        [RelayCommand]
        public async Task RemoveAssetAsync()
        {
            if (SelectedAsset != null)
            {
                await _assetService.DeleteAssetAsync(SelectedAsset.Id);
                await LoadAssetsAsync();
            }
        }

        [RelayCommand]
        public async Task UpdateAssetAsync()
        {
            if (SelectedAsset != null)
            {
                await _assetService.UpdateAssetAsync(SelectedAsset);
                await LoadAssetsAsync();
            }
        }

        [RelayCommand]
        public async Task CalculateDepreciationAsync()
        {
            if (SelectedAsset != null)
            {
                // Example: Call service to calculate depreciation
                var result = await _assetService.CalculateDepreciationAsync(SelectedAsset.Id, DateTime.Today);
                AnnualDepreciationRate = result.AnnualRate;
                NetBookValue = result.NetBookValue;
                DepreciationSchedule = new ObservableCollection<DepreciationRecord>(result.Schedule);
            }
        }

        [RelayCommand]
        public async Task LoadMaintenanceHistoryAsync()
        {
            if (SelectedAsset != null)
            {
                var history = await _assetService.GetMaintenanceHistoryAsync(SelectedAsset.Id);
                MaintenanceHistory = new ObservableCollection<MaintenanceRecord>(history);
                LastMaintenanceDate = history.OrderByDescending(m => m.CompletedDate).FirstOrDefault()?.CompletedDate;
            }
        }

        [RelayCommand]
        public async Task ScheduleMaintenanceAsync()
        {
            if (SelectedAsset != null && NextScheduledDate.HasValue)
            {
                var record = new MaintenanceRecord
                {
                    AssetId = SelectedAsset.Id,
                    ScheduledDate = NextScheduledDate.Value,
                    Provider = MaintenanceProvider,
                    Cost = MaintenanceCost,
                    Status = "Scheduled"
                };
                await _assetService.ScheduleMaintenanceAsync(record);
                await LoadMaintenanceHistoryAsync();
            }
        }

        [RelayCommand]
        public async Task LoadLocationHistoryAsync()
        {
            if (SelectedAsset != null)
            {
                var history = await _assetService.GetLocationHistoryAsync(SelectedAsset.Id);
                LocationHistory = new ObservableCollection<AssetLocationHistory>(history);
            }
        }

        [RelayCommand]
        public async Task ExportAssetRegisterAsync(string format)
        {
            var filePath = await _assetService.ExportAssetRegisterAsync(format);
            ExportResultMessage = $"Asset register exported: {filePath}";
        }
    }
}
