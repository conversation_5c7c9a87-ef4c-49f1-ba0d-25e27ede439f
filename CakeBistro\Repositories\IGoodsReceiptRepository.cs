using CakeBistro.Core.Models;

namespace CakeBistro.Repositories
{
    public interface IGoodsReceiptRepository : IRepository<GoodsReceipt>
    {
        // Get all goods receipts
        Task<IEnumerable<GoodsReceipt>> GetAllAsync();
        
        // Get goods receipt by ID
        Task<GoodsReceipt> GetByIdAsync(Guid id);
        
        // Add a new goods receipt
        Task AddAsync(GoodsReceipt receipt);
        
        // Update an existing goods receipt
        Task UpdateAsync(GoodsReceipt receipt);
        
        // Delete a goods receipt
        Task DeleteAsync(Guid id);
        
        // Get goods receipts by purchase order
        Task<IEnumerable<GoodsReceipt>> GetReceiptsByPurchaseOrderAsync(Guid purchaseOrderId);
        
        // Get goods receipts by date range
        Task<IEnumerable<GoodsReceipt>> GetReceiptsByDateRangeAsync(DateTime startDate, DateTime endDate);
    }
}
