using CakeBistro.Core.Models;
using CakeBistro.Core.Interfaces;
using Microsoft.EntityFrameworkCore;
using System.Linq.Expressions;

namespace CakeBistro.Repositories
{
    public class MaintenanceRepository : BaseRepository<MaintenanceRecord>, IMaintenanceRepository
    {
        public MaintenanceRepository(CakeBistroContext context) : base(context)
        {
        }

        public async Task<IEnumerable<MaintenanceRecord>> GetByAssetAsync(int assetId)
        {
            return await _context.MaintenanceRecords
                .Where(m => m.AssetId == assetId)
                .ToListAsync();
        }

        public async Task<IEnumerable<MaintenanceRecord>> GetScheduledMaintenanceAsync(DateTime date, int days = 7)
        {
            var endDate = date.AddDays(days);
            return await _context.MaintenanceRecords
                .Where(m => m.ScheduledDate >= date && m.ScheduledDate <= endDate && m.Status == "Scheduled")
                .ToListAsync();
        }
    }
}
