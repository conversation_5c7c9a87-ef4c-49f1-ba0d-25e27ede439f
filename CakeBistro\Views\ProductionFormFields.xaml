<?xml version="1.0" encoding="utf-8" ?>
<ContentView xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="CakeBistro.Views.ProductionFormFields">
    <StackLayout Spacing="16" Padding="10">
        <Entry Placeholder="Name" Text="{Binding Name}" />
        <Editor Placeholder="Description" Text="{Binding Description}" HeightRequest="80" />
        <Entry Placeholder="Category" Text="{Binding Category}" />
        <Entry Placeholder="Price Per Unit" Text="{Binding PricePerUnit, Mode=TwoWay}" Keyboard="Numeric" />
        <Entry Placeholder="Reorder Level" Text="{Binding ReorderLevel, Mode=TwoWay}" Keyboard="Numeric" />
        <Entry Placeholder="Profit Margin (%)" Text="{Binding ProfitMargin, Mode=TwoWay}" Keyboard="Numeric" />
    </StackLayout>
</ContentView>
