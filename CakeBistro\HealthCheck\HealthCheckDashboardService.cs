using CakeBistro.Services;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;

namespace CakeBistro.HealthCheck
{
    public class HealthCheckDashboardService
    {
        private readonly ILogger<HealthCheckDashboardService> _logger;
        private readonly IEnumerable<IHealthCheck> _healthChecks;
        private readonly HealthCheckService _healthCheckService;

        public HealthCheckDashboardService(
            ILogger<HealthCheckDashboardService> logger,
            IEnumerable<IHealthCheck> healthChecks,
            HealthCheckService healthCheckService)
        {
            _logger = logger;
            _healthChecks = healthChecks;
            _healthCheckService = healthCheckService;
        }

        public async Task<HealthReport> GetHealthReportAsync()
        {
            try
            {
                var data = new Dictionary<string, object>();
                
                // Overall health status
                var report = await _healthCheckService.CheckHealthAsync();
                data["OverallStatus"] = report.Status.ToString();
                data["TotalChecks"] = report.Entries.Count();
                data["HealthyChecks"] = report.Entries.Count(e => e.Value.Status == HealthStatus.Healthy);
                data["DegradedChecks"] = report.Entries.Count(e => e.Value.Status == HealthStatus.Degraded);
                data["UnhealthyChecks"] = report.Entries.Count(e => e.Value.Status == HealthStatus.Unhealthy);
                data["Description"] = report.Description;
                
                // Detailed component statuses
                var componentStatuses = new Dictionary<string, object>();
                foreach (var entry in report.Entries)
                {
                    var entryData = new Dictionary<string, object>
                    {
                        { "Status", entry.Value.Status.ToString() },
                        { "Description", entry.Value.Description }
                    };
                    
                    if (entry.Value.Exception != null)
                    {
                        entryData["Exception"] = entry.Value.Exception.Message;
                        entryData["StackTrace"] = entry.Value.Exception.StackTrace;
                    }
                    
                    componentStatuses[entry.Key] = entryData;
                }
                
                data["ComponentStatuses"] = componentStatuses;
                
                // Add timestamp
                data["Timestamp"] = DateTime.UtcNow;
                
                // Add identity service status
                await AddIdentityServiceStatus(data);
                
                // Add cache service status
                AddCacheServiceStatus(data);
                
                return new HealthReport(data);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating health report: {Message}", ex.Message);
                throw;
            }
        }
        
        private async Task AddIdentityServiceStatus(Dictionary<string, object> data)
        {
            try
            {
                var userManager = _serviceProvider.GetService<UserManager<ApplicationUser>>();
                if (userManager != null)
                {
                    // Test identity service by getting user count
                    var usersCount = await userManager.Users.CountAsync();
                    data["IdentityServiceStatus"] = new Dictionary<string, object>
                    {
                        { "Status", HealthStatus.Healthy.ToString() },
                        { "UsersCount", usersCount }
                    };
                }
                else
                {
                    data["IdentityServiceStatus"] = new Dictionary<string, object>
                    {
                        { "Status", HealthStatus.Unhealthy.ToString() },
                        { "Error", "UserManager not available" }
                    };
                }
            }
            catch (Exception ex)
            {
                data["IdentityServiceStatus"] = new Dictionary<string, object>
                {
                    { "Status", HealthStatus.Unhealthy.ToString() },
                    { "Error", ex.Message }
                };
                _logger.LogError(ex, "Error checking identity service status: {Message}", ex.Message);
            }
        }
        
        private void AddCacheServiceStatus(Dictionary<string, object> data)
        {
            try
            {
                // Test cache service by writing and reading a test value
                string testKey = $"healthcheck_{DateTime.UtcNow.Ticks}";
                string testValue = "test_data";
                
                _distributedCacheService.Set(testKey, testValue, TimeSpan.FromMinutes(1));
                var result = _distributedCacheService.Get<string>(testKey);
                _distributedCacheService.Remove(testKey);
                
                data["CacheServiceStatus"] = new Dictionary<string, object>
                {
                    { "Status", result == testValue ? HealthStatus.Healthy.ToString() : HealthStatus.Unhealthy.ToString() },
                    { "TestKey", testKey }
                };
            }
            catch (Exception ex)
            {
                data["CacheServiceStatus"] = new Dictionary<string, object>
                {
                    { "Status", HealthStatus.Unhealthy.ToString() },
                    { "Error", ex.Message }
                };
                _logger.LogError(ex, "Error checking cache service status: {Message}", ex.Message);
            }
        }
        
        public async Task WriteHealthReportResponseAsync(HttpContext context)
        {
            var report = await GetHealthReportAsync();
            
            context.Response.ContentType = "application/json";
            await context.Response.WriteAsync(report.ToJson());
        }
    }
    
    public class HealthReport
    {
        public Dictionary<string, object> Data { get; }
        
        public HealthReport(Dictionary<string, object> data)
        {
            Data = data;
        }
        
        public string ToJson()
        {
            return JsonSerializer.Serialize(Data, new JsonSerializerOptions { WriteIndented = true });
        }
    }
}