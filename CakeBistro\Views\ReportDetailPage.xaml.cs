using CakeBistro.ViewModels;
using CakeBistro.Core.Models;
using Microsoft.Maui.Controls;

namespace CakeBistro.Views
{
    public partial class ReportDetailPage : ContentPage
    {
        private readonly ReportDetailViewModel _viewModel;
        
        public ReportDetailPage(ReportDetailViewModel viewModel)
        {
            InitializeComponent();
            _viewModel = viewModel;
            BindingContext = _viewModel;
        }
        
        protected override async void OnAppearing()
        {
            base.OnAppearing();
            
            // Get the report ID from query parameters or navigation
            if (Shell.Current.CurrentState.Location.QueryParameters.TryGetValue("reportId", out var reportIdParam))
            {
                if (Guid.TryParse(reportIdParam.ToString(), out Guid reportId))
                {
                    await _viewModel.LoadDataAsync(reportId);
                }
            }
        }
    }
}
