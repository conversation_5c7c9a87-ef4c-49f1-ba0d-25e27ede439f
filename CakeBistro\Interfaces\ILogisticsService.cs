namespace CakeBistro.Core.Interfaces
{
    public interface ILogisticsService
    {
        // Vehicle Management
        Task<Vehicle> AddVehicleAsync(Vehicle vehicle);
        Task<Vehicle> UpdateVehicleAsync(Vehicle vehicle);
        Task<Vehicle> GetVehicleByIdAsync(int id);
        Task<IEnumerable<Vehicle>> GetAllVehiclesAsync();
        Task<IEnumerable<Vehicle>> GetAvailableVehiclesAsync(DateTime date);
        Task<bool> DeleteVehicleAsync(int id);
        
        // Driver Management
        Task<Driver> AddDriverAsync(Driver driver);
        Task<Driver> UpdateDriverAsync(Driver driver);
        Task<Driver> GetDriverByIdAsync(int id);
        Task<IEnumerable<Driver>> GetAllDriversAsync();
        Task<IEnumerable<Driver>> GetAvailableDriversAsync(DateTime date);
        Task<bool> DeleteDriverAsync(int id);
        
        // Maintenance Records
        Task<MaintenanceRecord> AddMaintenanceRecordAsync(MaintenanceRecord record);
        Task<IEnumerable<MaintenanceRecord>> GetVehicleMaintenanceHistoryAsync(int vehicleId);
        Task<IEnumerable<Vehicle>> GetVehiclesDueForMaintenanceAsync();
        
        // Delivery Manifests
        Task<DeliveryManifest> CreateManifestAsync(DeliveryManifest manifest);
        Task<DeliveryManifest> UpdateManifestAsync(DeliveryManifest manifest);
        Task<DeliveryManifest> GetManifestByIdAsync(int id);
        Task<IEnumerable<DeliveryManifest>> GetManifestsForDateAsync(DateTime date);
        Task<bool> DeleteManifestAsync(int id);
        
        // Route Optimization
        Task<IEnumerable<DeliveryStop>> OptimizeRouteAsync(DeliveryManifest manifest);
        Task<(decimal Distance, TimeSpan Duration)> CalculateRouteMetricsAsync(IEnumerable<DeliveryStop> stops);
        Task UpdateStopStatusAsync(int stopId, string status, string notes = null);
        
        // Stock Adjustment
        Task<Product> AdjustStockAsync(StockAdjustment adjustment);
    }
}