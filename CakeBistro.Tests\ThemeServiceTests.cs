
using System;
using System.Linq;
using System.Threading.Tasks;
using CakeBistro.Models;
using CakeBistro.Services;
using Microsoft.EntityFrameworkCore;
using Xunit;

namespace CakeBistro.Tests
{
    public class ThemeServiceTests
    {
        private readonly CakeBistroContext _context;
        private readonly IThemeService _themeService;

        public ThemeServiceTests()
        {
            // Set up in-memory database for testing
            var options = new DbContextOptionsBuilder<CakeBistroContext>()
                .UseInMemoryDatabase(databaseName: $"ThemeTestDb_{Guid.NewGuid()}")
                .Options;

            _context = new CakeBistroContext(options);
            _themeService = new ThemeService(_context);
        }

        [Fact]
        public async Task GetAvailableThemesAsync_ReturnsAllThemes()
        {
            // Arrange
            // Add built-in themes (simulated from configuration)
            // In real implementation these would come from actual configuration
            
            // Add custom themes to the database
            var customTheme1 = new CustomTheme
            {
                Name = "Dark Blue",
                Description = "Dark blue theme with light text",
                Version = "1.0.0",
                Author = "System",
                AccentColor = "#0078D4",
                BackgroundColor = "#121212",
                TextColor = "#FFFFFF"
            };
            
            var customTheme2 = new CustomTheme
            {
                Name = "Light Green",
                Description = "Light green theme with dark text",
                Version = "1.0.0",
                Author = "System",
                AccentColor = "#34C759",
                BackgroundColor = "#F8F8F8",
                TextColor = "#000000"
            };
            
            await _context.CustomThemes.AddRangeAsync(customTheme1, customTheme2);
            await _context.SaveChangesAsync();

            // Act
            var result = await _themeService.GetAvailableThemesAsync();

            // Assert
            Assert.NotNull(result);
            
            // Verify built-in themes are included
            Assert.Contains(result, t => t.Name == "Default");
            Assert.Contains(result, t => t.Name == "Dark Mode");
            Assert.Contains(result, t => t.Name == "High Contrast");
            
            // Verify custom themes are included
            Assert.Contains(result, t => t.Name == "Dark Blue");
            Assert.Contains(result, t => t.Name == "Light Green");
            
            // Verify description and author fields
            var highContrastTheme = result.FirstOrDefault(t => t.Name == "High Contrast");
            Assert.NotNull(highContrastTheme);
            Assert.Equal("Accessibility focused high contrast theme", highContrastTheme.Description);
            Assert.Equal("System", highContrastTheme.Author);
            
            // Verify total count (3 built-in + 2 custom = 5)
            Assert.Equal(5, result.Count);
        }

        [Fact]
        public async Task GetCurrentThemeAsync_Default_ReturnsDefaultConfiguration()
        {
            // Act
            var result = await _themeService.GetCurrentThemeAsync();

            // Assert
            Assert.NotNull(result);
            Assert.Equal("Default", result.CurrentThemeName);
            Assert.False(result.IsDarkModeEnabled);
            Assert.Equal("#FF6F00", result.AccentColor);  // Default accent color
            Assert.Equal("#FFFFFF", result.BackgroundColor);  // Light mode background
            Assert.Equal("#000000", result.TextColor);  // Light mode text
            
            // Verify last changed date is recent
            Assert.NotNull(result.LastChangedDate);
            Assert.True((DateTime.UtcNow - result.LastChangedDate.Value).TotalSeconds < 10);
        }

        [Fact]
        public async Task ApplyThemeAsync_ValidTheme_ChangesCurrentTheme()
        {
            // Arrange
            // Add a custom theme to the database
            var customTheme = new CustomTheme
            {
                Name = "Test Theme",
                Description = "A theme for testing",
                Version = "1.0.0",
                Author = "Test User",
                AccentColor = "#FF0000",
                BackgroundColor = "#000000",
                TextColor = "#FFFFFF"
            };
            
            await _context.CustomThemes.AddAsync(customTheme);
            await _context.SaveChangesAsync();

            // Act
            var result = await _themeService.ApplyThemeAsync("Test Theme");

            // Assert
            Assert.True(result);
            
            // Verify current theme has been updated
            var currentTheme = await _themeService.GetCurrentThemeAsync();
            Assert.NotNull(currentTheme);
            Assert.Equal("Test Theme", currentTheme.CurrentThemeName);
            Assert.True(currentTheme.IsDarkModeEnabled);  // Custom themes default to dark mode
            Assert.Equal("#FF0000", currentTheme.AccentColor);
            Assert.Equal("#000000", currentTheme.BackgroundColor);
            Assert.Equal(customTheme.TextColor, currentTheme.TextColor);
            
            // Verify last changed date was updated
            var preferences = await _context.ThemePreferences.FirstOrDefaultAsync();
            Assert.NotNull(preferences);
            Assert.Equal("Test Theme", preferences.PreferredTheme);
            Assert.True((DateTime.UtcNow - preferences.LastUpdated).TotalSeconds < 10);
        }

        [Fact]
        public async Task ApplyThemeAsync_InvalidTheme_ThrowsException()
        {
            // Act & Assert
            var exception = await Assert.ThrowsAsync<BusinessRuleValidationException>(() => 
                _themeService.ApplyThemeAsync("NonExistentTheme"));
            
            Assert.NotNull(exception);
            Assert.Equal("ThemeNotFound", exception.RuleName);
        }

        [Fact]
        public async Task SaveAndGetThemePreferencesAsync_PersistsPreferences()
        {
            // Arrange
            var preferences = new ThemePreferences
            {
                PreferredTheme = "Dark Mode",
                UseDarkMode = true,
                PreferredAccentColor = "#0078D4"
            };

            // Act
            var saveResult = await _themeService.SaveThemePreferencesAsync(preferences);
            var getResult = await _themeService.GetThemePreferencesAsync();

            // Assert
            Assert.True(saveResult);
            Assert.NotNull(getResult);
            Assert.Equal(preferences.PreferredTheme, getResult.PreferredTheme);
            Assert.Equal(preferences.UseDarkMode, getResult.UseDarkMode);
            Assert.Equal(preferences.PreferredAccentColor, getResult.PreferredAccentColor);
            Assert.True((DateTime.UtcNow - getResult.LastUpdated).TotalSeconds < 10);
        }

        [Fact]
        public async Task CreateCustomThemeAsync_ValidTheme_CreatesTheme()
        {
            // Arrange
            var theme = new CustomTheme
            {
                Name = "New Theme",
                Description = "A newly created theme",
                Version = "1.0.0",
                Author = "Test User",
                AccentColor = "#00FF00",
                BackgroundColor = "#000000",
                TextColor = "#FFFFFF",
                PrimaryFontFamily = "Segoe UI",
                SecondaryFontFamily = "Arial"
            };

            // Act
            var result = await _themeService.CreateCustomThemeAsync(theme);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(theme.Name, result.Name);
            Assert.Equal(theme.Description, result.Description);
            Assert.Equal(theme.Version, result.Version);
            Assert.Equal(theme.Author, result.Author);
            Assert.Equal(theme.AccentColor, result.AccentColor);
            Assert.Equal(theme.BackgroundColor, result.BackgroundColor);
            Assert.Equal(theme.TextColor, result.TextColor);
            Assert.Equal(theme.PrimaryFontFamily, result.PrimaryFontFamily);
            Assert.Equal(theme.SecondaryFontFamily, result.SecondaryFontFamily);
            Assert.NotNull(result.CreatedDate);
            Assert.Null(result.LastModifiedDate);
            Assert.False(result.IsDeleted);
            
            // Verify theme was saved to database
            var dbTheme = await _context.CustomThemes.FindAsync(result.Id);
            Assert.NotNull(dbTheme);
            Assert.Equal(result.Id, dbTheme.Id);
        }

        [Fact]
        public async Task UpdateCustomThemeAsync_ValidUpdates_AppliesChanges()
        {
            // Arrange
            // Create and save a theme first
            var originalTheme = new CustomTheme
            {
                Name = "Original Theme",
                Description = "Original description",
                Version = "1.0.0",
                Author = "Original Author",
                AccentColor = "#FF0000",
                BackgroundColor = "#000000",
                TextColor = "#FFFFFF"
            };
            
            var createdTheme = await _themeService.CreateCustomThemeAsync(originalTheme);
            
            // Create updated version
            var updatedTheme = new CustomTheme
            {
                Id = createdTheme.Id,
                Name = "Updated Theme",
                Description = "Updated description",
                Version = "1.1.0",
                Author = "Updated Author",
                AccentColor = "#00FF00",
                BackgroundColor = "#111111",
                TextColor = "#EEEEEE",
                PrimaryFontFamily = "Calibri",
                SecondaryFontFamily = "Tahoma"
            };
            
            // Act
            var result = await _themeService.UpdateCustomThemeAsync(updatedTheme);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(updatedTheme.Name, result.Name);
            Assert.Equal(updatedTheme.Description, result.Description);
            Assert.Equal(updatedTheme.Version, result.Version);
            Assert.Equal(updatedTheme.Author, result.Author);
            Assert.Equal(updatedTheme.AccentColor, result.AccentColor);
            Assert.Equal(updatedTheme.BackgroundColor, result.BackgroundColor);
            Assert.Equal(updatedTheme.TextColor, result.TextColor);
            Assert.Equal(updatedTheme.PrimaryFontFamily, result.PrimaryFontFamily);
            Assert.Equal(updatedTheme.SecondaryFontFamily, result.SecondaryFontFamily);
            Assert.NotNull(result.LastModifiedDate);
            
            // Verify changes were saved to database
            var dbTheme = await _context.CustomThemes.FindAsync(result.Id);
            Assert.NotNull(dbTheme);
            Assert.Equal(result.Id, dbTheme.Id);
            Assert.Equal(updatedTheme.Name, dbTheme.Name);
            Assert.Equal(updatedTheme.Description, dbTheme.Description);
            Assert.Equal(updatedTheme.Version, dbTheme.Version);
            Assert.Equal(updatedTheme.Author, dbTheme.Author);
            Assert.Equal(updatedTheme.AccentColor, dbTheme.AccentColor);
            Assert.Equal(updatedTheme.BackgroundColor, dbTheme.BackgroundColor);
            Assert.Equal(updatedTheme.TextColor, dbTheme.TextColor);
            Assert.Equal(updatedTheme.PrimaryFontFamily, dbTheme.PrimaryFontFamily);
            Assert.Equal(updatedTheme.SecondaryFontFamily, dbTheme.SecondaryFontFamily);
            Assert.NotNull(dbTheme.LastModifiedDate);
        }

        [Fact]
        public async Task DeleteCustomThemeAsync_ValidTheme_SoftDeletesTheme()
        {
            // Arrange
            // Create and save a theme
            var theme = new CustomTheme
            {
                Name = "Delete Test Theme",
                Description = "Theme for deletion test",
                Version = "1.0.0",
                Author = "Test User",
                AccentColor = "#FF0000",
                BackgroundColor = "#000000",
                TextColor = "#FFFFFF"
            };
            
            var createdTheme = await _themeService.CreateCustomThemeAsync(theme);
            
            // Act
            var result = await _themeService.DeleteCustomThemeAsync(createdTheme.Id);

            // Assert
            Assert.True(result);
            
            // Verify theme was soft-deleted
            var dbTheme = await _context.CustomThemes.FindAsync(createdTheme.Id);
            Assert.NotNull(dbTheme);
            Assert.True(dbTheme.IsDeleted);
            Assert.NotNull(dbTheme.DeletedDate);
            Assert.True((DateTime.UtcNow - dbTheme.DeletedDate.Value).TotalSeconds < 10);
        }
    }
}