using System;

namespace CakeBistro.Models
{
    public class FixedAsset
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty;
        public decimal AcquisitionCost { get; set; }
        public decimal AccumulatedDepreciation { get; set; }
        public string? Location { get; set; }
        public string? Status { get; set; }
        public DateTime PurchaseDate { get; set; }
        public decimal PurchasePrice { get; set; }
        public string? Notes { get; set; }
        public DateTime? DisposalDate { get; set; }
        public decimal? DisposalValue { get; set; }
        public string? SerialNumber { get; set; }
        public string? AssetTag { get; set; }
        public string? Supplier { get; set; }
        public string? DepreciationMethod { get; set; } // e.g. Straight-line, Reducing balance
        public int UsefulLifeYears { get; set; } // For depreciation
        public decimal CurrentValue => AcquisitionCost - AccumulatedDepreciation;
    }
}
