using System;

namespace CakeBistro.Core.Models {
    public class InventoryItemValuation : BaseEntity {
        public int RawMaterialId { get; set; }
        public string RawMaterialName { get; set; } = string.Empty;
        public decimal Quantity { get; set; }
        public decimal UnitPrice { get; set; }
        public decimal TotalValue => Quantity * UnitPrice;
        public bool IsLowStock { get; set; }
        public bool IsOutOfStock { get; set; }
        public bool IsCriticalStock { get; set; }
        public string StorageLocation { get; set; } = string.Empty;
        public InventoryValuationReport? InventoryValuationReport { get; set; }
        public int? InventoryValuationReportId { get; set; }
    }
}
