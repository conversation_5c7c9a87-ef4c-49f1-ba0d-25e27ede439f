

// Create GreaterThanZeroConverter.cs
using System;
using System.Globalization;
using Microsoft.Maui.Controls;

namespace CakeBistro.Converters;

public class GreaterThanZeroConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is int intValue)
        {
            return intValue > 0;
        }
        return false;
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        // Since this converter is likely used for one-way binding,
        // we can safely return a default value of 0.
        return 0;
    }