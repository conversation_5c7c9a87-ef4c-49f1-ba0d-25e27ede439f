﻿using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;

namespace CakeBistro.Services
{
    public class GlobalExceptionHandler
    {
        private readonly ILogger<GlobalExceptionHandler> _logger;

        public GlobalExceptionHandler(ILogger<GlobalExceptionHandler> logger)
        {
            _logger = logger;
        }

        public void RegisterHandlers()
        {
            // Register exception handlers for different platforms
            RegisterPlatformSpecificHandlers();
            
            // Register unhandled exception handler
            AppDomain.CurrentDomain.UnhandledException += (sender, args) =>
            {
                var exception = (Exception)args.ExceptionObject;
                HandleException(exception);
            };
        }

        private void RegisterPlatformSpecificHandlers()
        {
            // Platform-specific exception handling registration
            // This would vary depending on the target platform
            // For example, on Android you might use Java.Lang.Thread.DefaultUncaughtExceptionHandler
            // On iOS you might use UIKit.UICriticalAlertController
            // For .NET Core you could use AppDomain.CurrentDomain.UnhandledException
        }

        private void HandleException(Exception exception)
        {
            _logger.LogError(exception, "An unhandled exception occurred: {Message}", exception.Message);
            
            // You could also dispatch to a specific error-handling service or UI
            // For example, showing an error page in your application
        }
    }
}
using CakeBistro.Data;
using CakeBistro.Services;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Configuration;

namespace CakeBistro
{
    public partial class App : Application
    {
        public App()
        {
            InitializeComponent();
            
            // Load configuration
            var config = new ConfigurationBuilder()
                .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                .Build();
                
            // Configure services
            var services = new ServiceCollection()
                .AddApplicationServices(config);
                
            // Get and use global exception handler
            var serviceProvider = services.BuildServiceProvider();
            var exceptionHandler = serviceProvider.GetService<GlobalExceptionHandler>();
            exceptionHandler.RegisterHandlers();
            
            // Get identity services and create database if needed
            var identityContext = serviceProvider.GetService<CakeBistroIdentityContext>();
            if (identityContext != null)
            {
                try
                {
                    await identityContext.Database.EnsureCreatedAsync(); // Changed to async
                }
                catch (Exception ex)
                {
                    // Log error but continue since it might be a migration issue
                    var logger = serviceProvider.GetService<ILogger<App>>();
                    logger.LogError(ex, "Error ensuring identity database: {Message}", ex.Message);
                }
            }
            
            // Register health check dashboard
            var healthCheckDashboard = serviceProvider.GetService<HealthCheckDashboardService>();
            
            // Set up health check middleware
            var appBuilder = serviceProvider.GetRequiredService<IApplicationBuilder>();
            appBuilder.UseHealthCheckDashboard();
            
            // Get context and apply migrations
            var context = serviceProvider.GetService<CakeBistroContext>();
            
            // Use retry policy for database migration
            var retryPolicy = serviceProvider.GetService<RetryPolicy>();
            var logger = serviceProvider.GetService<ILogger<App>>();
            
            try
            {
                // Apply migrations with retry policy
                await retryPolicy.ExecuteAsync(
                    async () =>
                    {
                        await context.Database.MigrateAsync();
                        return true;
                    },
                    "DatabaseMigration"
                );
            }
            catch (Exception ex)
            {
                logger.LogCritical(ex, "Failed to apply database migrations: {Message}", ex.Message);
                // Handle migration failure appropriately
            }
                
            // Set the main page
            MainPage = new AppShell();
        }
    }
}