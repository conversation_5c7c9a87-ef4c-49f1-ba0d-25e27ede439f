using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using CakeBistro.Core.Models;

namespace CakeBistro.Services
{
    public class LoadingService
    {
        // Simulated in-memory storage (replace with EF Core/DB in production)
        private readonly List<LoadingSection> _sections = new();

        public Task<LoadingSection> CreateLoadingSectionAsync(LoadingSection section)
        {
            section.Id = _sections.Count > 0 ? _sections.Max(s => s.Id) + 1 : 1;
            _sections.Add(section);
            return Task.FromResult(section);
        }

        public Task<List<LoadingSection>> GetAllLoadingSectionsAsync()
        {
            return Task.FromResult(_sections.ToList());
        }

        public Task<bool> AddItemToLoadingSectionAsync(int sectionId, LoadingItem item)
        {
            var section = _sections.FirstOrDefault(s => s.Id == sectionId);
            if (section == null) return Task.FromResult(false);
            if (section.Items == null) section.Items = new List<LoadingItem>();
            item.Id = section.Items.Count > 0 ? section.Items.Max(i => i.Id) + 1 : 1;
            section.Items.Add(item);
            return Task.FromResult(true);
        }

        public Task<bool> RemoveItemFromLoadingSectionAsync(int sectionId, int itemId)
        {
            var section = _sections.FirstOrDefault(s => s.Id == sectionId);
            if (section == null || section.Items == null) return Task.FromResult(false);
            var item = section.Items.FirstOrDefault(i => i.Id == itemId);
            if (item == null) return Task.FromResult(false);
            section.Items.Remove(item);
            return Task.FromResult(true);
        }

        public Task<bool> AdjustLoadingItemQuantityAsync(int sectionId, int itemId, int newQuantity)
        {
            var section = _sections.FirstOrDefault(s => s.Id == sectionId);
            if (section == null || section.Items == null) return Task.FromResult(false);
            var item = section.Items.FirstOrDefault(i => i.Id == itemId);
            if (item == null) return Task.FromResult(false);
            item.Quantity = newQuantity;
            return Task.FromResult(true);
        }

        public Task<bool> LoadManifestAsync(DeliveryManifest manifest)
        {
            // For each item in the manifest, add to the first loading section (or create one if none exists)
            var section = _sections.FirstOrDefault();
            if (section == null)
            {
                section = new LoadingSection { Id = 1, Name = "Default Loading Section", Description = "Auto-created for manifest loading", Items = new List<LoadingItem>() };
                _sections.Add(section);
            }
            foreach (var item in manifest.Items)
            {
                var existing = section.Items.FirstOrDefault(i => i.Name == item.ProductName);
                if (existing != null)
                {
                    existing.Quantity += item.Quantity;
                }
                else
                {
                    section.Items.Add(new LoadingItem { Id = section.Items.Count > 0 ? section.Items.Max(i => i.Id) + 1 : 1, Name = item.ProductName, Quantity = item.Quantity });
                }
            }
            return Task.FromResult(true);
        }
    }
}
