<?xml version="1.0" encoding="utf-8" ?>
<views:FormTemplate xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
                    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
                    xmlns:views="clr-namespace:CakeBistro.Views"
                    x:Class="CakeBistro.Views.InventoryFormFields">
    <StackLayout>
        <Picker Title="Raw Material" ItemsSource="{Binding RawMaterials}" ItemDisplayBinding="{Binding Name}" SelectedItem="{Binding InventoryBatch.RawMaterial}" AutomationId="InventoryBatchRawMaterialPicker">
            <Picker.Tooltip>Select the raw material for this batch.</Picker.Tooltip>
        </Picker>
        <Entry Placeholder="Batch Number" Text="{Binding InventoryBatch.BatchNumber}" AutomationId="InventoryBatchNumberEntry">
            <Entry.Tooltip>Enter a unique batch number.</Entry.Tooltip>
        </Entry>
        <Entry Placeholder="Quantity" Keyboard="Numeric" Text="{Binding InventoryBatch.Quantity}" AutomationId="InventoryBatchQuantityEntry">
            <Entry.Tooltip>Enter the quantity for this batch.</Entry.Tooltip>
        </Entry>
        <DatePicker Date="{Binding InventoryBatch.ProductionDate}" Format="yyyy-MM-dd" AutomationId="InventoryBatchProductionDatePicker">
            <DatePicker.Tooltip>Select the production date.</DatePicker.Tooltip>
        </DatePicker>
        <DatePicker Date="{Binding InventoryBatch.ExpiryDate}" Format="yyyy-MM-dd" AutomationId="InventoryBatchExpiryDatePicker">
            <DatePicker.Tooltip>Select the expiry date.</DatePicker.Tooltip>
        </DatePicker>
        <Editor Placeholder="Notes" Text="{Binding InventoryBatch.Notes}" AutoSize="TextChanges" AutomationId="InventoryBatchNotesEditor">
            <Editor.Tooltip>Additional notes or comments.</Editor.Tooltip>
        </Editor>
    </StackLayout>
</views:FormTemplate>
