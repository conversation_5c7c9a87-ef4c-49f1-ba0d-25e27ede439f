using Microsoft.Maui.Controls;
using System.Globalization;

namespace CakeBistro.Converters
{
    public class StockLevelColorConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is decimal stockLevel)
            {
                // You would typically compare against minimum stock level
                // For now, using simple thresholds
                if (stockLevel <= 5)
                    return Colors.Red;
                else if (stockLevel <= 20)
                    return Colors.Orange;
                else
                    return Colors.Green;
            }
            return Colors.Gray;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    public class StockLevelTextConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is decimal stockLevel)
            {
                if (stockLevel <= 5)
                    return "Low";
                else if (stockLevel <= 20)
                    return "Medium";
                else
                    return "Good";
            }
            return "Unknown";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
