using CakeBistro.Models;
using CakeBistro.Services;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.Collections.ObjectModel;
using System.Threading.Tasks;
using System;
using Microsoft.UI.Xaml.Media;

namespace CakeBistro.ViewModels
{
    public partial class SalesAnalysisViewModel : ObservableObject
    {
        private readonly ISalesService _salesService;

        [ObservableProperty] private ObservableCollection<SalesSummary> salesSummaries = new();
        [ObservableProperty] private DateTime startDate = DateTime.Today.AddDays(-30);
        [ObservableProperty] private DateTime endDate = DateTime.Today;
        [ObservableProperty] private decimal totalSales;
        [ObservableProperty] private int orderCount;
        [ObservableProperty] private decimal averageOrderValue;
        [ObservableProperty] private string? topSellingProduct;
        [ObservableProperty] private string? exportResultMessage;

        private Color _statusColor = Colors.Transparent;
        public Color StatusColor
        {
            get => _statusColor;
            set => SetProperty(ref _statusColor, value);
        }

        public SalesAnalysisViewModel(ISalesService salesService)
        {
            _salesService = salesService;
        }

        [RelayCommand]
        public async Task LoadSalesSummaryAsync()
        {
            try
            {
                var summary = await _salesService.GetSalesAnalysisAsync(StartDate, EndDate);
                SalesSummaries.Clear();
                SalesSummaries.Add(summary);
                TotalSales = summary.TotalSales;
                OrderCount = summary.OrderCount;
                AverageOrderValue = summary.AverageOrderValue;
                TopSellingProduct = summary.TopSellingProduct?.Name;

                StatusMessage = "Sales analysis complete.";
                StatusColor = Color.FromArgb("#388E3C"); // Success color
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error: {ex.Message}";
                StatusColor = Color.FromArgb("#D32F2F"); // Error color
            }
        }

        [RelayCommand]
        public async Task ExportSalesSummaryAsync(string format)
        {
            try
            {
                var filePath = await _salesService.ExportSalesSummaryAsync(StartDate, EndDate, format);
                ExportResultMessage = $"Sales summary exported: {filePath}";

                StatusMessage = "Export successful.";
                StatusColor = Color.FromArgb("#388E3C"); // Success color
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error: {ex.Message}";
                StatusColor = Color.FromArgb("#D32F2F"); // Error color
            }
        }
    }
}
