<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="CakeBistro.Views.PurchaseOrderPage"
             xmlns:viewModels="clr-namespace:CakeBistro.ViewModels"
             xmlns:models="clr-namespace:CakeBistro.Models"
             Title="Purchase Orders">
    <ContentPage.BindingContext>
        <viewModels:PurchaseOrderViewModel />
    </ContentPage.BindingContext>
    
    <ScrollView>
        <VerticalStackLayout Spacing="16" Padding="16">
            <!-- Status Message -->
            <Label Text="{Binding StatusMessage}"
                   IsVisible="{Binding StatusMessage, Converter={StaticResource EmptyToFalseConverter}}"
                   TextColor="{Binding StatusColor}" />
            
            <!-- New Purchase Order Form -->
            <Frame Header="New Purchase Order">
                <VerticalStackLayout Spacing="8">
                    <Picker ItemsSource="{Binding Suppliers}"
                          SelectedItem="{Binding NewPurchaseOrder.Supplier}"
                          DisplayMemberPath="Name"
                          Title="Select Supplier" />
                    
                    <DatePicker Date="{Binding NewPurchaseOrder.OrderDate}"
                                 Format="D" />
                    
                    <CollectionView ItemsSource="{Binding NewPurchaseOrder.Items}"
                                      ItemsLayout="VerticalList">
                        <CollectionView.ItemTemplate>
                            <DataTemplate x:DataType="models:PurchaseOrderItem">
                                <HorizontalStackLayout Spacing="8">
                                    <Picker ItemsSource="{Binding RawMaterials}"
                                          SelectedItem="{Binding RawMaterial}"
                                          DisplayMemberPath="Name"
                                          WidthRequest="200" />
                                    
                                    <Entry Placeholder="Quantity"
                                           Text="{Binding Quantity, StringFormat='{0:F0}'}"
                                           Keyboard="Numeric"
                                           WidthRequest="80" />
                                    
                                    <Entry Placeholder="Unit Price"
                                           Text="{Binding UnitPrice, StringFormat='{0:F2}'}"
                                           Keyboard="Numeric" />
                                    
                                    <Button Text="Remove"
                                            Command="{Binding Source={RelativeSource AncestorType={x:Type viewModels:PurchaseOrderViewModel}}, Path=RemoveItemCommand}"
                                            CommandParameter="{Binding .}" />
                                </HorizontalStackLayout>
                            </DataTemplate>
                        </CollectionView.ItemTemplate>
                    </CollectionView>
                    
                    <HorizontalStackLayout Spacing="8">
                        <Button Text="Add Order"
                                Command="{Binding AddOrderCommand}"
                                BackgroundColor="Green"
                                TextColor="White" />
                        <Button Text="Add Item"
                                Command="{Binding AddItemCommand}"
                                IsEnabled="{Binding IsProcessing, Converter={StaticResource InvertBooleanConverter}}" />
                        
                        <Button Text="Create Order"
                                Command="{Binding CreateOrderCommand}"
                                IsEnabled="{Binding IsProcessing, Converter={StaticResource InvertBooleanConverter}}" />
                    </HorizontalStackLayout>
                </VerticalStackLayout>
            </Frame>
            
            <!-- Purchase Orders List -->
            <Frame Header="Existing Purchase Orders">
                <VerticalStackLayout Spacing="8">
                    <SearchBar Placeholder="Search orders..." />
                    
                    <CollectionView ItemsSource="{Binding PurchaseOrders}"
                                      SelectionMode="Single"
                                      SelectedItem="{Binding SelectedPurchaseOrder}">
                        <CollectionView.ItemTemplate>
                            <DataTemplate x:DataType="models:PurchaseOrder">
                                <Frame Margin="0,4">
                                    <Grid ColumnDefinitions="*, Auto">
                                        <VerticalStackLayout Grid.Column="0">
                                            <Label Text="{Binding Supplier.Name}"
                                                   FontAttributes="Bold" />
                                            <Label Text="{Binding OrderDate, StringFormat='Date: {0:D}'}" />
                                            <Label Text="{Binding TotalAmount, StringFormat='Total: {0:C}'}" />
                                            <Label Text="{Binding Status, StringFormat='Status: {0}'}" />
                                        </VerticalStackLayout>
                                        
                                        <VerticalStackLayout Grid.Column="1" HorizontalOptions="End">
                                            <Button Text="Edit"
                                                    Command="{Binding Source={RelativeSource AncestorType={x:Type viewModels:PurchaseOrderViewModel}}, Path=EditOrderCommand}"
                                                    CommandParameter="{Binding .}"
                                                    BackgroundColor="Blue"
                                                    TextColor="White" />
                                            <Button Text="Delete"
                                                    Command="{Binding Source={RelativeSource AncestorType={x:Type viewModels:PurchaseOrderViewModel}}, Path=DeleteOrderCommand}"
                                                    CommandParameter="{Binding Id}"
                                                    IsEnabled="{Binding Status, Converter={StaticResource StatusToDeleteEnabledConverter}}"
                                                    TextColor="Red" />
                                        </VerticalStackLayout>
                                    </Grid>
                                </Frame>
                            </DataTemplate>
                        </CollectionView.ItemTemplate>
                    </CollectionView>
                    
                    <ActivityIndicator IsRunning="{Binding IsProcessing}"
                                       IsVisible="{Binding IsProcessing}" />
                </VerticalStackLayout>
            </Frame>
            
            <!-- Purchase Order Details -->
            <Frame Header="Order Details"
                   IsVisible="{Binding SelectedPurchaseOrder, Converter={StaticResource NullToFalseConverter}}">
                <VerticalStackLayout Spacing="8">
                    <Label Text="{Binding SelectedPurchaseOrder.Supplier.Name}"
                           FontSize="Large"
                           FontAttributes="Bold" />
                    
                    <Label Text="{Binding SelectedPurchaseOrder.Supplier.Address}"
                           FontSize="Medium" />
                    
                    <Label Text="{Binding SelectedPurchaseOrder.Supplier.Phone}"
                           FontSize="Medium" />
                    
                    <Label Text="{Binding SelectedPurchaseOrder.Supplier.Email}"
                           FontSize="Medium" />
                    
                    <Label Text="{Binding SelectedPurchaseOrder.OrderDate, StringFormat='Order Date: {0:D}'}"
                           FontSize="Medium" />
                    
                    <Label Text="{Binding SelectedPurchaseOrder.Status, StringFormat='Status: {0}'}"
                           FontSize="Medium" />
                    
                    <Label Text="{Binding SelectedPurchaseOrder.SubmittedDate, StringFormat='Submitted: {0:D}'}"
                           FontSize="Medium"
                           IsVisible="{Binding SelectedPurchaseOrder.Status, Converter={StaticResource SubmittedDateVisibilityConverter}}" />
                    
                    <Label Text="{Binding SelectedPurchaseOrder.ReceivedDate, StringFormat='Received: {0:D}'}"
                           FontSize="Medium"
                           IsVisible="{Binding SelectedPurchaseOrder.Status, Converter={StaticResource ReceivedDateVisibilityConverter}}" />
                    
                    <Label Text="{Binding SelectedPurchaseOrder.TotalAmount, StringFormat='Total Amount: {0:C}'}"
                           FontSize="Medium" />
                    
                    <CollectionView ItemsSource="{Binding SelectedPurchaseOrder.Items}"
                                      ItemsLayout="VerticalList">
                        <CollectionView.ItemTemplate>
                            <DataTemplate x:DataType="models:PurchaseOrderItem">
                                <Grid ColumnDefinitions="*,Auto">
                                    <Label Text="{Binding RawMaterial.Name}"
                                           Grid.Column="0" />
                                    <Label Text="{Binding Quantity, StringFormat='{0:F0} x {1:C}'}" Arguments="{Binding UnitPrice}"
                                           Grid.Column="1" />
                                </Grid>
                            </DataTemplate>
                        </CollectionView.ItemTemplate>
                    </CollectionView>
                    
                    <HorizontalStackLayout Spacing="8">
                        <Button Text="{Binding SelectedPurchaseOrder.Status, Converter={StaticResource StatusToActionTextConverter}}"
                                Command="{Binding Source={RelativeSource AncestorType={x:Type viewModels:PurchaseOrderViewModel}}, Path={Binding SelectedPurchaseOrder.Status, Converter={StaticResource StatusToCommandConverter}, ConverterParameter={x:Reference This}}"
                                CommandParameter="{Binding SelectedPurchaseOrder.Id}"
                                IsEnabled="{Binding Source={RelativeSource AncestorType={x:Type viewModels:PurchaseOrderViewModel}}, Path=IsProcessing, Converter={StaticResource InvertBooleanConverter}}" />
                        <Button Text="Delete"
                                Command="{Binding Source={RelativeSource AncestorType={x:Type viewModels:PurchaseOrderViewModel}}, Path=DeleteOrderCommand}"
                                CommandParameter="{Binding SelectedPurchaseOrder.Id}"
                                IsEnabled="{Binding SelectedPurchaseOrder.Status, Converter={StaticResource StatusToDeleteEnabledConverter}}"
                                TextColor="Red" />
                    </HorizontalStackLayout>
                </VerticalStackLayout>
            </Frame>
        </VerticalStackLayout>
    </ScrollView>
</ContentPage>