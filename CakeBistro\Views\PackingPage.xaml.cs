using CakeBistro.ViewModels;

namespace CakeBistro.Views
{
    public partial class PackingPage : ContentPage
    {
        public PackingPage()
        {
            InitializeComponent();
            BindingContext = new PackingViewModel(/* TODO: inject PackingService or use a default */);
        }

        public PackingPage(PackingViewModel vm)
        {
            InitializeComponent();
            BindingContext = vm;
        }
    }
}
