
using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using CakeBistro.Models;

namespace CakeBistro.Services
{
    /// <summary>
    /// Interface defining methods for document management operations as specified in PRD requirements
    /// </summary>
    public interface IDocumentService
    {
        /// <summary>
        /// Uploads a document for a finished product with classification of document type.
        /// Implements PRD requirement for document management.
        /// </summary>
        /// <param name="documentUpload">The document upload containing details about the document</param>
        /// <returns>The created document record</returns>
        /// <exception cref="BusinessRuleValidationException">
        /// Thrown when validation fails:
        /// - Document upload is null (ruleName: "DocumentUploadNullCheck")
        /// - Product ID is not valid (ruleName: "InvalidProductID")
        /// - File stream is null or empty (ruleName: "FileStreamRequired")
        /// - Document type is not specified (ruleName: "DocumentTypeRequired")
        /// </exception>
        Task<Document> UploadDocumentAsync(DocumentUpload documentUpload);

        /// <summary>
        /// Retrieves all documents for a specific product filtered by document type.
        /// Implements PRD requirement for document organization.
        /// </summary>
        /// <param name="productId">The unique identifier of the product</param>
        /// <param name="documentType">The type of documents to retrieve (optional)</param>
        /// <returns>A list of documents matching the criteria</returns>
        /// <exception cref="BusinessRuleValidationException">
        /// Thrown when validation fails:
        /// - Product ID is not valid (ruleName: "InvalidProductID")
        /// </exception>
        Task<List<Document>> GetDocumentsByProductAsync(int productId, string documentType = null);

        /// <summary>
        /// Deletes a document with soft delete functionality.
        /// Implements PRD requirement for document management.
        /// </summary>
        /// <param name="documentId">The unique identifier of the document to delete</param>
        /// <returns>True if the document was successfully marked as deleted, false otherwise</returns>
        /// <exception cref="BusinessRuleValidationException">
        /// Thrown when validation fails:
        /// - Document ID is not valid (ruleName: "InvalidDocumentID")
        /// </exception>
        Task<bool> DeleteDocumentAsync(int documentId);

        /// <summary>
        /// Restores a previously deleted document.
        /// Implements PRD requirement for document recovery.
        /// </summary>
        /// <param name="documentId">The unique identifier of the document to restore</param>
        /// <returns>True if the document was successfully restored, false otherwise</returns>
        /// <exception cref="BusinessRuleValidationException">
        /// Thrown when validation fails:
        /// - Document ID is not valid (ruleName: "InvalidDocumentID")
        /// </exception>
        Task<bool> RestoreDocumentAsync(int documentId);

        /// <summary>
        /// Downloads a document by its identifier.
        /// Implements PRD requirement for document access.
        /// </summary>
        /// <param name="documentId">The unique identifier of the document to download</param>
        /// <returns>A stream containing the document content</returns>
        /// <exception cref="BusinessRuleValidationException">
        /// Thrown when validation fails:
        /// - Document ID is not valid (ruleName: "InvalidDocumentID")
        /// </exception>
        Task<Stream> DownloadDocumentAsync(int documentId);
    }

    /// <summary>
    /// Represents a document upload request
    /// </summary>
    public class DocumentUpload
    {
        /// <summary>
        /// Gets or sets the ID of the product this document belongs to
        /// </summary>
        public int ProductId { get; set; }
        
        /// <summary>
        /// Gets or sets the stream containing the document content
        /// </summary>
        public Stream FileStream { get; set; }
        
        /// <summary>
        /// Gets or sets the name of the document
        /// </summary>
        public string Name { get; set; }
        
        /// <summary>
        /// Gets or sets the description of the document
        /// </summary>
        public string Description { get; set; }
        
        /// <summary>
        /// Gets or sets the type/category of the document
        /// </summary>
        public string DocumentType { get; set; }
        
        /// <summary>
        /// Gets or sets the content type of the document (e.g., application/pdf)
        /// </summary>
        public string ContentType { get; set; }
    }
}