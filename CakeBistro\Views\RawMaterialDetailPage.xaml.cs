using System;
using CakeBistro.ViewModels;
using CakeBistro.Services;

namespace CakeBistro
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class RawMaterialDetailPage : BasePage
    {
        private RawMaterialDetailViewModel _viewModel;
        
        public RawMaterialDetailPage(IInventoryService inventoryService)
        {
            InitializeComponent();
            
            // Initialize the view model
            _viewModel = new RawMaterialDetailViewModel(inventoryService);
            BindingContext = _viewModel;
            
            // Subscribe to go back event
            MessagingCenter.Subscribe<RawMaterialDetailViewModel>(this, "GoBack", (sender) =>
            {
                Navigation.PopAsync();
            });
            
            // Subscribe to adjustment event
            MessagingCenter.Subscribe<RawMaterialDetailViewModel>(this, "ShowAdjustmentDialog", (sender) =>
            {
                // In a real app, show an adjustment dialog here
                DisplayAlert("Adjust Stock", "Would show adjustment dialog here", "OK");
            });
        }
        
        // Handle appearing of the page
        protected override async void OnAppearing()
        {
            base.OnAppearing();
            
            // Get the material ID from navigation parameter
            if (Navigation.NavigationStack.Count > 1)
            {
                var previousPage = Navigation.NavigationStack[Navigation.NavigationStack.Count - 2];
                if (previousPage is InventoryPage inventoryPage &&
                    inventoryPage.BindingContext is InventoryViewModel inventoryViewModel)
                {
                    // Get selected material from inventory view model
                    var selectedItem = inventoryViewModel.RawMaterials.FirstOrDefault();
                    if (selectedItem != null)
                    {
                        await _viewModel.InitializeAsync(selectedItem.Id);
                    }
                }
            }
        }
    }
}
