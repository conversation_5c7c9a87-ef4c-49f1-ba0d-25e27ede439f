using CakeBistro.ViewModels;
using Microsoft.Maui.Controls;
using System;

namespace CakeBistro.Views
{
    public partial class CostCalculationDashboard : ContentPage
    {
        public CostCalculationDashboard(CostCalculationDashboardViewModel vm)
        {
            InitializeComponent();
            BindingContext = vm;
        }

        private void OnCalculateCostClicked(object sender, EventArgs e)
        {
            // TODO: Add cost calculation logic and animated feedback
            CostResultLabel.Text = "Total Cost: $123.45";
        }
    }
}
