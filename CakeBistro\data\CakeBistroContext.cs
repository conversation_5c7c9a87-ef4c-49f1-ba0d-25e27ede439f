using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using CakeBistro.Core.Models;
using Microsoft.EntityFrameworkCore;

namespace CakeBistro.Data
{
    public class CakeBistroContext : DbContext
    {
        public CakeBistroContext(DbContextOptions<CakeBistroContext> options) : base(options) { }
        public CakeBistroContext() : base() { }

        #region DbSet Properties
        // Asset Management
        public DbSet<Asset> Assets { get; set; }
        public DbSet<Depreciation> Depreciations { get; set; }
        public DbSet<Maintenance> Maintenances { get; set; }

        // Logistics Management
        public DbSet<Vehicle> Vehicles { get; set; }
        public DbSet<Driver> Drivers { get; set; }
        public DbSet<MaintenanceRecord> MaintenanceRecords { get; set; }
        public DbSet<DeliveryManifest> DeliveryManifests { get; set; }
        public DbSet<DeliveryStop> DeliveryStops { get; set; }

        // Inventory Management
        public DbSet<RawMaterial> RawMaterials { get; set; }
        public DbSet<InventoryBatch> InventoryBatches { get; set; }
        public DbSet<StockMovement> StockMovements { get; set; }
        public DbSet<StockTake> StockTakes { get; set; }
        public DbSet<StockAdjustment> StockAdjustments { get; set; }
        public DbSet<InterBranchTransfer> InterBranchTransfers { get; set; }
        public DbSet<InventoryItemValuation> InventoryItemValuations { get; set; }
        public DbSet<InventoryValuationReport> InventoryValuationReports { get; set; }
        public DbSet<ExpiringBatchAlert> ExpiringBatchAlerts { get; set; }
        public DbSet<ExpiringBatchReport> ExpiringBatchReports { get; set; }
        public DbSet<ExpiringBatchDetail> ExpiringBatchDetails { get; set; }
        public DbSet<LowStockAlert> LowStockAlerts { get; set; }

        // Product Management
        public DbSet<Product> Products { get; set; }
        public DbSet<Recipe> Recipes { get; set; }
        public DbSet<RecipeItem> RecipeItems { get; set; }
        public DbSet<ProductionBatch> ProductionBatches { get; set; }
        public DbSet<QualityCheck> QualityChecks { get; set; }

        // Purchase Management
        public DbSet<PurchaseOrder> PurchaseOrders { get; set; }
        public DbSet<PurchaseOrderItem> PurchaseOrderItems { get; set; }
        public DbSet<GoodsReceipt> GoodsReceipts { get; set; }
        public DbSet<GoodsReceiptItem> GoodsReceiptItems { get; set; }
        public DbSet<Supplier> Suppliers { get; set; }

        // Sales Management
        public DbSet<SalesOrder> SalesOrders { get; set; }
        public DbSet<SalesOrderItem> SalesOrderItems { get; set; }
        public DbSet<Customer> Customers { get; set; }

        // Reporting
        public DbSet<InventoryReport> InventoryReports { get; set; }
        public DbSet<PurchaseReport> PurchaseReports { get; set; }
        public DbSet<SalesReport> SalesReports { get; set; }
        public DbSet<RawMaterialDocument> RawMaterialDocuments { get; set; }
        public DbSet<RawMaterialStockStatement> RawMaterialStockStatements { get; set; }

        // User Management
        public DbSet<User> Users { get; set; }
        public DbSet<Role> Roles { get; set; }
        public DbSet<UserActivity> UserActivities { get; set; }
        public DbSet<Permission> Permissions { get; set; }
        public DbSet<UserPermission> UserPermissions { get; set; }
        public DbSet<RolePermission> RolePermissions { get; set; }
        #endregion

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            if (!optionsBuilder.IsConfigured)
            {
                optionsBuilder.UseSqlite("Data Source=data/cake_bistro.db");
            }
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            #region Logistics Configuration
            modelBuilder.Entity<Vehicle>()
                .HasMany(v => v.MaintenanceHistory)
                .WithOne(m => m.Vehicle)
                .HasForeignKey(m => m.VehicleId)
                .OnDelete(DeleteBehavior.Cascade);

            modelBuilder.Entity<Vehicle>()
                .HasMany(v => v.DeliveryManifests)
                .WithOne(m => m.Vehicle)
                .HasForeignKey(m => m.VehicleId)
                .OnDelete(DeleteBehavior.Restrict);
            modelBuilder.Entity<Driver>()
                .HasMany(d => d.DeliveryManifests)
                .WithOne(m => m.Driver)
                .HasForeignKey(m => m.DriverId)
                .OnDelete(DeleteBehavior.Restrict);
            modelBuilder.Entity<DeliveryManifest>()
                .HasMany(m => m.Stops)
                .WithOne(s => s.DeliveryManifest)
                .HasForeignKey(s => s.DeliveryManifestId)
                .OnDelete(DeleteBehavior.Cascade);
            modelBuilder.Entity<DeliveryStop>()
                .HasOne(s => s.SalesOrder)
                .WithMany()
                .HasForeignKey(s => s.SalesOrderId)
                .OnDelete(DeleteBehavior.Restrict);
            #endregion

            #region Stock Movement Configuration
            modelBuilder.Entity<StockMovement>()
                .HasOne(sm => sm.RawMaterial)
                .WithMany(rm => rm.StockMovements)
                .HasForeignKey(sm => sm.ProductId)
                .OnDelete(DeleteBehavior.Restrict);
            #endregion

            #region Purchase Order Configuration
            modelBuilder.Entity<PurchaseOrderItem>()
                .HasOne(poi => poi.PurchaseOrder)
                .WithMany(po => po.Items)
                .HasForeignKey(poi => poi.PurchaseOrderId)
                .OnDelete(DeleteBehavior.Cascade);
            modelBuilder.Entity<PurchaseOrderItem>()
                .HasOne(poi => poi.RawMaterial)
                .WithMany(rm => rm.PurchaseOrderItems)
                .HasForeignKey(poi => poi.RawMaterialId)
                .OnDelete(DeleteBehavior.Restrict);
            #endregion

            #region Sales Order Configuration
            modelBuilder.Entity<SalesOrderItem>()
                .HasOne(soi => soi.SalesOrder)
                .WithMany(so => so.Items)
                .HasForeignKey(soi => soi.SalesOrderId)
                .OnDelete(DeleteBehavior.Cascade);
            modelBuilder.Entity<SalesOrderItem>()
                .HasOne(soi => soi.Product)
                .WithMany()
                .HasForeignKey(soi => soi.ProductId)
                .OnDelete(DeleteBehavior.Restrict);
            #endregion

            #region Inventory Configuration
            modelBuilder.Entity<InventoryBatch>()
                .HasOne(ib => ib.RawMaterial)
                .WithMany(rm => rm.InventoryBatches)
                .HasForeignKey(ib => ib.RawMaterialId)
                .OnDelete(DeleteBehavior.Restrict);
            modelBuilder.Entity<InventoryItemValuation>()
                .HasOne(iiv => iiv.InventoryValuationReport)
                .WithMany(iv => iv.ItemValuations)
                .HasForeignKey(iiv => iiv.InventoryValuationReportId)
                .OnDelete(DeleteBehavior.Cascade);
            #endregion

            #region Expiring Batch Configuration
            modelBuilder.Entity<ExpiringBatchAlert>()
                .HasOne(eba => eba.InventoryBatch)
                .WithMany()
                .HasForeignKey(eba => eba.InventoryBatchId)
                .OnDelete(DeleteBehavior.Restrict);
            modelBuilder.Entity<ExpiringBatchAlert>()
                .HasOne(eba => eba.RawMaterial)
                .WithMany(rm => rm.ExpiringBatchAlerts)
                .HasForeignKey(eba => eba.RawMaterialId)
                .OnDelete(DeleteBehavior.Restrict);
            modelBuilder.Entity<ExpiringBatchDetail>()
                .HasOne(ebd => ebd.ExpiringBatchReport)
                .WithMany(ebr => ebr.ExpiringBatchDetails)
                .HasForeignKey(ebd => ebd.ExpiringBatchReportId)
                .OnDelete(DeleteBehavior.Cascade);
            #endregion

            #region InventoryBatch Configuration
            modelBuilder.Entity<InventoryBatch>()
                .Property(ib => ib.Status)
                .HasConversion<string>();
            modelBuilder.Entity<InventoryBatch>()
                .Property(ib => ib.TraceabilityCode)
                .HasMaxLength(100);
            #endregion
        }
    }
}
