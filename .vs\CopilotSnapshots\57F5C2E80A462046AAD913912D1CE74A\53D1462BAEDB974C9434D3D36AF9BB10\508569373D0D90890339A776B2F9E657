﻿using CakeBistro.Data;
using CakeBistro.Services;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;

namespace CakeBistro
{
    public partial class App : Application
    {
        public App()
        {
            InitializeComponent();
            MainPage = new AppShell(); // Use parameterless constructor
            // Async initialization will be triggered after construction
            InitializeAsync();
        }

        private async void InitializeAsync()
        {
            var serviceProvider = MauiProgram.ServiceProvider;
            var logger = serviceProvider.GetService<ILogger<App>>();

            // Register global exception handler
            var exceptionHandler = serviceProvider.GetService<GlobalExceptionHandler>();
            exceptionHandler?.RegisterHandlers();

            // Ensure main database is created
            try
            {
                var db = serviceProvider.GetService<CakeBistroContext>();
                if (db != null)
                    await db.Database.EnsureCreatedAsync();
            }
            catch (Exception ex)
            {
                logger?.LogError(ex, "Error ensuring main database: {Message}", ex.Message);
            }

            // Ensure identity database is created (if present)
            try
            {
                var identityContext = serviceProvider.GetService<Models.Identity.CakeBistroIdentityContext>();
                if (identityContext != null)
                    await identityContext.Database.EnsureCreatedAsync();
            }
            catch (Exception ex)
            {
                logger?.LogError(ex, "Error ensuring identity database: {Message}", ex.Message);
            }
        }
    }
}