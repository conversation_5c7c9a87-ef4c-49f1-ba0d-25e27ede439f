<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:vm="clr-namespace:CakeBistro.ViewModels"
             xmlns:model="clr-namespace:CakeBistro.Models"
             x:Class="CakeBistro.Views.RecipesPage"
             x:DataType="vm:RecipeViewModel"
             Title="{Binding Title}">

    <Grid RowDefinitions="Auto,*">
        <!-- Header with Add Recipe button -->
        <HorizontalStackLayout Grid.Row="0" 
                             Padding="10"
                             Spacing="10">
            <Button Text="Add Recipe"
                    Command="{Binding AddRecipeCommand}"
                    SemanticProperties.Hint="Add a new recipe"/>
            <ActivityIndicator IsRunning="{Binding IsBusy}"/>
        </HorizontalStackLayout>

        <!-- Recipe List -->
        <RefreshView Grid.Row="1"
                     Command="{Binding LoadRecipesCommand}"
                     IsRefreshing="{Binding IsBusy}">
            <CollectionView ItemsSource="{Binding Recipes}"
                          SelectedItem="{Binding SelectedRecipe}"
                          SelectionMode="Single">
                <CollectionView.ItemTemplate>
                    <DataTemplate x:DataType="model:Recipe">
                        <SwipeView>
                            <SwipeView.RightItems>
                                <SwipeItems>
                                    <SwipeItem Text="Delete"
                                             BackgroundColor="Red"
                                             Command="{Binding Source={RelativeSource AncestorType={x:Type vm:RecipeViewModel}}, Path=DeleteRecipeCommand}"
                                             CommandParameter="{Binding .}"/>
                                </SwipeItems>
                            </SwipeView.RightItems>

                            <Grid Padding="10">
                                <Frame>
                                    <Grid RowDefinitions="Auto,Auto,Auto"
                                          ColumnDefinitions="*,Auto">
                                        
                                        <!-- Recipe Name -->
                                        <Label Grid.Row="0"
                                               Grid.Column="0"
                                               Text="{Binding Name}"
                                               FontSize="16"
                                               FontAttributes="Bold"/>

                                        <!-- Estimated Cost -->
                                        <Label Grid.Row="0"
                                               Grid.Column="1"
                                               Text="{Binding EstimatedCostPerBatch, StringFormat='${0:N2}'}"
                                               FontSize="16"/>

                                        <!-- Description -->
                                        <Label Grid.Row="1"
                                               Grid.Column="0"
                                               Grid.ColumnSpan="2"
                                               Text="{Binding Description}"
                                               FontSize="14"
                                               LineBreakMode="TailTruncation"/>

                                        <!-- Action Buttons -->
                                        <HorizontalStackLayout Grid.Row="2"
                                                             Grid.Column="0"
                                                             Grid.ColumnSpan="2"
                                                             Spacing="10">
                                            <Button Text="Edit"
                                                    Command="{Binding Source={RelativeSource AncestorType={x:Type vm:RecipeViewModel}}, Path=EditRecipeCommand}"
                                                    CommandParameter="{Binding .}"/>
                                            
                                            <Button Text="Create Batch"
                                                    Command="{Binding Source={RelativeSource AncestorType={x:Type vm:RecipeViewModel}}, Path=CreateProductionBatchCommand}"
                                                    CommandParameter="{Binding .}"/>
                                        </HorizontalStackLayout>
                                    </Grid>
                                </Frame>
                            </Grid>
                        </SwipeView>
                    </DataTemplate>
                </CollectionView.ItemTemplate>
            </CollectionView>
        </RefreshView>
    </Grid>
</ContentPage>
