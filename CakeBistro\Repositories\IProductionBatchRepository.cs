using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using CakeBistro.Core.Models;

namespace CakeBistro.Repositories
{
    public interface IProductionBatchRepository : IRepository<ProductionBatch>
    {
        Task<IEnumerable<ProductionBatch>> GetBatchesByDateRangeAsync(DateTime startDate, DateTime endDate);
        Task<IEnumerable<ProductionBatch>> GetBatchesByStatusAsync(string status);
        Task<IEnumerable<ProductionBatch>> GetBatchesByRecipeAsync(int recipeId);
        Task<decimal> CalculateActualCostAsync(int batchId);
    }
}
