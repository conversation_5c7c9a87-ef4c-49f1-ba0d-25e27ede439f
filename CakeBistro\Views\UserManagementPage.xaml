<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="MCakeBistro.Views.UserManagementPage"
             Title="User Management">
    <VerticalStackLayout>
        <!-- Search and add -->
        <HorizontalStackLayout Padding="10" Spacing="10">
            <Entry Placeholder="Search users..."
                   Text="{Binding SearchText}"
                   HorizontalOptions="FillAndExpand"
                   ReturnType="Search"/>
            <Button Text="Add"
                    Command="{Binding AddUserCommand}"
                    BackgroundColor="#e74c3c"
                    TextColor="White"/>
        </HorizontalStackLayout>
        
        <!-- Users list -->
        <CollectionView ItemsSource="{Binding Users}"
                        SelectedItem="{Binding SelectedUser}"
                        SelectionMode="Single">
            <CollectionView.ItemTemplate>
                <DataTemplate>
                    <Grid Padding="10">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        
                        <VerticalStackLayout>
                            <Label Text="{Binding Username}"
                                   FontAttributes="Bold"/>
                            <Label Text="{Binding Email}"
                                   TextColor="Gray"/>
                            <Label Text="{Binding Role.Name}"
                                   TextColor="Gray"/>
                        </VerticalStackLayout>
                        
                        <Image Grid.Column="1"
                               Source="delete.png"
                               HeightRequest="20"
                               WidthRequest="20"
                               HorizontalOptions="End"
                               VerticalOptions="Center"
                               Command="{Binding BindingContext.DeleteUserCommand, Source={x:Reference UserManagementPage}}"
                               CommandParameter="{Binding .}"
                               Margin="0,0,10,0"/>
                    </Grid>
                </DataTemplate>
            </CollectionView.ItemTemplate>
        </CollectionView>
        
        <!-- User details -->
        <ContentView IsVisible="{Binding SelectedUser != null}">
            <VerticalStackLayout Padding="10">
                <Label Text="User Details" FontSize="Large" FontAttributes="Bold"/>
                
                <Grid ColumnDefinitions="*,*" RowDefinitions="Auto,Auto,Auto,Auto,Auto" Spacing="10">
                    <Label Text="Username:" Grid.Row="0" Grid.Column="0" TextColor="Gray"/>
                    <Entry Text="{Binding SelectedUser.Username}"
                           Grid.Row="0" Grid.Column="1"/>
                    
                    <Label Text="Email:" Grid.Row="1" Grid.Column="0" TextColor="Gray"/>
                    <Entry Text="{Binding SelectedUser.Email}"
                           Grid.Row="1" Grid.Column="1"/>
                    
                    <Label Text="First Name:" Grid.Row="2" Grid.Column="0" TextColor="Gray"/>
                    <Entry Text="{Binding SelectedUser.FirstName}"
                           Grid.Row="2" Grid.Column="1"/>
                    
                    <Label Text="Last Name:" Grid.Row="3" Grid.Column="0" TextColor="Gray"/>
                    <Entry Text="{Binding SelectedUser.LastName}"
                           Grid.Row="3" Grid.Column="1"/>
                    
                    <Label Text="Status:" Grid.Row="4" Grid.Column="0" TextColor="Gray"/>
                    <Switch IsToggled="{Binding SelectedUser.IsActive}"
                            Grid.Row="4" Grid.Column="1"/>
                </Grid>
                
                <HorizontalStackLayout Spacing="10" Margin="0,20,0,0">
                    <Button Text="Save"
                            Command="{Binding UpdateProfileCommand}"
                            BackgroundColor="#e74c3c"
                            TextColor="White"
                            HorizontalOptions="FillAndExpand"/>
                    <Button Text="Cancel"
                            Command="{Binding CancelEditCommand}"
                            BackgroundColor="#bdc3c7"
                            TextColor="White"
                            HorizontalOptions="FillAndExpand"/>
                </HorizontalStackLayout>
                
                <Label Text="{Binding StatusMessage}"
                       IsVisible="{Binding StatusMessage, Converter={StaticResource EmptyToFalseConverter}}"
                       TextColor="{Binding StatusColor}" />
            </VerticalStackLayout>
        </ContentView>
        
        <!-- Navigation -->
        <Button Text="Back to Main"
                Command="{Binding NavigateBackCommand}"
                BackgroundColor="#bdc3c7"
                TextColor="White"
                HorizontalOptions="Center"
                Margin="10"/>
    </VerticalStackLayout>
</ContentPage>