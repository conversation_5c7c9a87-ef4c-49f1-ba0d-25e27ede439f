<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="CakeBistro.Views.StockTakePage"
             Title="Stock Take">
    <StackLayout Padding="20">
        <Label Text="Stock Take Records" FontSize="24" FontAttributes="Bold" HorizontalOptions="Center" />
        <CollectionView x:Name="StockTakeListView" SelectionMode="Single" SelectionChanged="OnStockTakeSelected">
            <CollectionView.ItemTemplate>
                <DataTemplate>
                    <SwipeView>
                        <SwipeView.RightItems>
                            <SwipeItems>
                                <SwipeItem Text="Delete" BackgroundColor="Red" Invoked="OnDeleteStockTake" CommandParameter="{Binding .}"/>
                            </SwipeItems>
                        </SwipeView.RightItems>
                        <Frame Margin="5" Padding="10" BorderColor="#ccc">
                            <StackLayout>
                                <Label Text="{Binding Date, StringFormat='Date: {0:yyyy-MM-dd}'}" FontSize="16" />
                                <Label Text="{Binding Notes}" FontSize="14" TextColor="Gray" />
                            </StackLayout>
                        </Frame>
                    </SwipeView>
                </DataTemplate>
            </CollectionView.ItemTemplate>
        </CollectionView>
        <Button Text="Add Stock Take" Margin="0,20,0,0" Clicked="OnAddStockTakeClicked" />
    </StackLayout>
</ContentPage>
