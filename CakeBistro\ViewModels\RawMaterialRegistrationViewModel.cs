using System.Collections.ObjectModel;
using System.Windows.Input;
using CakeBistro.Core.Models;
using CakeBistro.Services;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Microsoft.UI.Xaml.Media;

namespace CakeBistro.ViewModels
{
    public partial class RawMaterialRegistrationViewModel : ObservableObject
    {
        private readonly IInventoryService _inventoryService;
        private readonly ISupplierService _supplierService;

        [ObservableProperty]
        private string name;
        [ObservableProperty]
        private string category;
        [ObservableProperty]
        private decimal price;
        [ObservableProperty]
        private Supplier selectedSupplier;
        public ObservableCollection<Supplier> Suppliers { get; } = new();

        private Color _statusColor = Colors.Transparent;
        public Color StatusColor
        {
            get => _statusColor;
            set => SetProperty(ref _statusColor, value);
        }

        public RawMaterialRegistrationViewModel(IInventoryService inventoryService, ISupplierService supplierService)
        {
            _inventoryService = inventoryService;
            _supplierService = supplierService;
            LoadSuppliers();
        }

        private async void LoadSuppliers()
        {
            var suppliers = await _supplierService.GetAllSuppliersAsync();
            Suppliers.Clear();
            foreach (var s in suppliers)
                Suppliers.Add(s);
        }

        [RelayCommand]
        private async Task RegisterRawMaterial()
        {
            var material = new RawMaterial
            {
                Name = Name,
                Category = Category,
                Price = Price,
                SupplierId = SelectedSupplier?.Id ?? 0
            };
            try
            {
                await _inventoryService.RegisterRawMaterialAsync(material);
                StatusMessage = "Raw material registered!";
                StatusColor = Color.FromArgb("#388E3C");
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error: {ex.Message}";
                StatusColor = Color.FromArgb("#D32F2F");
            }
            finally
            {
                Name = string.Empty;
                Category = string.Empty;
                Price = 0;
                SelectedSupplier = null;
            }
        }

        [ObservableProperty]
        private string statusMessage;
    }
}
