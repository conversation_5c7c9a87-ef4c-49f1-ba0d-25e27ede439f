using CakeBistro.Core.Models;
using CakeBistro.data;
using Microsoft.EntityFrameworkCore;

namespace CakeBistro.Repositories
{
    public interface IPurchaseOrderItemRepository : IRepository<PurchaseOrderItem>
    {
        Task<IEnumerable<PurchaseOrderItem>> GetItemsByOrderAsync(Guid orderId);
    }

    public class PurchaseOrderItemRepository : BaseRepository<PurchaseOrderItem>, IPurchaseOrderItemRepository
    {
        public PurchaseOrderItemRepository(CakeBistroContext context) : base(context)
        {
        }

        public async Task<IEnumerable<PurchaseOrderItem>> GetItemsByOrderAsync(Guid orderId)
        {
            return await _context.PurchaseOrderItems
                .Where(i => i.PurchaseOrderId == orderId)
                .ToListAsync();
        }

        public override async Task DeleteAsync(Guid id)
        {
            var item = await _context.PurchaseOrderItems.FindAsync(id);
            if (item != null)
            {
                _context.PurchaseOrderItems.Remove(item);
                await _context.SaveChangesAsync();
            }
        }
    }
}
