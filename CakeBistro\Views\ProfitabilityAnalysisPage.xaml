<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:vm="clr-namespace:CakeBistro.ViewModels"
             x:Class="CakeBistro.Views.ProfitabilityAnalysisPage"
             Title="Profitability Analysis">
    <ContentPage.BindingContext>
        <vm:ProfitabilityAnalysisReportViewModel />
    </ContentPage.BindingContext>
    <ScrollView>
        <VerticalStackLayout Padding="20">
            <Label Text="Profitability Analysis" FontSize="24" HorizontalOptions="Center" />
            <Button Text="Refresh" Command="{Binding AnalyzeProfitabilityCommand}" />
            <Picker ItemsSource="{Binding Products}"
                    ItemDisplayBinding="{Binding Name}"
                    SelectedItem="{Binding SelectedProduct, Mode=TwoWay}" />
            <Label Text="{Binding ProfitabilityResult}" FontAttributes="Bold" FontSize="18" TextColor="{StaticResource Primary}" />
        </VerticalStackLayout>
    </ScrollView>
</ContentPage>
