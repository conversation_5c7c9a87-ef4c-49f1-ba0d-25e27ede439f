<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:zxing="clr-namespace:ZXing.Net.Maui.Controls;assembly=ZXing.Net.Maui.Controls"
             x:Class="CakeBistro.Views.BarcodeScannerPage"
             Title="Barcode Scanner">
    <StackLayout>
        <zxing:BarcodeScannerView x:Name="barcodeScannerView"
                               Options="{Binding BarcodeOptions}"
                               ScanResult="{Binding BarcodeScanned}"
                               VerticalOptions="FillAndExpand"
                               HorizontalOptions="FillAndExpand" />
        
        <Button Text="Scan"
                Command="{Binding ScanCommand}"
                VerticalOptions="End"
                HorizontalOptions="Center" />
        
        <Button Text="Manual Entry"
                Command="{Binding ManualEntryCommand}"
                VerticalOptions="End"
                HorizontalOptions="Center" />
    </StackLayout>
</ContentPage>