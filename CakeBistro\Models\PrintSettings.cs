/// <summary>
/// Represents settings for printed documents
/// </summary>
public class PrintSettings
{
    /// <summary>
    /// Gets or sets the top margin in millimeters
    /// </summary>
    public double MarginTop { get; set; }
    
    /// <summary>
    /// Gets or sets the bottom margin in millimeters
    /// </summary>
    public double MarginBottom { get; set; }
    
    /// <summary>
    /// Gets or sets the left margin in millimeters
    /// </summary>
    public double MarginLeft { get; set; }
    
    /// <summary>
    /// Gets or sets the right margin in millimeters
    /// </summary>
    public double MarginRight { get; set; }
    
    /// <summary>
    /// Gets or sets the height of the header section in millimeters
    /// </summary>
    public double HeaderHeight { get; set; }
    
    /// <summary>
    /// Gets or sets the height of the footer section in millimeters
    /// </summary>
    public double FooterHeight { get; set; }
    
    /// <summary>
    /// Gets or sets the paper size (A4, Letter, etc.)
    /// </summary>
    public string PaperSize { get; set; }
    
    /// <summary>
    /// Gets or sets the default orientation (Portrait or Landscape)
    /// </summary>
    public string Orientation { get; set; }
    
    /// <summary>
    /// Gets or sets the DPI (dots per inch) for printing
    /// </summary>
    public int Dpi { get; set; }
}