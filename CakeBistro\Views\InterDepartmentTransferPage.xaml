<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:vm="clr-namespace:CakeBistro.ViewModels"
             x:Class="CakeBistro.Views.InterDepartmentTransferPage"
             Title="Inter-Departmental Transfer">
    <ContentPage.BindingContext>
        <vm:InterDepartmentTransferViewModel />
    </ContentPage.BindingContext>
    <ScrollView>
        <VerticalStackLayout Padding="20">
            <Label Text="Inter-Departmental Transfer" FontSize="24" HorizontalOptions="Center" />
            <Picker Title="Select Completed Batch" ItemsSource="{Binding CompletedBatches}" ItemDisplayBinding="{Binding Id}" SelectedItem="{Binding SelectedBatch}" />
            <Entry Placeholder="Transfer Quantity" Text="{Binding TransferQuantity}" Keyboard="Numeric" />
            <Editor Placeholder="Notes (optional)" Text="{Binding TransferNotes}" />
            <Button Text="Transfer to Packing" Command="{Binding TransferToPackingCommand}" />
        </VerticalStackLayout>
    </ScrollView>
</ContentPage>
