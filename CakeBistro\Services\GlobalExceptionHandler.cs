using Microsoft.Extensions.Logging;
using System;

namespace CakeBistro.Services
{
    public class GlobalExceptionHandler
    {
        private readonly ILogger<GlobalExceptionHandler> _logger;

        public GlobalExceptionHandler(ILogger<GlobalExceptionHandler> logger)
        {
            _logger = logger;
        }

        public void RegisterHandlers()
        {
            // Handle UI thread exceptions
            AppDomain.CurrentDomain.UnhandledException += (sender, args) =>
            {
                var exception = (Exception)args.ExceptionObject;
                _logger.LogCritical(exception, "Unhandled exception: {Message}", exception.Message);
                // In a real application, you might want to show a friendly error message to the user
            };

            // Handle task exceptions
            TaskScheduler.UnobservedTaskException += (sender, args) =>
            {
                _logger.LogWarning(args.Exception, "Unobserved task exception: {Message}", args.Exception.Message);
                args.SetObserved();
            };
        }
    }
}