using CakeBistro.ViewModels;
using CakeBistro.Services;

namespace CakeBistro.Views
{
    [XamlCompilation(XamlCompilationOptions.Compile)]
    public partial class SupplierPage : BasePage
    {
        private SupplierViewModel _viewModel;
        
        public SupplierPage(IInventoryService inventoryService)
        {
            InitializeComponent();
            
            // Initialize the view model
            _viewModel = new SupplierViewModel(inventoryService);
            BindingContext = _viewModel;
            
            // Subscribe to add CakeBistro.Core.Models.Supplier event
            MessagingCenter.Subscribe<SupplierViewModel>(this, "ShowAddSupplier", (sender) =>
            {
                // Navigate to add CakeBistro.Core.Models.Supplier page
                Navigation.PushAsync(new SupplierDetailPage(inventoryService));
            });
            
            // Subscribe to material details event
            MessagingCenter.Subscribe<SupplierViewModel>(this, "ShowSupplierDetails", async (sender) =>
            {
                // Navigate to CakeBistro.Core.Models.Supplier details page
                await Navigation.PushAsync(new SupplierDetailPage(inventoryService));
            });
            
            // Subscribe to navigate to inventory event
            MessagingCenter.Subscribe<SupplierViewModel>(this, "NavigateToInventory", async (sender) =>
            {
                // Navigate to inventory page
                await Navigation.PushAsync(new InventoryPage(inventoryService));
            });
        }
        
        // Handle appearing of the page
        protected override async void OnAppearing()
        {
            base.OnAppearing();
            
            // Refresh data when page appears
            await _viewModel.ExecuteRefreshCommand();
        }
    }
}
