
    public class CakeBistroContext : DbContext
    {
        // ... existing DbSet properties ...

        /// <summary>
        /// Gets or sets the collection of stock adjustments.
        /// </summary>
        public DbSet<StockAdjustment> StockAdjustments { get; set; }

        /// <summary>
        /// Gets or sets the custom themes for theme management
        /// </summary>
        public DbSet<CustomTheme> CustomThemes { get; set; }

        /// <summary>
        /// Gets or sets the user theme preferences
        /// </summary>
        public DbSet<ThemePreferences> ThemePreferences { get; set; }

        /// <summary>
        /// Gets or sets the low stock alerts for inventory monitoring
        /// </summary>
        public DbSet<LowStockAlert> LowStockAlerts { get; set; }

        /// <summary>
        /// Gets or sets the profitability analyses for product profitability tracking
        /// </summary>
        public DbSet<ProfitabilityAnalysis> ProfitabilityAnalyses { get; set; }

        /// <summary>
        /// Gets or sets the documents for managing product documentation
        /// </summary>
        public DbSet<Document> Documents { get; set; }

        /// <summary>
        /// Gets or sets the financial reports
        /// </summary>
        public DbSet<FinancialReport> FinancialReports { get; set; }

        // ... existing OnModelCreating configuration ...

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Configure relationships and constraints
            modelBuilder.Entity<DamageRecord>()
                .HasOne(d => d.Batch)
                .WithMany()
                .HasForeignKey(d => d.BatchId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<InterBranchTransfer>()
                .HasOne(t => t.Product)
                .WithMany()
                .HasForeignKey(t => t.ProductId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<InterBranchTransfer>()
                .HasOne(t => t.Batch)
                .WithMany()
                .HasForeignKey(t => t.BatchId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<InventoryBatch>()
                .HasOne(b => b.Product)
                .WithMany(p => p.Batches)
                .HasForeignKey(b => b.ProductId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<ProfitabilityAnalysis>()
                .HasNoKey()  // This is a query type, not an entity that we manage directly
                .ToView("vwProfitabilityAnalysis")  // Assuming we'll implement this as a database view
                .HasKey(pa => pa.ProductId);

            modelBuilder.Entity<Document>()
                .HasOne(d => d.Product)
                .WithMany()
                .HasForeignKey(d => d.ProductId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<LowStockAlert>()
                .HasIndex(a => new { a.ItemId, a.ItemType })  // Index for efficient querying
                .IsUnique(false);  // Allow multiple alerts for the same item over time

            modelBuilder.Entity<CustomTheme>()
                .HasIndex(t => t.Name)  // Index for efficient theme name queries
                .IsUnique();  // Theme names must be unique

            modelBuilder.Entity<CustomTheme>()
                .Property(t => t.IsDeleted)
                .HasDefaultValue(false);  // Default value for soft delete flag

            modelBuilder.Entity<CustomTheme>()
                .HasQueryFilter(t => !t.IsDeleted);  // Global filter to exclude deleted themes

            // Add more model configurations as needed
        }
    }