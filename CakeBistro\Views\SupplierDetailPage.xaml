<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="CakeBistro.Views.SupplierDetailPage"
             Title="Supplier Details">
    <VerticalStackLayout Margin="20">
        <!-- Loading Indicator -->
        <ActivityIndicator IsRunning="{Binding IsBusy}"
                           IsVisible="{Binding IsBusy}"
                           HorizontalOptions="Center"
                           VerticalOptions="Center" />
        
        <!-- Supplier Information -->
        <Frame BackgroundColor="#F5F5F5" Margin="0,10">
            <VerticalStackLayout Spacing="15">
                <!-- Supplier Name -->
                <HorizontalStackLayout Spacing="10">
                    <Label Text="Name:" WidthRequest="120" VerticalOptions="Center" />
                    <Entry Text="{Binding Supplier.Name}"
                           Placeholder="Enter supplier name"
                           HorizontalOptions="FillAndExpand" />
                </HorizontalStackLayout>
                
                <!-- Contact Name -->
                <HorizontalStackLayout Spacing="10">
                    <Label Text="Contact:" WidthRequest="120" VerticalOptions="Center" />
                    <Entry Text="{Binding Supplier.ContactName}"
                           Placeholder="Enter contact name"
                           HorizontalOptions="FillAndExpand" />
                </HorizontalStackLayout>
                
                <!-- Category -->
                <HorizontalStackLayout Spacing="10">
                    <Label Text="Category:" WidthRequest="120" VerticalOptions="Center" />
                    <Picker ItemsSource="{Binding Categories}"
                             SelectedItem="{Binding Supplier.Category}"
                             HorizontalOptions="FillAndExpand">
                        <Picker.Items>
                            <x:String>Dry Goods</x:String>
                            <x:String>Dairy</x:String>
                            <x:String>Bakery Supplies</x:String>
                            <x:String>Packaging</x:String>
                            <x:String>Equipment</x:String>
                        </Picker.Items>
                    </Picker>
                </HorizontalStackLayout>
                
                <!-- Email -->
                <HorizontalStackLayout Spacing="10">
                    <Label Text="Email:" WidthRequest="120" VerticalOptions="Center" />
                    <Entry Text="{Binding Supplier.Email}"
                           Placeholder="Enter email address"
                           Keyboard="Email"
                           HorizontalOptions="FillAndExpand" />
                </HorizontalStackLayout>
                
                <!-- Phone -->
                <HorizontalStackLayout Spacing="10">
                    <Label Text="Phone:" WidthRequest="120" VerticalOptions="Center" />
                    <Entry Text="{Binding Supplier.Phone}"
                           Placeholder="Enter phone number"
                           Keyboard="Telephone"
                           HorizontalOptions="FillAndExpand" />
                </HorizontalStackLayout>
                
                <!-- Address -->
                <HorizontalStackLayout Spacing="10">
                    <Label Text="Address:" WidthRequest="120" VerticalOptions="Top" />
                    <Editor Text="{Binding Supplier.Address}"
                             Placeholder="Enter address"
                             HorizontalOptions="FillAndExpand"
                             HeightRequest="100" />
                </HorizontalStackLayout>
                
                <!-- Account Number -->
                <HorizontalStackLayout Spacing="10">
                    <Label Text="Account No.:" WidthRequest="120" VerticalOptions="Center" />
                    <Entry Text="{Binding Supplier.AccountNumber}"
                           Placeholder="Enter account number"
                           HorizontalOptions="FillAndExpand" />
                </HorizontalStackLayout>
                
                <!-- Tax ID -->
                <HorizontalStackLayout Spacing="10">
                    <Label Text="Tax ID:" WidthRequest="120" VerticalOptions="Center" />
                    <Entry Text="{Binding Supplier.TaxId}"
                           Placeholder="Enter tax ID"
                           HorizontalOptions="FillAndExpand" />
                </HorizontalStackLayout>
                
                <!-- Active Status -->
                <HorizontalStackLayout Spacing="10">
                    <Label Text="Active:" WidthRequest="120" VerticalOptions="Center" />
                    <Switch IsToggled="{Binding Supplier.IsActive}"
                             VerticalOptions="Center" />
                </HorizontalStackLayout>
            </VerticalStackLayout>
        </Frame>
        
        <!-- Action Buttons -->
        <HorizontalStackLayout Spacing="10" Margin="0,20">
            <Button Text="Save"
                    Command="{Binding SaveCommand}"
                    HorizontalOptions="FillAndExpand" />
            <Button Text="Cancel"
                    Command="{Binding CancelCommand}"
                    HorizontalOptions="FillAndExpand" />
            <Button Text="Back"
                    Command="{Binding GoBackCommand}"
                    HorizontalOptions="FillAndExpand" />
            <Button Text="Inventory"
                    Command="{Binding GoToInventoryCommand}"
                    HorizontalOptions="FillAndExpand" />
        </HorizontalStackLayout>
    </VerticalStackLayout>
</ContentPage>