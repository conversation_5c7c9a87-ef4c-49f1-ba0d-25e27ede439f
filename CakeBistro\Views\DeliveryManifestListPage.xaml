<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:viewmodels="clr-namespace:CakeBistro.ViewModels"
             x:Class="CakeBistro.Views.DeliveryManifestListPage">
    <ContentPage.BindingContext>
        <viewmodels:DeliveryManifestListViewModel />
    </ContentPage.BindingContext>
    <ScrollView>
        <VerticalStackLayout Padding="16" Spacing="12">
            <Label Text="Delivery Manifests" FontSize="24" FontAttributes="Bold" />
            <DatePicker Date="{Binding NewManifestDate, Mode=TwoWay}" />
            <Entry Placeholder="Destination" Text="{Binding NewDestination}" />
            <Picker Title="Select Vehicle" ItemsSource="{Binding Vehicles}" ItemDisplayBinding="{Binding RegistrationNumber}" SelectedItem="{Binding SelectedVehicle, Mode=TwoWay}" />
            <Picker Title="Select Driver" ItemsSource="{Binding Drivers}" ItemDisplayBinding="{Binding Name}" SelectedItem="{Binding SelectedDriver, Mode=TwoWay}" />
            <Entry Placeholder="Notes" Text="{Binding NewNotes}" />
            <Label Text="Manifest Items" FontAttributes="Bold" />
            <Entry Placeholder="Product Name" Text="{Binding NewProductName}" />
            <Entry Placeholder="Quantity" Keyboard="Numeric" Text="{Binding NewQuantity}" />
            <Entry Placeholder="Unit Price" Keyboard="Numeric" Text="{Binding NewUnitPrice}" />
            <Button Text="Add Item" Command="{Binding AddItemCommand}" />
            <CollectionView ItemsSource="{Binding Items}" SelectionMode="Single" SelectedItem="{Binding SelectedItem, Mode=TwoWay}">
                <CollectionView.ItemTemplate>
                    <DataTemplate>
                        <StackLayout Orientation="Horizontal" Spacing="10">
                            <Label Text="{Binding ProductName}" />
                            <Label Text="Qty: {Binding Quantity}" />
                            <Label Text="Unit: {Binding UnitPrice, StringFormat='{}{0:C2}'}" />
                            <Label Text="Total: {Binding TotalPrice, StringFormat='{}{0:C2}'}" />
                            <Button Text="Remove" Command="{Binding BindingContext.RemoveItemCommand, Source={x:Reference Name=DeliveryManifestListPage}}" />
                        </StackLayout>
                    </DataTemplate>
                </CollectionView.ItemTemplate>
            </CollectionView>
            <Label Text="Total Quantity: {Binding Items.Count}" FontAttributes="Bold" />
            <Label Text="Total Price: {Binding Items, Converter={StaticResource ItemsTotalPriceConverter}}" FontAttributes="Bold" />
            <Button Text="Add Manifest" Command="{Binding AddManifestCommand}" />
            <CollectionView ItemsSource="{Binding Manifests}" SelectionMode="Single" SelectedItem="{Binding SelectedManifest, Mode=TwoWay}">
                <CollectionView.ItemTemplate>
                    <DataTemplate>
                        <Frame Margin="10" Padding="10" BackgroundColor="White" CornerRadius="8">
                            <StackLayout>
                                <Label Text="{Binding Destination}" FontAttributes="Bold" FontSize="18" />
                                <Label Text="{Binding Date, StringFormat='Date: {0:yyyy-MM-dd}'}" />
                                <Label Text="{Binding VehicleId, StringFormat='Vehicle ID: {0}'}" />
                                <Label Text="{Binding DriverId, StringFormat='Driver ID: {0}'}" />
                                <Label Text="{Binding Notes}" />
                                <Button Text="Remove" Command="{Binding BindingContext.RemoveManifestCommand, Source={x:Reference Name=DeliveryManifestListPage}}" />
                            </StackLayout>
                        </Frame>
                    </DataTemplate>
                </CollectionView.ItemTemplate>
            </CollectionView>
        </VerticalStackLayout>
    </ScrollView>
</ContentPage>
