
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace CakeBistro.Models
{
    /// <summary>
    /// Represents a record of damaged goods in the production process
    /// Implements PRD 4.2 requirement for effective damage tracking and management
    /// </summary>
    [Table("DamageRecords")]
    public class DamageRecord
    {
        /// <summary>
        /// Gets or sets the unique identifier of the damage record
        /// </summary>
        [Key]
        public int Id { get; set; }

        /// <summary>
        /// Gets or sets the ID of the batch where damage occurred
        /// </summary>
        public int BatchId { get; set; }

        /// <summary>
        /// Gets or sets the quantity of damaged items
        /// </summary>
        public int Quantity { get; set; }

        /// <summary>
        /// Gets or sets the reason for the damage
        /// </summary>
        public string Reason { get; set; }

        /// <summary>
        /// Gets or sets the date when damage was reported
        /// </summary>
        public DateTime ReportDate { get; set; }

        /// <summary>
        /// Gets or sets the date when the record was created
        /// </summary>
        public DateTime CreatedDate { get; set; }

        // Navigation properties
        /// <summary>
        /// Gets or sets the batch associated with this damage record
        /// </summary>
        [ForeignKey("BatchId")]
        public virtual InventoryBatch Batch { get; set; }
    }
}