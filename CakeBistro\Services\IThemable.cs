
using Microsoft.Maui.Controls;
using CakeBistro.Models;

namespace CakeBistro.Services
{
    /// <summary>
    /// Interface to be implemented by pages that can apply themes
    /// </summary>
    public interface IThemable
    {
        /// <summary>
        /// Applies the specified theme to the page
        /// </summary>
        /// <param name="theme">The theme configuration to apply</param>
        void ApplyTheme(ThemeConfiguration theme);
    }
}