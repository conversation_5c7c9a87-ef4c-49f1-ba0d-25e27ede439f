<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="CakeBistro.Views.ReconciliationPage"
             xmlns:viewModels="clr-namespace:CakeBistro.ViewModels"
             Title="Reconciliation">
    <ContentPage.BindingContext>
        <viewModels:ReconciliationViewModel />
    </ContentPage.BindingContext>
    <ScrollView>
        <VerticalStackLayout Spacing="16" Padding="16">
            <Label Text="Bank Reconciliation" FontSize="24" HorizontalOptions="Start"/>
            <Grid ColumnDefinitions="*,*" RowDefinitions="Auto,Auto,Auto,Auto,Auto">
                <Label Grid.Row="0" Grid.Column="0" Text="Statement Date:"/>
                <DatePicker Grid.Row="0" Grid.Column="1" Date="{Binding ReconciliationDate, Mode=TwoWay, StringFormat={0:yyyy-MM-dd}}"/>
                <Label Grid.Row="1" Grid.Column="0" Text="Statement Balance:"/>
                <Entry Grid.Row="1" Grid.Column="1" Text="{Binding StatementBalance, Mode=TwoWay, StringFormat={}{0:C2}}"/>
                <Label Grid.Row="2" Grid.Column="0" Text="Variance Tolerance:"/>
                <Entry Grid.Row="2" Grid.Column="1" Text="{Binding VarianceTolerance, Mode=TwoWay, StringFormat={}{0:C2}}"/>
                <Label Grid.Row="3" Grid.Column="0" Text="Reconciliation Notes:"/>
                <Editor Grid.Row="3" Grid.Column="1" Text="{Binding ReconciliationNotes, Mode=TwoWay}"/>
                <Label Grid.Row="4" Grid.Column="0" Text="Reconciliation Result:"/>
                <Label Grid.Row="4" Grid.Column="1" Text="{Binding ReconciliationResult}"/>
            </Grid>
            <Button Text="Perform Reconciliation" Command="{Binding PerformReconciliationCommand}" IsEnabled="{Binding SelectedAccountId, Converter={StaticResource GreaterThanZeroConverter}}"/>
        </VerticalStackLayout>
    </ScrollView>
</ContentPage>
