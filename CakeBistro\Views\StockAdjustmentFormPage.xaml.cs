using System;
using System.Threading.Tasks;
using CakeBistro.ViewModels;
using CakeBistro.Core.Interfaces;
using Microsoft.Extensions.DependencyInjection;

namespace CakeBistro.Views
{
    [QueryProperty(nameof(StockAdjustmentId), "id")]
    public partial class StockAdjustmentFormPage : ContentPage
    {
        public int? StockAdjustmentId { get; set; }
        private StockAdjustmentFormViewModel _viewModel;
        public StockAdjustmentFormPage()
        {
            InitializeComponent();
            var inventoryService = MauiProgram.ServiceProvider.GetService<IInventoryService>();
            _viewModel = new StockAdjustmentFormViewModel(inventoryService);
            BindingContext = _viewModel;
        }

        protected override async void OnAppearing()
        {
            base.OnAppearing();
            if (StockAdjustmentId.HasValue)
            {
                await _viewModel.LoadAsync(StockAdjustmentId.Value);
            }
        }
    }
}
