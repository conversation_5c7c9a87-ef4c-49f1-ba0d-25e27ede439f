using System;
using System.Collections.ObjectModel;
using System.Threading.Tasks;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using CakeBistro.Services;
using CakeBistro.Models;
using Microsoft.UI.Xaml.Media;

namespace CakeBistro.ViewModels;

public partial class FinancialReportsViewModel : ObservableObject
{
    private readonly FinanceService? _financeService;

    [ObservableProperty]
    private DateTime reportDate = DateTime.Today;

    [ObservableProperty]
    private FinancialReport? currentReport;

    [ObservableProperty]
    private string? statusMessage;

    [ObservableProperty]
    private bool isBusy;

    private Color _statusColor = Colors.Transparent;
    public Color StatusColor
    {
        get => _statusColor;
        set => SetProperty(ref _statusColor, value);
    }

    public ObservableCollection<FinancialReport> Reports { get; } = new();

    public IRelayCommand GenerateBalanceSheetCommand { get; }
    public IRelayCommand GenerateIncomeStatementCommand { get; }
    public IRelayCommand GenerateCashFlowCommand { get; }
    public IRelayCommand ExportPdfCommand { get; }
    public IRelayCommand ExportExcelCommand { get; }

    public FinancialReportsViewModel(FinanceService financeService)
    {
        _financeService = financeService;
        GenerateBalanceSheetCommand = new AsyncRelayCommand(GenerateBalanceSheetAsync);
        GenerateIncomeStatementCommand = new AsyncRelayCommand(GenerateIncomeStatementAsync);
        GenerateCashFlowCommand = new AsyncRelayCommand(GenerateCashFlowAsync);
        ExportPdfCommand = new AsyncRelayCommand(ExportPdfAsync, CanExport);
        ExportExcelCommand = new AsyncRelayCommand(ExportExcelAsync, CanExport);
    }

    public FinancialReportsViewModel() : this(null!) { }

    private bool CanExport() => CurrentReport != null && _financeService != null;

    private async Task ExportPdfAsync()
    {
        StatusMessage = string.Empty;
        IsBusy = true;
        try
        {
            if (_financeService == null || CurrentReport == null) return;
            var fileName = $"{CurrentReport.ReportType}_{ReportDate:yyyyMMdd_HHmmss}.pdf";
            var documents = Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments);
            var filePath = Path.Combine(documents, fileName);
            string resultPath = string.Empty;
            switch (CurrentReport.ReportType)
            {
                case ReportType.BalanceSheet:
                    resultPath = await _financeService.ExportBalanceSheetAsync(ReportDate, filePath);
                    break;
                case ReportType.IncomeStatement:
                    resultPath = await _financeService.ExportIncomeStatementAsync(ReportDate.AddMonths(-1), ReportDate, filePath);
                    break;
                case ReportType.CashFlow:
                    resultPath = await _financeService.ExportCashFlowStatementAsync(ReportDate.AddMonths(-1), ReportDate, filePath);
                    break;
                default:
                    StatusMessage = "Export not supported for this report type.";
                    StatusColor = Color.FromArgb("#1976D2"); // Info color
                    return;
            }
            StatusMessage = $"PDF exported: {resultPath}";
            StatusColor = Color.FromArgb("#388E3C"); // Success color
        }
        catch (Exception ex)
        {
            StatusMessage = $"Export failed: {ex.Message}";
            StatusColor = Color.FromArgb("#D32F2F"); // Error color
        }
        finally
        {
            IsBusy = false;
        }
    }

    private async Task ExportExcelAsync()
    {
        StatusMessage = string.Empty;
        IsBusy = true;
        try
        {
            if (_financeService == null || CurrentReport == null) return;
            var fileName = $"{CurrentReport.ReportType}_{ReportDate:yyyyMMdd_HHmmss}.xlsx";
            var documents = Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments);
            var filePath = Path.Combine(documents, fileName);
            // For now, simulate Excel export (implement actual export logic as needed)
            StatusMessage = $"Excel export is not yet implemented. Would save to: {filePath}";
            StatusColor = Color.FromArgb("#1976D2"); // Info color
        }
        catch (Exception ex)
        {
            StatusMessage = $"Export failed: {ex.Message}";
            StatusColor = Color.FromArgb("#D32F2F"); // Error color
        }
        finally
        {
            IsBusy = false;
        }
    }

    private async Task GenerateBalanceSheetAsync()
    {
        StatusMessage = string.Empty;
        IsBusy = true;
        try
        {
            if (_financeService == null) return;
            var report = await _financeService.GenerateBalanceSheetAsync(ReportDate);
            report.ReportType = ReportType.BalanceSheet;
            CurrentReport = report;
            Reports.Add(report);
            StatusMessage = "Balance Sheet generated.";
            StatusColor = Color.FromArgb("#388E3C"); // Success color
        }
        catch (Exception ex)
        {
            StatusMessage = $"Error: {ex.Message}";
            StatusColor = Color.FromArgb("#D32F2F"); // Error color
        }
        finally
        {
            IsBusy = false;
        }
    }

    private async Task GenerateIncomeStatementAsync()
    {
        StatusMessage = string.Empty;
        IsBusy = true;
        try
        {
            if (_financeService == null) return;
            var report = await _financeService.GenerateIncomeStatementAsync(ReportDate.AddMonths(-1), ReportDate);
            report.ReportType = ReportType.IncomeStatement;
            CurrentReport = report;
            Reports.Add(report);
            StatusMessage = "Income Statement generated.";
            StatusColor = Color.FromArgb("#388E3C"); // Success color
        }
        catch (Exception ex)
        {
            StatusMessage = $"Error: {ex.Message}";
            StatusColor = Color.FromArgb("#D32F2F"); // Error color
        }
        finally
        {
            IsBusy = false;
        }
    }

    private async Task GenerateCashFlowAsync()
    {
        StatusMessage = string.Empty;
        IsBusy = true;
        try
        {
            if (_financeService == null) return;
            var report = await _financeService.GenerateCashFlowStatementAsync(ReportDate.AddMonths(-1), ReportDate);
            report.ReportType = ReportType.CashFlow;
            CurrentReport = report;
            Reports.Add(report);
            StatusMessage = "Cash Flow Statement generated.";
            StatusColor = Color.FromArgb("#388E3C"); // Success color
        }
        catch (Exception ex)
        {
            StatusMessage = $"Error: {ex.Message}";
            StatusColor = Color.FromArgb("#D32F2F"); // Error color
        }
        finally
        {
            IsBusy = false;
        }
    }
}
