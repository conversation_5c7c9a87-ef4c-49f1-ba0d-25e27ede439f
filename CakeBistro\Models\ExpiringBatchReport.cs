using System;
using System.Collections.Generic;

namespace CakeBistro.Models
{
    public class ExpiringBatchReport
    {
        public DateTime ReportDate { get; set; }
        public List<ExpiringBatchDetail> BatchesExpiringWithin30Days { get; set; } = new();
        public List<ExpiringBatchDetail> BatchesExpiringWithin7Days { get; set; } = new();
        public List<ExpiringBatchDetail> ExpiredBatches { get; set; } = new();
        public string? Notes { get; set; }
        public int TotalExpiringBatches { get; set; }
        public List<string> Recommendations { get; set; } = new();
    }

    public class ExpiringBatchDetail
    {
        public int RawMaterialId { get; set; }
        public string? RawMaterialName { get; set; }
        public string? BatchNumber { get; set; }
        public decimal Quantity { get; set; }
        public DateTime ExpiryDate { get; set; }
        public string? StorageLocation { get; set; }
        public bool IsExpired { get; set; }
        public bool IsUrgent { get; set; }
    }
}
