// Create IReportExportService.cs
using CakeBistro.Core.Models;
using System;
using System.Threading.Tasks;

namespace CakeBistro.Services;

/// <summary>
/// Interface for report export operations
/// </summary>
public interface IReportExportService
{
    /// <summary>
    /// Exports a report to the specified format
    /// </summary>
    Task<byte[]> ExportAsync<T>(T report, string format) where T : class;
    
    /// <summary>
    /// Exports a report to PDF format
    /// </summary>
    Task<byte[]> ExportToPdfAsync<T>(T report) where T : class;
    
    /// <summary>
    /// Exports a report to CSV format
    /// </summary>
    Task<byte[]> ExportToCsvAsync<T>(T report) where T : class;
    
    /// <summary>
    /// Exports a report to Excel format
    /// </summary>
    Task<byte[]> ExportToExcelAsync<T>(T report) where T : class;
}
