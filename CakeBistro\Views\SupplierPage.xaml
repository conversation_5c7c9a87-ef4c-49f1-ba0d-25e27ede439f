<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="CakeBistro.Views.SupplierPage"
             Title="Suppliers">
    <VerticalStackLayout Margin="20">
        <!-- Search Bar -->
        <HorizontalStackLayout Spacing="10" VerticalOptions="Center">
            <Image Source="search.png" WidthRequest="24" HeightRequest="24" VerticalOptions="Center" />
            <Entry 
                Placeholder="Search suppliers..."
                Text="{Binding SearchTerm}"
                HorizontalOptions="FillAndExpand"
                VerticalOptions="Center"
                FontSize="16" />
        </HorizontalStackLayout>
        
        <!-- Category Filter -->
        <Picker ItemsSource="{Binding Categories}"
                SelectedItem="{Binding SelectedCategory}"
                Margin="0,10,0,0">
        </Picker>
        
        <!-- Suppliers List -->
        <Label Text="Suppliers" FontAttributes="Bold" Margin="0,20,0,10" />
        <CollectionView ItemsSource="{Binding Suppliers}"
                        SelectionMode="Single"
                        SelectedItem="{Binding SelectedItem, Source={RelativeSource AncestorType={x:Type viewModels:SupplierViewModel}}}">
            <CollectionView.ItemsLayout>
                <LinearItemsLayout Orientation="Vertical" />
            </CollectionView.ItemsLayout>
            <CollectionView.ItemTemplate>
                <DataTemplate>
                    <Frame Margin="0,5">
                        <StackLayout Orientation="Horizontal">
                            <StackLayout>
                                <Label Text="{Binding Name}" FontAttributes="Bold" />
                                <Label Text="{Binding ContactName}" />
                                <Label Text="{Binding Category}" />
                            </StackLayout>
                            <StackLayout HorizontalOptions="EndAndExpand">
                                <Label Text="{Binding Phone}" />
                                <Label Text="{Binding Email}" />
                            </StackLayout>
                        </StackLayout>
                    </Frame>
                </DataTemplate>
            </CollectionView.ItemTemplate>
        </CollectionView>
        
        <!-- Loading Indicator -->
        <ActivityIndicator IsRunning="{Binding IsSearching}"
                           IsVisible="{Binding IsSearching}"
                           HorizontalOptions="Center"
                           VerticalOptions="Center" />
        
        <!-- Action Buttons -->
        <HorizontalStackLayout Spacing="10" Margin="0,20">
            <Button Text="Add Supplier"
                    Command="{Binding AddSupplierCommand}"
                    HorizontalOptions="FillAndExpand" />
            <Button Text="Refresh"
                    Command="{Binding RefreshCommand}"
                    HorizontalOptions="FillAndExpand" />
            <Button Text="Manage Inventory"
                    Command="{Binding GoToInventoryCommand}"
                    HorizontalOptions="FillAndExpand" />
        </HorizontalStackLayout>
    </VerticalStackLayout>
</ContentPage>