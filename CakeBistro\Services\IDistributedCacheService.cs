using CakeBistro.Services;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;

namespace CakeBistro.Services
{
    public interface IDistributedCacheService : ICacheService
    {
        // ... existing methods ...
        
        /// <summary>
        /// Invalidates all cache entries with the specified tag
        /// </summary>
        /// <param name="tag">The tag to invalidate</param>
        void InvalidateByTag(string tag);
    }
    
    // ... rest of the file ...
}