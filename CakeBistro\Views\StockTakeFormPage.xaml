<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:local="clr-namespace:CakeBistro.Views"
             x:Class="CakeBistro.Views.StockTakeFormPage"
             Title="Stock Take Form">
    <local:FormTemplate>
        <StackLayout Padding="0" Spacing="16">
            <Label Text="Stock Take Entry" FontAttributes="Bold" FontSize="Large"/>
            <Picker Title="Raw Material"
                    ItemsSource="{Binding RawMaterials}"
                    ItemDisplayBinding="{Binding Name}"
                    SelectedItem="{Binding SelectedRawMaterial}"
                    x:Name="RawMaterialPicker"
                    BackgroundColor="{Binding RawMaterialError, Converter={StaticResource NullToErrorColorConverter}}"/>
            <Label Text="{Binding RawMaterialError}" TextColor="Red" IsVisible="{Binding RawMaterialError, Converter={StaticResource NullToFalseConverter}}" FontSize="Small"/>
            <Entry Placeholder="Batch Name"
                   Text="{Binding BatchName}"
                   x:Name="BatchNameEntry"
                   BackgroundColor="{Binding BatchNameError, Converter={StaticResource NullToErrorColorConverter}}"/>
            <Label Text="{Binding BatchNameError}" TextColor="Red" IsVisible="{Binding BatchNameError, Converter={StaticResource NullToFalseConverter}}" FontSize="Small"/>
            <Entry Placeholder="Quantity" Keyboard="Numeric"
                   Text="{Binding Quantity}"
                   x:Name="QuantityEntry"
                   BackgroundColor="{Binding QuantityError, Converter={StaticResource NullToErrorColorConverter}}"/>
            <Label Text="{Binding QuantityError}" TextColor="Red" IsVisible="{Binding QuantityError, Converter={StaticResource NullToFalseConverter}}" FontSize="Small"/>
            <DatePicker Date="{Binding Date}"/>
            <Label Text="{Binding DateError}" TextColor="Red" IsVisible="{Binding DateError, Converter={StaticResource NullToFalseConverter}}" FontSize="Small"/>
            <Entry Placeholder="Notes" Text="{Binding Notes}"/>
        </StackLayout>
    </local:FormTemplate>
</ContentPage>
