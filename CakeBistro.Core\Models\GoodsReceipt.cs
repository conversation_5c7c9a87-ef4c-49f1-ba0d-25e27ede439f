using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace CakeBistro.Core.Models
{
    public class GoodsReceipt : BaseEntity
    {
        [Required]
        public string ReceiptNumber { get; set; }
        [Required]
        public DateTime ReceiptDate { get; set; }
        [Required]
        public Guid PurchaseOrderId { get; set; }
        public Guid WarehouseId { get; set; }
        public GoodsReceiptStatus Status { get; set; }
        [ForeignKey("PurchaseOrderId")]
        public PurchaseOrder PurchaseOrder { get; set; }
        public ICollection<GoodsReceiptItem> Items { get; set; } = new List<GoodsReceiptItem>();
    }
    public enum GoodsReceiptStatus
    {
        Draft,
        Completed,
        Cancelled
    }
}
