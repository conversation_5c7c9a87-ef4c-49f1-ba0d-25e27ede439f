{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\Desktop\\CakeBistro\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\Desktop\\CakeBistro\\CakeBistro\\App.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:CakeBistro\\App.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\Desktop\\CakeBistro\\CakeBistro\\AppShell.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:CakeBistro\\AppShell.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\Desktop\\CakeBistro\\CakeBistro\\Services\\ThemeEventArgs.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:CakeBistro\\Services\\ThemeEventArgs.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\Desktop\\CakeBistro\\CakeBistro\\Views\\ProductPage.xaml||{8B382828-6202-11D1-8870-0000F87579D2}|", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:CakeBistro\\Views\\ProductPage.xaml||{8B382828-6202-11D1-8870-0000F87579D2}|"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\Desktop\\CakeBistro\\CakeBistro\\Views\\ProductPage.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:CakeBistro\\Views\\ProductPage.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\Desktop\\CakeBistro\\CakeBistro\\MauiProgram.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:CakeBistro\\MauiProgram.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 4, "Children": [{"$type": "Document", "DocumentIndex": 4, "Title": "ProductPage.xaml.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\CakeBistro\\CakeBistro\\Views\\ProductPage.xaml.cs", "RelativeDocumentMoniker": "CakeBistro\\Views\\ProductPage.xaml.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\CakeBistro\\CakeBistro\\Views\\ProductPage.xaml.cs", "RelativeToolTip": "CakeBistro\\Views\\ProductPage.xaml.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAsAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-13T17:12:22.188Z"}, {"$type": "Document", "DocumentIndex": 2, "Title": "ThemeEventArgs.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\CakeBistro\\CakeBistro\\Services\\ThemeEventArgs.cs", "RelativeDocumentMoniker": "CakeBistro\\Services\\ThemeEventArgs.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\CakeBistro\\CakeBistro\\Services\\ThemeEventArgs.cs", "RelativeToolTip": "CakeBistro\\Services\\ThemeEventArgs.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA0AAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-13T17:12:22.048Z"}, {"$type": "Document", "DocumentIndex": 3, "Title": "ProductPage.xaml", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\CakeBistro\\CakeBistro\\Views\\ProductPage.xaml", "RelativeDocumentMoniker": "CakeBistro\\Views\\ProductPage.xaml", "ToolTip": "C:\\Users\\<USER>\\Desktop\\CakeBistro\\CakeBistro\\Views\\ProductPage.xaml", "RelativeToolTip": "CakeBistro\\Views\\ProductPage.xaml", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAcAAAAOAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-07-13T17:12:21.923Z"}, {"$type": "Document", "DocumentIndex": 1, "Title": "AppShell.xaml.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\CakeBistro\\CakeBistro\\AppShell.xaml.cs", "RelativeDocumentMoniker": "CakeBistro\\AppShell.xaml.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\CakeBistro\\CakeBistro\\AppShell.xaml.cs", "RelativeToolTip": "CakeBistro\\AppShell.xaml.cs", "ViewState": "AgIAAFIAAAAAAAAAAAAvwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-13T17:11:23.992Z"}, {"$type": "Document", "DocumentIndex": 0, "Title": "App.xaml.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\CakeBistro\\CakeBistro\\App.xaml.cs", "RelativeDocumentMoniker": "CakeBistro\\App.xaml.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\CakeBistro\\CakeBistro\\App.xaml.cs", "RelativeToolTip": "CakeBistro\\App.xaml.cs", "ViewState": "AgIAAAwAAAAAAAAAAAAvwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-13T17:10:13.957Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "MauiProgram.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\CakeBistro\\CakeBistro\\MauiProgram.cs", "RelativeDocumentMoniker": "CakeBistro\\MauiProgram.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\CakeBistro\\CakeBistro\\MauiProgram.cs", "RelativeToolTip": "CakeBistro\\MauiProgram.cs", "ViewState": "AgIAACcAAAAAAAAAAEBVwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-13T17:24:06.467Z"}]}]}]}