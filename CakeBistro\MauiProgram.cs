using Microsoft.Extensions.Logging;
using CakeBistro.Services;
using CakeBistro.ViewModels;
using CakeBistro.Views;
using Microsoft.EntityFrameworkCore;
using CommunityToolkit.Maui;
using CakeBistro.Data;
using Microsoft.Extensions.Configuration;

namespace CakeBistro;

public static class MauiProgram
{
    public static MauiApp CreateMauiApp()
    {
        var builder = MauiApp.CreateBuilder();
        builder
            .UseMauiApp<App>()
            .UseMauiCommunityToolkit()
            .ConfigureFonts(fonts =>
            {
                fonts.AddFont("OpenSans-Regular.ttf", "OpenSansRegular");
            });

#if DEBUG
        builder.Logging.AddDebug();
#endif

        // Load configuration and register all application services
        var config = new ConfigurationBuilder()
            .AddJsonFile("appsettings.json", optional: true, reloadOnChange: true)
            .Build();

        // Register all application services (DI, cache, metrics, etc.)
        builder.Services.AddApplicationServices(config);

        // Register Views and ViewModels not handled by AddApplicationServices
        builder.Services.AddTransient<MainPage>();
        builder.Services.AddTransient<ProductPage>();
        builder.Services.AddTransient<InventoryPage>();
        // ...add any other views/viewmodels as needed...

        var app = builder.Build();
        ServiceProvider = app.Services;
        return app;
    }

    public static IServiceProvider ServiceProvider { get; private set; }
}
