using CakeBistro.Services;
using CakeBistro.ViewModels;
using CakeBistro.Core.Interfaces;

namespace CakeBistro.Views
{
    public partial class SalesOrderFormPage : ContentPage
    {
        public SalesOrderFormPage()
        {
            InitializeComponent();
            var salesService = MauiProgram.ServiceProvider.GetService<ISalesService>();
        }

        private void InitializeComponent() { }
    }
    }
