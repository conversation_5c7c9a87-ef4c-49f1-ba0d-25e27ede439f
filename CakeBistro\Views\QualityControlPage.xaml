<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:vm="clr-namespace:CakeBistro.ViewModels"
             xmlns:model="clr-namespace:CakeBistro.Models"
             x:Class="CakeBistro.Views.QualityControlPage"
             x:DataType="vm:QualityControlViewModel"
             Title="{Binding Title}">

    <Grid RowDefinitions="Auto,Auto,*">
        <!-- Header with Filters and Add Button -->
        <Grid Grid.Row="0" 
              ColumnDefinitions="*,Auto,Auto"
              Padding="10">
            
            <VerticalStackLayout Grid.Column="0" Spacing="10">
                <Label Text="Date Range"/>
                <HorizontalStackLayout Spacing="10">
                    <DatePicker Date="{Binding StartDate}"
                              MinimumDate="{Binding Source={x:Static system:DateTime.MinValue}}"
                              MaximumDate="{Binding EndDate}"/>
                    <Label Text="to"/>
                    <DatePicker Date="{Binding EndDate}"
                              MinimumDate="{Binding StartDate}"
                              MaximumDate="{Binding Source={x:Static system:DateTime.Today}}"/>
                    <Button Text="Apply"
                            Command="{Binding DateRangeChangedCommand}"/>
                </HorizontalStackLayout>

                <HorizontalStackLayout Spacing="10">
                    <Button Text="All" 
                            Command="{Binding FilterChangedCommand}"
                            CommandParameter="All"
                            BackgroundColor="{Binding SelectedFilter, Converter={StaticResource EqualityConverter}, ConverterParameter='All'}"/>
                    <Button Text="Failed"
                            Command="{Binding FilterChangedCommand}"
                            CommandParameter="Failed"
                            BackgroundColor="{Binding SelectedFilter, Converter={StaticResource EqualityConverter}, ConverterParameter='Failed'}"/>
                    <Button Text="Pending Review"
                            Command="{Binding FilterChangedCommand}"
                            CommandParameter="Pending"
                            BackgroundColor="{Binding SelectedFilter, Converter={StaticResource EqualityConverter}, ConverterParameter='Pending'}"/>
                </HorizontalStackLayout>
            </VerticalStackLayout>

            <Button Grid.Column="1"
                    Text="Add Check"
                    Command="{Binding AddCheckCommand}"
                    VerticalOptions="Start"/>

            <Button Grid.Column="2"
                    Text="Generate Report"
                    Command="{Binding GenerateReportCommand}"
                    BackgroundColor="#6A1B9A"
                    TextColor="White"/>
        </Grid>

        <!-- Metrics Section -->
        <Grid Grid.Row="1" 
              ColumnDefinitions="*,*,*,*"
              Padding="10"
              BackgroundColor="{StaticResource Primary}">
            
            <Frame Grid.Column="0" Margin="5">
                <VerticalStackLayout>
                    <Label Text="Total Checks"
                           HorizontalOptions="Center"/>
                    <Label Text="{Binding Metrics[TotalChecks]}"
                           FontSize="24"
                           HorizontalOptions="Center"/>
                </VerticalStackLayout>
            </Frame>

            <Frame Grid.Column="1" Margin="5">
                <VerticalStackLayout>
                    <Label Text="Passed"
                           TextColor="Green"
                           HorizontalOptions="Center"/>
                    <Label Text="{Binding Metrics[PassedChecks]}"
                           FontSize="24"
                           HorizontalOptions="Center"/>
                </VerticalStackLayout>
            </Frame>

            <Frame Grid.Column="2" Margin="5">
                <VerticalStackLayout>
                    <Label Text="Failed"
                           TextColor="Red"
                           HorizontalOptions="Center"/>
                    <Label Text="{Binding Metrics[FailedChecks]}"
                           FontSize="24"
                           HorizontalOptions="Center"/>
                </VerticalStackLayout>
            </Frame>

            <Frame Grid.Column="3" Margin="5">
                <VerticalStackLayout>
                    <Label Text="Pending"
                           TextColor="Orange"
                           HorizontalOptions="Center"/>
                    <Label Text="{Binding Metrics[PendingChecks]}"
                           FontSize="24"
                           HorizontalOptions="Center"/>
                </VerticalStackLayout>
            </Frame>
        </Grid>

        <!-- Quality Checks List -->
        <RefreshView Grid.Row="2"
                     Command="{Binding LoadChecksCommand}"
                     IsRefreshing="{Binding IsBusy}">
            <CollectionView ItemsSource="{Binding QualityChecks}"
                          SelectedItem="{Binding SelectedCheck}"
                          SelectionMode="Single">
                <CollectionView.ItemTemplate>
                    <DataTemplate x:DataType="model:QualityCheck">
                        <SwipeView>
                            <SwipeView.RightItems>
                                <SwipeItems>
                                    <SwipeItem Text="Delete"
                                             BackgroundColor="Red"
                                             Command="{Binding Source={RelativeSource AncestorType={x:Type vm:QualityCheckViewModel}}, Path=DeleteCheckCommand}"
                                             CommandParameter="{Binding .}"/>
                                </SwipeItems>
                            </SwipeView.RightItems>

                            <Grid Padding="10">
                                <Frame>
                                    <Grid RowDefinitions="Auto,Auto,Auto"
                                          ColumnDefinitions="*,Auto">
                                        
                                        <!-- Check Type and Status -->
                                        <Label Grid.Row="0"
                                               Grid.Column="0"
                                               Text="{Binding Type}"
                                               FontSize="16"
                                               FontAttributes="Bold"/>

                                        <Label Grid.Row="0"
                                               Grid.Column="1"
                                               Text="{Binding Status}"
                                               FontSize="14"
                                               TextColor="{Binding Status, Converter={StaticResource StatusColorConverter}}"/>

                                        <!-- Production Batch Info -->
                                        <Label Grid.Row="1"
                                               Grid.Column="0"
                                               Grid.ColumnSpan="2"
                                               Text="{Binding ProductionBatch.Recipe.Name, StringFormat='Batch: {0}'}"
                                               FontSize="14"/>

                                        <!-- Check Details -->
                                        <VerticalStackLayout Grid.Row="2"
                                                           Grid.Column="0"
                                                           Grid.ColumnSpan="2">
                                            <Label Text="{Binding CheckDate, StringFormat='Check Date: {0:g}'}"
                                                   FontSize="12"/>
                                            
                                            <Label Text="{Binding MeasuredValue, StringFormat='Measured: {0}'}"
                                                   IsVisible="{Binding MeasuredValue, Converter={StaticResource NotNullConverter}}"/>
                                            
                                            <Label Text="{Binding ActualResult}"
                                                   IsVisible="{Binding ActualResult, Converter={StaticResource NotNullConverter}}"/>
                                            
                                            <Label Text="{Binding CheckedBy, StringFormat='By: {0}'}"
                                                   FontSize="12"/>

                                            <Button Text="View Details"
                                                    Command="{Binding Source={RelativeSource AncestorType={x:Type vm:QualityCheckViewModel}}, Path=EditCheckCommand}"
                                                    CommandParameter="{Binding .}"
                                                    HorizontalOptions="Start"/>
                                        </VerticalStackLayout>
                                    </Grid>
                                </Frame>
                            </Grid>
                        </SwipeView>
                    </DataTemplate>
                </CollectionView.ItemTemplate>

                <CollectionView.EmptyView>
                    <ContentView>
                        <VerticalStackLayout VerticalOptions="Center"
                                           HorizontalOptions="Center">
                            <Label Text="No quality checks found"
                                   FontSize="18"
                                   TextColor="Gray"/>
                            <Button Text="Add Check"
                                    Command="{Binding AddCheckCommand}"
                                    Margin="0,20,0,0"/>
                        </VerticalStackLayout>
                    </ContentView>
                </CollectionView.EmptyView>
            </CollectionView>
        </RefreshView>
    </Grid>
</ContentPage>
