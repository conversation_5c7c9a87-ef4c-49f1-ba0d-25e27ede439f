<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:vm="clr-namespace:CakeBistro.ViewModels"
             xmlns:model="clr-namespace:CakeBistro.Models"
             x:Class="CakeBistro.Views.QualityCheckDetailPage"
             x:DataType="vm:QualityCheckDetailViewModel"
             Title="{Binding Title}">

    <ScrollView>
        <VerticalStackLayout Spacing="10" Padding="20">
            <!-- Production Batch Selection -->
            <Label Text="Production Batch" />
            <Picker ItemsSource="{Binding Batches}"
                    SelectedItem="{Binding SelectedBatch}"
                    ItemDisplayBinding="{Binding Recipe.Name}"
                    Title="Select Production Batch"/>

            <!-- Check Type -->
            <Label Text="Check Type" />
            <Picker ItemsSource="{Binding CheckTypes}"
                    SelectedItem="{Binding QualityCheck.Type}"
                    Title="Select Check Type"/>

            <!-- Check Value Type Selection -->
            <HorizontalStackLayout Spacing="10">
                <RadioButton Content="Numeric Value"
                            IsChecked="{Binding HasNumericValue}"
                            GroupName="ValueType"
                            Command="{Binding ToggleValueTypeCommand}"
                            CommandParameter="Numeric"/>
                <RadioButton Content="Pass/Fail"
                            IsChecked="{Binding HasPassFailValue}"
                            GroupName="ValueType"
                            Command="{Binding ToggleValueTypeCommand}"
                            CommandParameter="PassFail"/>
            </HorizontalStackLayout>

            <!-- Numeric Value Section -->
            <VerticalStackLayout IsVisible="{Binding HasNumericValue}"
                               Spacing="10">
                <Label Text="Measured Value" />
                <Entry Text="{Binding QualityCheck.MeasuredValue}"
                       Keyboard="Numeric"
                       Placeholder="Enter measured value"/>

                <Label Text="Minimum Value" />
                <Entry Text="{Binding QualityCheck.MinValue}"
                       Keyboard="Numeric"
                       Placeholder="Enter minimum value"/>

                <Label Text="Maximum Value" />
                <Entry Text="{Binding QualityCheck.MaxValue}"
                       Keyboard="Numeric"
                       Placeholder="Enter maximum value"/>
            </VerticalStackLayout>

            <!-- Pass/Fail Section -->
            <VerticalStackLayout IsVisible="{Binding HasPassFailValue}"
                               Spacing="10">
                <Label Text="Expected Result" />
                <Entry Text="{Binding QualityCheck.ExpectedResult}"
                       Placeholder="Enter expected result"/>

                <Label Text="Actual Result" />
                <Entry Text="{Binding QualityCheck.ActualResult}"
                       Placeholder="Enter actual result"/>
            </VerticalStackLayout>

            <!-- Status -->
            <Label Text="Status" />
            <Picker ItemsSource="{Binding StatusOptions}"
                    SelectedItem="{Binding QualityCheck.Status}"
                    Title="Select Status"/>

            <!-- Notes -->
            <Label Text="Notes" />
            <Editor Text="{Binding QualityCheck.Notes}"
                    AutoSize="TextChanges"
                    Placeholder="Enter any notes about the quality check"
                    MaxLength="500"/>

            <!-- Defect Description (if Failed) -->
            <VerticalStackLayout IsVisible="{Binding QualityCheck.Status, Converter={StaticResource EqualityConverter}, ConverterParameter='Failed'}">
                <Label Text="Defect Description" />
                <Editor Text="{Binding QualityCheck.DefectDescription}"
                        AutoSize="TextChanges"
                        Placeholder="Describe the defect"
                        MaxLength="500"/>

                <Label Text="Corrective Action" />
                <Editor Text="{Binding QualityCheck.CorrectiveAction}"
                        AutoSize="TextChanges"
                        Placeholder="Describe corrective actions taken or needed"
                        MaxLength="500"/>
            </VerticalStackLayout>

            <!-- Checked By -->
            <Label Text="Checked By" />
            <Entry Text="{Binding QualityCheck.CheckedBy}"
                   Placeholder="Enter your name"/>

            <!-- Action Buttons -->
            <HorizontalStackLayout Spacing="10" 
                                 Margin="0,20,0,0"
                                 HorizontalOptions="Center">
                <Button Text="Save"
                        Command="{Binding SaveCheckCommand}"
                        IsEnabled="{Binding !IsSaving}"/>
                <Button Text="Cancel"
                        Command="{Binding CancelCommand}"
                        IsEnabled="{Binding !IsSaving}"/>
            </HorizontalStackLayout>
        </VerticalStackLayout>
    </ScrollView>
</ContentPage>
