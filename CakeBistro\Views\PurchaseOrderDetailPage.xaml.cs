using Microsoft.Maui.Controls;
using CakeBistro.ViewModels;

namespace CakeBistro.Views
{
    public partial class PurchaseOrderDetailPage : ContentPage
    {
        private readonly IInventoryService _inventoryService;
        private readonly PurchaseOrderDetailViewModel _viewModel;
        private Guid _orderId;
        
        public PurchaseOrderDetailPage(IInventoryService inventoryService)
            : base()
        {
            InitializeComponent();
            _inventoryService = inventoryService;
            _viewModel = new PurchaseOrderDetailViewModel(inventoryService);
            BindingContext = _viewModel;
            
            // Subscribe to messages
            MessagingCenter.Subscribe<PurchaseOrderDetailViewModel>(this, "NavigateBack", async (sender) =>
            {
                await Navigation.PopAsync();
            });
            
            MessagingCenter.Subscribe<PurchaseOrderDetailViewModel>(this, "PurchaseOrderSaved", async (sender, orderId) =>
            {
                // Navigate back after saving
                await Navigation.PopAsync();
            });
            
            MessagingCenter.Subscribe<PurchaseOrderDetailViewModel>(this, "PurchaseOrderSubmitted", async (sender, orderId) =>
            {
                // Navigate back after submitting
                await Navigation.PopAsync();
            });
            
            MessagingCenter.Subscribe<PurchaseOrderDetailViewModel>(this, "PurchaseOrderReceived", async (sender, orderId) =>
            {
                // Navigate back after receiving
                await Navigation.PopAsync();
            });
            
            MessagingCenter.Subscribe<PurchaseOrderDetailViewModel>(this, "ShowLowStockAlerts", (sender, message) =>
            {
                DisplayAlert("Low Stock Alerts", message, "OK");
            });
        }
        
        protected override async void OnNavigatedTo(NavigatedToEventArgs args)
        {
            base.OnNavigatedTo(args);
            
            if (args.Parameter is Guid orderId && orderId != _viewModel.Order?.Id)
            {
                _orderId = orderId;
                await _viewModel.LoadDataAsync(orderId);
            }
        }
        
        private async void DisplayAlert(string title, string message, string cancel)
        {
            await DisplayAlert(title, message, cancel);
        }
    }
}
