using System;
using System.Collections.Generic;

namespace CakeBistro.Models
{
    public class AccountStatement
    {
        public int AccountId { get; set; }
        public string? AccountName { get; set; }
        public List<Transaction> Transactions { get; set; } = new();
    }

    public class Transaction
    {
        public DateTime Date { get; set; }
        public string? Description { get; set; }
        public decimal Amount { get; set; }
        public decimal RunningBalance { get; set; }
    }
}
