using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using CakeBistro.Core.Models;
using CakeBistro.Repositories;

namespace MCakeBistro.Repositories
{
    // Implementation of raw material stock statement repository with inventory-specific data operations
    public class RawMaterialStockStatementRepository : RepositoryBase<RawMaterialStockStatement>, IRawMaterialStockStatementRepository
    {
        private readonly InventoryContext _context;
        
        public RawMaterialStockStatementRepository(InventoryContext context)
        {
            _context = context;
        }
        
        public override async Task<IEnumerable<RawMaterialStockStatement>> GetAllAsync()
        {
            return await _context.RawMaterialStockStatements.ToListAsync();
        }
        
        public override async Task<RawMaterialStockStatement> GetByIdAsync(Guid id)
        {
            return await _context.RawMaterialStockStatements.FindAsync(id);
        }
        
        public override async Task AddAsync(RawMaterialStockStatement statement)
        {
            await _context.RawMaterialStockStatements.AddAsync(statement);
            await _context.SaveChangesAsync();
        }
        
        public override async Task UpdateAsync(RawMaterialStockStatement statement)
        {
            _context.RawMaterialStockStatements.Update(statement);
            await _context.SaveChangesAsync();
        }
        
        public override async Task DeleteAsync(Guid id)
        {
            var statement = await _context.RawMaterialStockStatements.FindAsync(id);
            if (statement != null)
            {
                _context.RawMaterialStockStatements.Remove(statement);
                await _context.SaveChangesAsync();
            }
        }
        
        public async Task<IEnumerable<RawMaterialStockStatement>> GetStatementsByMaterialAsync(Guid materialId)
        {
            return await _context.RawMaterialStockStatements
                .Where(s => s.RawMaterialId == materialId)
                .ToListAsync();
        }
        
        public async Task<IEnumerable<RawMaterialStockStatement>> GetStatementsByPeriodAsync(DateTime startDate, DateTime endDate)
        {
            return await _context.RawMaterialStockStatements
                .Where(s => s.StatementDate >= startDate && s.StatementDate <= endDate)
                .ToListAsync();
        }
        
        public async Task<RawMaterialStockStatement> GetLatestStatementAsync(Guid materialId)
        {
            return await _context.RawMaterialStockStatements
                .Where(s => s.RawMaterialId == materialId)
                .OrderByDescending(s => s.StatementDate)
                .FirstOrDefaultAsync();
        }
    }
}
