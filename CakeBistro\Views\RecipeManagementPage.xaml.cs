using CakeBistro.ViewModels;
using Microsoft.Maui.Controls;
using System;

namespace CakeBistro.Views
{
    public partial class RecipeManagementPage : ContentPage
    {
        public RecipeManagementPage(RecipeManagementViewModel vm)
        {
            InitializeComponent();
            BindingContext = vm;
        }

        private void OnAddIngredientClicked(object sender, EventArgs e)
        {
            // TODO: Add ingredient logic and animated feedback
            DisplayAlert("Info", "Ingredient added!", "OK");
        }

        private void OnSaveRecipeClicked(object sender, EventArgs e)
        {
            // TODO: Add save recipe logic and animated feedback
            DisplayAlert("Success", "Recipe saved!", "OK");
        }
    }
}
