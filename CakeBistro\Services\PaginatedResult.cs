using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;

namespace CakeBistro.Services
{
    // ... existing classes ...
    
    /// <summary>
    /// Represents a paginated result of a query.
    /// </summary>
    /// <typeparam name="T">The type of the items in the result.</typeparam>
    public class PaginatedResult<T>
    {
        /// <summary>
        /// Gets or sets the items in the current page.
        /// </summary>
        public List<T> Items { get; set; }
        
        /// <summary>
        /// Gets or sets the total number of items across all pages.
        /// </summary>
        public int TotalCount { get; set; }
        
        /// <summary>
        /// Gets or sets the current page number.
        /// </summary>
        public int Page { get; set; }
        
        /// <summary>
        /// Gets or sets the number of items per page.
        /// </summary>
        public int PageSize { get; set; }
        
        /// <summary>
        /// Gets the total number of pages.
        /// </summary>
        public int TotalPages => (int)Math.Ceiling(TotalCount / (double)PageSize);
        
        /// <summary>
        /// Gets whether there is a previous page.
        /// </summary>
        public bool HasPreviousPage => Page > 1;
        
        /// <summary>
        /// Gets whether there is a next page.
        /// </summary>
        public bool HasNextPage => Page < TotalPages;
    }
}