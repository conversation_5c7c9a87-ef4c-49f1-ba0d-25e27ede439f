using Microsoft.EntityFrameworkCore;
using System.Linq.Expressions;
using CakeBistro.Core.Models;
using CakeBistro.Core.Interfaces;

namespace CakeBistro.Repositories
{
    public class SupplierRepository : BaseRepository<CakeBistro.Core.Models.Supplier>, ISupplierRepository
    {
        private readonly InventoryContext _context;
        
        public SupplierRepository(InventoryContext context)
        {
            _context = context;
        }
        
        public async Task<CakeBistro.Core.Models.Supplier> GetByAccountCodeAsync(string accountCode)
        {
            return await _context.Suppliers
                .FirstOrDefaultAsync(s => s.AccountCode == accountCode);
        }

        public async Task<IEnumerable<CakeBistro.Core.Models.Supplier>> GetByProductAsync(int productId)
        {
            return await _context.Suppliers
                .Where(s => s.RawMaterials.Any(r => r.ProductId == productId))
                .ToListAsync();
        }
        
        public override async Task DeleteAsync(Guid id)
        {
            var CakeBistro.Core.Models.Supplier = await _context.Suppliers.FindAsync(id);
            if (CakeBistro.Core.Models.Supplier != null)
            {
                _context.Suppliers.Remove(CakeBistro.Core.Models.Supplier);
                await _context.SaveChangesAsync();
            }
        }
        
        public async Task<IEnumerable<CakeBistro.Core.Models.Supplier>> GetSuppliersByCategoryAsync(string category)
        {
            if (string.IsNullOrWhiteSpace(category))
            {
                return await _context.Suppliers.ToListAsync();
            }
            
            return await _context.Suppliers
                .Where(s => s.Category.Contains(category))
                .ToListAsync();
        }
        
        public async Task<IEnumerable<CakeBistro.Core.Models.Supplier>> SearchSuppliersAsync(string searchTerm)
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
            {
                return await _context.Suppliers.ToListAsync();
            }
            
            return await _context.Suppliers
                .Where(s => s.Name.Contains(searchTerm) ||
                            s.ContactName != null && s.ContactName.Contains(searchTerm) ||
                            s.Email != null && s.Email.Contains(searchTerm) ||
                            s.Phone != null && s.Phone.Contains(searchTerm))
                .ToListAsync();
        }
        
        public async Task<IEnumerable<CakeBistro.Core.Models.Supplier>> GetActiveSuppliersAsync()
        {
            return await _context.Suppliers
                .Where(s => s.IsActive)
                .ToListAsync();
        }
        
        public async Task<IEnumerable<CakeBistro.Core.Models.Supplier>> GetSuppliersWithPendingOrdersAsync()
        {
            // In a real implementation, this would query based on order status
            return await _context.Suppliers
                .Where(s => s.HasPendingOrders)
                .ToListAsync();
        }
    }
}
