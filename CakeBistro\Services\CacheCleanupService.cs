using CakeBistro.Services;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;

namespace CakeBistro.Services
{
    public class CacheCleanupService : IHostedService, IDisposable
    {
        private readonly ILogger<CacheCleanupService> _logger;
        private readonly ICacheService _cacheService;
        private readonly Timer _timer;
        private readonly TimeSpan _cleanupInterval;
        private readonly TimeSpan _staleThreshold;

        public CacheCleanupService(
            ILogger<CacheCleanupService> logger,
            ICacheService cacheService)
        {
            _logger = logger;
            _cacheService = cacheService;
            
            // Get configuration values or use defaults
            _cleanupInterval = TimeSpan.FromHours(1); // Run cleanup every hour
            _staleThreshold = TimeSpan.FromDays(7);  // Remove items not accessed in 7 days
            
            // Create timer with interval based on configuration
            _timer = new Timer(DoCleanup, null, TimeSpan.Zero, _cleanupInterval);
        }

        private void DoCleanup(object state)
        {
            try
            {
                _logger.LogInformation("Cache cleanup started at {Time}", DateTime.UtcNow);
                
                // For memory cache, we can't directly enumerate all keys
                // This would require a custom cache implementation
                // For Redis, we could use SCAN command to find and remove stale entries
                
                // Log cleanup statistics
                _logger.LogInformation("Cache cleanup completed");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during cache cleanup: {Message}", ex.Message);
            }
        }

        public Task StartAsync(CancellationToken cancellationToken)
        {
            _logger.LogInformation("Cache Cleanup Service is starting.");
            return Task.CompletedTask;
        }

        public Task StopAsync(CancellationToken cancellationToken)
        {
            _logger.LogInformation("Cache Cleanup Service is stopping.");
            _timer?.Change(Timeout.Infinite, 0);
            return Task.CompletedTask;
        }

        public void Dispose()
        {
            _timer?.Dispose();
        }
    }
}