using System;
using CakeBistro.ViewModels;
using Microsoft.Maui.Controls;

namespace CakeBistro.Views
{
    [QueryProperty(nameof(ProductId), "productId")]
    public partial class ProductionFormPage : ContentPage
    {
        private readonly ProductionFormViewModel _viewModel;

        public ProductionFormPage(ProductionFormViewModel viewModel)
        {
            InitializeComponent();
            _viewModel = viewModel;
            BindingContext = _viewModel;
        }

        public string ProductId
        {
            set
            {
                if (int.TryParse(value, out var id))
                {
                    _viewModel.LoadProductAsync(id);
                }
            }
        }
    }
}
