using Microsoft.Maui.Controls;
using CakeBistro.ViewModels;

namespace CakeBistro.Views
{
    public partial class GoodsReceiptDetailPage : ContentPage
    {
        private readonly IInventoryService _inventoryService;
        private Guid _receiptId;
        
        public GoodsReceiptDetailViewModel ViewModel { get; }
        
        public GoodsReceiptDetailPage(IInventoryService inventoryService)
            : base()
        {
            _inventoryService = inventoryService;
            ViewModel = new GoodsReceiptDetailViewModel(inventoryService);
            BindingContext = ViewModel;
            
            // Subscribe to messages
            MessagingCenter.Subscribe<GoodsReceiptDetailViewModel>(this, "NavigateBack", (sender) =>
            {
                Navigation.PopAsync();
            });
            
            MessagingCenter.Subscribe<GoodsReceiptDetailViewModel>(this, "GoodsReceiptSaved", async (sender, receiptId) =>
            {
                await Navigation.PopAsync();
            });
            
            MessagingCenter.Subscribe<GoodsReceiptDetailViewModel>(this, "GoodsReceiptSubmitted", async (sender, receiptId) =>
            {
                await Navigation.PopAsync();
            });
            
            MessagingCenter.Subscribe<GoodsReceiptDetailViewModel>(this, "GoodsReceiptDiscarded", async (sender, receiptId) =>
            {
                await Navigation.PopAsync();
            });
        }
        
        protected override async void OnNavigatedTo(NavigatedToEventArgs args)
        {
            base.OnNavigatedTo(args);
            
            if (args.Parameter is Guid receiptId && receiptId != ViewModel.Receipt?.Id)
            {
                _receiptId = receiptId;
                await ViewModel.LoadDataAsync(receiptId);
            }
        }
        
        private async void DisplayAlert(string title, string message, string cancel)
        {
            await DisplayAlert(title, message, cancel);
        }
    }
}
