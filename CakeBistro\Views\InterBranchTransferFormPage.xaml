<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:views="clr-namespace:CakeBistro.Views"
             x:Class="CakeBistro.Views.InterBranchTransferFormPage"
             Title="Inter-branch Transfer">
    <ScrollView>
        <StackLayout Spacing="16" Padding="16">
            <views:FormTemplate>
                <views:InterBranchTransferFormFields />
            </views:FormTemplate>
        </StackLayout>
    </ScrollView>
</ContentPage>
