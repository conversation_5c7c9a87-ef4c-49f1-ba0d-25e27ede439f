<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:vm="clr-namespace:CakeBistro.ViewModels"
             xmlns:model="clr-namespace:CakeBistro.Models"
             x:Class="CakeBistro.Views.ProductionBatchPage"
             x:DataType="vm:ProductionBatchViewModel"
             Title="{Binding Title}">

    <ContentPage.Resources>
        <ResourceDictionary>
            <converters:StringNotNullOrEmptyConverter x:Key="StringNotNullOrEmptyConverter" />
        </ResourceDictionary>
    </ContentPage.Resources>

    <Grid RowDefinitions="Auto,*">
        <!-- Header with Add Button -->
        <HorizontalStackLayout Grid.Row="0" 
                             Padding="10"
                             Spacing="10">
            <Button Text="New Production Batch"
                    Command="{Binding AddProductionBatchCommand}"
                    SemanticProperties.Hint="Create a new production batch"/>
            <ActivityIndicator IsRunning="{Binding IsBusy}"/>
        </HorizontalStackLayout>

        <!-- Production Batches List -->
        <RefreshView Grid.Row="1"
                     Command="{Binding LoadProductionBatchesCommand}"
                     IsRefreshing="{Binding IsBusy}">
            <CollectionView ItemsSource="{Binding ProductionBatches}"
                          SelectedItem="{Binding SelectedBatch}"
                          SelectionMode="Single">
                <CollectionView.ItemTemplate>
                    <DataTemplate x:DataType="model:ProductionBatch">
                        <SwipeView>
                            <SwipeView.RightItems>
                                <SwipeItems>
                                    <SwipeItem Text="Delete"
                                             BackgroundColor="Red"
                                             Command="{Binding Source={RelativeSource AncestorType={x:Type vm:ProductionBatchViewModel}}, Path=DeleteProductionBatchCommand}"
                                             CommandParameter="{Binding .}"/>
                                </SwipeItems>
                            </SwipeView.RightItems>

                            <Grid Padding="10">
                                <Frame>
                                    <Grid RowDefinitions="Auto,Auto,Auto,Auto"
                                          ColumnDefinitions="*,Auto">
                                        
                                        <!-- Recipe Name -->
                                        <Label Grid.Row="0"
                                               Grid.Column="0"
                                               Text="{Binding Recipe.Name}"
                                               FontSize="16"
                                               FontAttributes="Bold"/>
                                        
                                        <!-- Quality Check Button -->
                                        <Button Grid.Row="0"
                                                Grid.Column="1"
                                                Text="Quality Check"
                                                Command="{Binding Source={RelativeSource AncestorType={x:Type vm:ProductionBatchViewModel}}, Path=AddQualityCheckCommand}"
                                                CommandParameter="{Binding .}"
                                                BackgroundColor="{StaticResource Primary}"
                                                TextColor="White"
                                                Margin="0,0,10,0"/>

                                        <!-- Status -->
                                        <Label Grid.Row="0"
                                               Grid.Column="1"
                                               Text="{Binding Status}"
                                               FontSize="14"
                                               TextColor="{Binding StatusColor}"/>

                                        <!-- Planned Quantity -->
                                        <Label Grid.Row="1"
                                               Grid.Column="0"
                                               Grid.ColumnSpan="2"
                                               Text="{Binding PlannedQuantity, StringFormat='Planned: {0} units'}"
                                               FontSize="14"/>

                                        <!-- Actual Quantity -->
                                        <Label Grid.Row="2"
                                               Grid.Column="0"
                                               Grid.ColumnSpan="2"
                                               Text="{Binding ActualQuantity, StringFormat='Actual: {0} units'}"
                                               FontSize="14"
                                               IsVisible="{Binding ActualQuantity, Converter={StaticResource GreaterThanZeroConverter}}"/>

                                        <!-- Action Buttons -->
                                        <HorizontalStackLayout Grid.Row="3"
                                                             Grid.Column="0"
                                                             Grid.ColumnSpan="2"
                                                             Spacing="10">
                                            <Button Text="Edit"
                                                    Command="{Binding Source={RelativeSource AncestorType={x:Type vm:ProductionBatchViewModel}}, Path=EditProductionBatchCommand}"
                                                    CommandParameter="{Binding .}"
                                                    IsEnabled="{Binding Status, Converter={StaticResource EqualityConverter}, ConverterParameter='Planned'}"/>
                                            
                                            <Button Text="Start"
                                                    Command="{Binding Source={RelativeSource AncestorType={x:Type vm:ProductionBatchViewModel}}, Path=StartBatchCommand}"
                                                    CommandParameter="{Binding .}"
                                                    IsEnabled="{Binding Status, Converter={StaticResource EqualityConverter}, ConverterParameter='Planned'}"/>

                                            <Button Text="Complete"
                                                    Command="{Binding Source={RelativeSource AncestorType={x:Type vm:ProductionBatchViewModel}}, Path=CompleteBatchCommand}"
                                                    CommandParameter="{Binding .}"
                                                    IsEnabled="{Binding Status, Converter={StaticResource EqualityConverter}, ConverterParameter='InProgress'}"/>

                                            <Button Text="Cancel"
                                                    Command="{Binding Source={RelativeSource AncestorType={x:Type vm:ProductionBatchViewModel}}, Path=CancelBatchCommand}"
                                                    CommandParameter="{Binding .}"
                                                    IsEnabled="{Binding Status, Converter={StaticResource OrConverter}, ConverterParameter='Planned,InProgress'}"
                                                    BackgroundColor="Red"/>
                                        </HorizontalStackLayout>
                                    </Grid>
                                </Frame>
                            </Grid>
                        </SwipeView>
                    </DataTemplate>
                </CollectionView.ItemTemplate>
            </CollectionView>
        </RefreshView>

        <!-- Add a section to display InventoryBatch details from the new ViewModel -->
        <Label Text="Production Batches (Sample Integration)" FontSize="18" FontAttributes="Bold" Margin="0,10,0,0" />
        <CollectionView ItemsSource="{Binding Batches}">
            <CollectionView.ItemTemplate>
                <DataTemplate>
                    <Frame BorderColor="Gray" Margin="5">
                        <StackLayout>
                            <Label Text="{Binding BatchNumber}" FontAttributes="Bold" />
                            <Label Text="Status: {Binding Status}" />
                            <Label Text="Expiry: {Binding ExpiryDate, StringFormat='{}{0:yyyy-MM-dd}'}" />
                            <Label Text="Trace: {Binding TraceabilityCode}" />
                        </StackLayout>
                    </Frame>
                </DataTemplate>
            </CollectionView.ItemTemplate>
        </CollectionView>
        <!-- End sample integration -->

        <!-- Interactive buttons for batch operations -->
        <VerticalStackLayout Padding="10" Spacing="10">
            <Button Text="Create Sample Batch" Command="{Binding CreateBatchCommand}" Margin="0,10,0,0" />
            <Button Text="Load Expiring Batches (7 days)" Command="{Binding LoadExpiringBatchesCommand}" Margin="0,5,0,10" />
        </VerticalStackLayout>

        <!-- Simple input form for batch creation -->
        <StackLayout Margin="0,10,0,0" Spacing="5">
            <Entry Placeholder="Raw Material ID" Keyboard="Numeric" Text="{Binding InputRawMaterialId}" />
            <Entry Placeholder="Batch Number" Text="{Binding InputBatchNumber}" />
            <Entry Placeholder="Production Date (yyyy-MM-dd)" Text="{Binding InputProductionDate, StringFormat='{}{0:yyyy-MM-dd}'}" />
            <Entry Placeholder="Expiry Date (yyyy-MM-dd)" Text="{Binding InputExpiryDate, StringFormat='{}{0:yyyy-MM-dd}'}" />
            <Entry Placeholder="Quantity" Keyboard="Numeric" Text="{Binding InputQuantity}" />
            <Entry Placeholder="Cost" Keyboard="Numeric" Text="{Binding InputCost}" />
            <Entry Placeholder="Storage Location" Text="{Binding InputStorageLocation}" />
            <Entry Placeholder="Traceability Code" Text="{Binding InputTraceabilityCode}" />
            <Button Text="Create Batch" Command="{Binding ShowCreateBatchDialogAsync}" />
        </StackLayout>

        <!-- Validation Error Message -->
        <Label Text="{Binding ValidationError}" TextColor="Red" FontAttributes="Bold" IsVisible="{Binding ValidationError, Converter={StaticResource StringNotNullOrEmptyConverter}}" />
        
        <!-- Success Message -->
        <Label Text="{Binding SuccessMessage}" TextColor="Green" FontAttributes="Bold" IsVisible="{Binding SuccessMessage, Converter={StaticResource StringNotNullOrEmptyConverter}}" />
    </Grid>
</ContentPage>
