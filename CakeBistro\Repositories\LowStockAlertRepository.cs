using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using CakeBistro.Core.Models;
using CakeBistro.Repositories;

namespace CakeBistro.Repositories
{
    // Implementation of low stock alert repository with inventory-specific data operations
    public class LowStockAlertRepository : RepositoryBase<LowStockAlert>, ILowStockAlertRepository
    {
        private readonly InventoryContext _context;
        
        public LowStockAlertRepository(InventoryContext context)
        {
            _context = context;
        }
        
        public override async Task<IEnumerable<LowStockAlert>> GetAllAsync()
        {
            return await _context.LowStockAlerts.ToListAsync();
        }
        
        public override async Task<LowStockAlert> GetByIdAsync(Guid id)
        {
            return await _context.LowStockAlerts.FindAsync(id);
        }
        
        public override async Task AddAsync(LowStockAlert alert)
        {
            await _context.LowStockAlerts.AddAsync(alert);
            await _context.SaveChangesAsync();
        }
        
        public override async Task UpdateAsync(LowStockAlert alert)
        {
            _context.LowStockAlerts.Update(alert);
            await _context.SaveChangesAsync();
        }
        
        public override async Task DeleteAsync(Guid id)
        {
            var alert = await _context.LowStockAlerts.FindAsync(id);
            if (alert != null)
            {
                _context.LowStockAlerts.Remove(alert);
                await _context.SaveChangesAsync();
            }
        }
        
        public async Task<IEnumerable<LowStockAlert>> GetActiveAlertsAsync()
        {
            return await _context.LowStockAlerts
                .Where(a => a.Status == AlertStatus.Active)
                .ToListAsync();
        }
        
        public async Task<IEnumerable<LowStockAlert>> GetAlertsByMaterialAsync(Guid materialId)
        {
            return await _context.LowStockAlerts
                .Where(a => a.RawMaterialId == materialId)
                .ToListAsync();
        }
        
        public async Task<IEnumerable<LowStockAlert>> GetUrgentAlertsAsync()
        {
            return await _context.LowStockAlerts
                .Where(a => a.Priority == AlertPriority.High && a.Status == AlertStatus.Active)
                .ToListAsync();
        }
    }
}
